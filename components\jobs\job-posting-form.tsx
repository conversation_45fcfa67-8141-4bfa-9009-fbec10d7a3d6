'use client'

import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useJobsStore, useAuthStore, type Job } from '@/stores'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { ButtonLoading } from '@/components/ui/button-loading'
import { ErrorAlert, InlineError } from '@/components/ui/error-alert'
import { Badge } from '@/components/ui/badge'
import { 
  Briefcase, 
  MapPin, 
  DollarSign,
  Calendar,
  Users,
  Award,
  X,
  Plus
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface JobPostingFormProps {
  job?: Job
  onSuccess?: (job: Job) => void
  onCancel?: () => void
  mode?: 'create' | 'edit'
}

interface JobFormData {
  title: string
  description: string
  type: string
  location: {
    city: string
    state: string
    country: string
    remote: boolean
  }
  salary: {
    min: string
    max: string
    currency: string
    period: string
    showSalary: boolean
  }
  requirements: {
    experience: string
    education: string
    skills: string[]
  }
  benefits: string[]
  applicationDeadline: string
  customQuestions: Array<{
    question: string
    type: 'text' | 'textarea' | 'select'
    options?: string[]
    required: boolean
  }>
}

interface FormErrors {
  title?: string
  description?: string
  type?: string
  location?: string
  experience?: string
  education?: string
  skills?: string
  general?: string
}

const JOB_TYPES = [
  { value: 'full-time', label: 'Full-time' },
  { value: 'part-time', label: 'Part-time' },
  { value: 'contract', label: 'Contract' },
  { value: 'internship', label: 'Internship' }
]

const EXPERIENCE_LEVELS = [
  { value: 'entry', label: 'Entry Level (0-2 years)' },
  { value: 'mid', label: 'Mid Level (3-5 years)' },
  { value: 'senior', label: 'Senior Level (6-10 years)' },
  { value: 'lead', label: 'Lead/Principal (10+ years)' },
  { value: 'executive', label: 'Executive' }
]

const EDUCATION_LEVELS = [
  { value: 'high-school', label: 'High School' },
  { value: 'associate', label: 'Associate Degree' },
  { value: 'bachelor', label: 'Bachelor\'s Degree' },
  { value: 'master', label: 'Master\'s Degree' },
  { value: 'phd', label: 'PhD' },
  { value: 'none', label: 'No formal education required' }
]

const SALARY_PERIODS = [
  { value: 'hourly', label: 'Per Hour' },
  { value: 'monthly', label: 'Per Month' },
  { value: 'yearly', label: 'Per Year' }
]

export function JobPostingForm({ 
  job, 
  onSuccess, 
  onCancel, 
  mode = 'create' 
}: JobPostingFormProps) {
  const router = useRouter()
  const { user } = useAuthStore()
  const { createJob, updateJob, createLoading, updateLoading, error, clearError } = useJobsStore()
  
  const [formData, setFormData] = useState<JobFormData>({
    title: job?.title || '',
    description: job?.description || '',
    type: job?.type || '',
    location: {
      city: job?.location?.city || '',
      state: job?.location?.state || '',
      country: job?.location?.country || 'United States',
      remote: job?.location?.remote || false
    },
    salary: {
      min: job?.salary?.min?.toString() || '',
      max: job?.salary?.max?.toString() || '',
      currency: job?.salary?.currency || 'USD',
      period: job?.salary?.period || 'yearly',
      showSalary: !!(job?.salary?.min || job?.salary?.max)
    },
    requirements: {
      experience: job?.requirements?.experience || '',
      education: job?.requirements?.education || '',
      skills: job?.requirements?.skills || []
    },
    benefits: job?.benefits || [],
    applicationDeadline: job?.applicationDeadline 
      ? new Date(job.applicationDeadline).toISOString().split('T')[0] 
      : '',
    customQuestions: []
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [newSkill, setNewSkill] = useState('')
  const [newBenefit, setNewBenefit] = useState('')

  const isLoading = createLoading || updateLoading

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Job title is required'
    } else if (formData.title.length < 3) {
      newErrors.title = 'Job title must be at least 3 characters'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Job description is required'
    } else if (formData.description.length < 100) {
      newErrors.description = 'Description must be at least 100 characters'
    }

    if (!formData.type) {
      newErrors.type = 'Job type is required'
    }

    if (!formData.location.remote && (!formData.location.city.trim() || !formData.location.state.trim())) {
      newErrors.location = 'City and state are required for non-remote positions'
    }

    if (!formData.requirements.experience) {
      newErrors.experience = 'Experience level is required'
    }

    if (!formData.requirements.education) {
      newErrors.education = 'Education requirement is required'
    }

    if (formData.requirements.skills.length === 0) {
      newErrors.skills = 'At least one skill is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()

    if (!validateForm()) {
      return
    }

    try {
      const jobData = {
        ...formData,
        salary: formData.salary.showSalary ? {
          min: formData.salary.min ? parseInt(formData.salary.min) : undefined,
          max: formData.salary.max ? parseInt(formData.salary.max) : undefined,
          currency: formData.salary.currency,
          period: formData.salary.period
        } : undefined,
        applicationDeadline: formData.applicationDeadline 
          ? new Date(formData.applicationDeadline) 
          : undefined
      }

      let result: Job
      if (mode === 'edit' && job) {
        result = await updateJob(job._id, jobData)
      } else {
        result = await createJob(jobData)
      }

      if (onSuccess) {
        onSuccess(result)
      } else {
        router.push(`/jobs/${result._id}`)
      }
    } catch (error) {
      console.error('Job form error:', error)
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof JobFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }))
    
    // Clear field error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Handle location changes
  const handleLocationChange = (field: keyof JobFormData['location']) => (
    e: React.ChangeEvent<HTMLInputElement> | boolean
  ) => {
    const value = typeof e === 'boolean' ? e : e.target.value
    setFormData(prev => ({
      ...prev,
      location: { ...prev.location, [field]: value }
    }))
    
    if (errors.location) {
      setErrors(prev => ({ ...prev, location: undefined }))
    }
  }

  // Handle salary changes
  const handleSalaryChange = (field: keyof JobFormData['salary']) => (
    e: React.ChangeEvent<HTMLInputElement> | boolean
  ) => {
    const value = typeof e === 'boolean' ? e : e.target.value
    setFormData(prev => ({
      ...prev,
      salary: { ...prev.salary, [field]: value }
    }))
  }

  // Handle requirements changes
  const handleRequirementsChange = (field: keyof JobFormData['requirements']) => (
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      requirements: { ...prev.requirements, [field]: value }
    }))
    
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Handle skill addition
  const addSkill = () => {
    if (newSkill.trim() && !formData.requirements.skills.includes(newSkill.trim())) {
      setFormData(prev => ({
        ...prev,
        requirements: {
          ...prev.requirements,
          skills: [...prev.requirements.skills, newSkill.trim()]
        }
      }))
      setNewSkill('')
      
      if (errors.skills) {
        setErrors(prev => ({ ...prev, skills: undefined }))
      }
    }
  }

  // Handle skill removal
  const removeSkill = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      requirements: {
        ...prev.requirements,
        skills: prev.requirements.skills.filter(s => s !== skill)
      }
    }))
  }

  // Handle benefit addition
  const addBenefit = () => {
    if (newBenefit.trim() && !formData.benefits.includes(newBenefit.trim())) {
      setFormData(prev => ({
        ...prev,
        benefits: [...prev.benefits, newBenefit.trim()]
      }))
      setNewBenefit('')
    }
  }

  // Handle benefit removal
  const removeBenefit = (benefit: string) => {
    setFormData(prev => ({
      ...prev,
      benefits: prev.benefits.filter(b => b !== benefit)
    }))
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">
            {mode === 'edit' ? 'Edit Job Posting' : 'Create Job Posting'}
          </CardTitle>
          <CardDescription>
            {mode === 'edit' 
              ? 'Update your job posting to attract the right candidates'
              : 'Create a compelling job posting to attract top talent'
            }
          </CardDescription>
        </CardHeader>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* General Error */}
        {error && (
          <ErrorAlert
            type="error"
            message={error.message || 'Failed to save job posting. Please try again.'}
            dismissible
            onDismiss={clearError}
          />
        )}

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Briefcase className="w-5 h-5" />
              <span>Job Details</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Job Title *</Label>
              <Input
                id="title"
                placeholder="e.g., Senior Software Engineer"
                value={formData.title}
                onChange={handleInputChange('title')}
                className={cn(errors.title && 'border-red-500')}
                disabled={isLoading}
              />
              <InlineError message={errors.title} />
            </div>

            <div>
              <Label htmlFor="type">Job Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value) => {
                  setFormData(prev => ({ ...prev, type: value }))
                  if (errors.type) {
                    setErrors(prev => ({ ...prev, type: undefined }))
                  }
                }}
                disabled={isLoading}
              >
                <SelectTrigger className={cn(errors.type && 'border-red-500')}>
                  <SelectValue placeholder="Select job type" />
                </SelectTrigger>
                <SelectContent>
                  {JOB_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <InlineError message={errors.type} />
            </div>

            <div>
              <Label htmlFor="description">Job Description *</Label>
              <Textarea
                id="description"
                placeholder="Describe the role, responsibilities, and what makes this opportunity exciting..."
                value={formData.description}
                onChange={handleInputChange('description')}
                className={cn('min-h-40 resize-none', errors.description && 'border-red-500')}
                disabled={isLoading}
              />
              <div className="flex justify-between items-center mt-1">
                <InlineError message={errors.description} />
                <span className="text-xs text-muted-foreground">
                  {formData.description.length}/5000 characters
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Location */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="w-5 h-5" />
              <span>Location</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remote"
                checked={formData.location.remote}
                onCheckedChange={(checked) => handleLocationChange('remote')(!!checked)}
                disabled={isLoading}
              />
              <Label htmlFor="remote">This is a remote position</Label>
            </div>

            {!formData.location.remote && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="city">City *</Label>
                  <Input
                    id="city"
                    placeholder="San Francisco"
                    value={formData.location.city}
                    onChange={handleLocationChange('city')}
                    className={cn(errors.location && 'border-red-500')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label htmlFor="state">State *</Label>
                  <Input
                    id="state"
                    placeholder="CA"
                    value={formData.location.state}
                    onChange={handleLocationChange('state')}
                    className={cn(errors.location && 'border-red-500')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.location.country}
                    onChange={handleLocationChange('country')}
                    disabled={isLoading}
                  />
                </div>
              </div>
            )}
            <InlineError message={errors.location} />
          </CardContent>
        </Card>

        {/* Salary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="w-5 h-5" />
              <span>Compensation</span>
            </CardTitle>
            <CardDescription>
              Salary information helps attract qualified candidates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="showSalary"
                checked={formData.salary.showSalary}
                onCheckedChange={(checked) => handleSalaryChange('showSalary')(!!checked)}
                disabled={isLoading}
              />
              <Label htmlFor="showSalary">Include salary information</Label>
            </div>

            {formData.salary.showSalary && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="salaryMin">Minimum Salary</Label>
                  <Input
                    id="salaryMin"
                    type="number"
                    placeholder="50000"
                    value={formData.salary.min}
                    onChange={handleSalaryChange('min')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label htmlFor="salaryMax">Maximum Salary</Label>
                  <Input
                    id="salaryMax"
                    type="number"
                    placeholder="80000"
                    value={formData.salary.max}
                    onChange={handleSalaryChange('max')}
                    disabled={isLoading}
                  />
                </div>

                <div>
                  <Label htmlFor="currency">Currency</Label>
                  <Select
                    value={formData.salary.currency}
                    onValueChange={(value) => handleSalaryChange('currency')(value)}
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="CAD">CAD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="period">Period</Label>
                  <Select
                    value={formData.salary.period}
                    onValueChange={(value) => handleSalaryChange('period')(value)}
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {SALARY_PERIODS.map((period) => (
                        <SelectItem key={period.value} value={period.value}>
                          {period.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span>Requirements</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="experience">Experience Level *</Label>
                <Select
                  value={formData.requirements.experience}
                  onValueChange={handleRequirementsChange('experience')}
                  disabled={isLoading}
                >
                  <SelectTrigger className={cn(errors.experience && 'border-red-500')}>
                    <SelectValue placeholder="Select experience level" />
                  </SelectTrigger>
                  <SelectContent>
                    {EXPERIENCE_LEVELS.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InlineError message={errors.experience} />
              </div>

              <div>
                <Label htmlFor="education">Education *</Label>
                <Select
                  value={formData.requirements.education}
                  onValueChange={handleRequirementsChange('education')}
                  disabled={isLoading}
                >
                  <SelectTrigger className={cn(errors.education && 'border-red-500')}>
                    <SelectValue placeholder="Select education requirement" />
                  </SelectTrigger>
                  <SelectContent>
                    {EDUCATION_LEVELS.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InlineError message={errors.education} />
              </div>
            </div>

            {/* Skills */}
            <div>
              <Label>Required Skills *</Label>
              <div className="space-y-2">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Add a required skill (e.g., JavaScript, React)"
                    value={newSkill}
                    onChange={(e) => setNewSkill(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addSkill}
                    disabled={!newSkill.trim() || isLoading}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                {formData.requirements.skills.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.requirements.skills.map((skill, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center space-x-1"
                      >
                        <span>{skill}</span>
                        <button
                          type="button"
                          onClick={() => removeSkill(skill)}
                          className="ml-1 hover:text-red-600"
                          disabled={isLoading}
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
                <InlineError message={errors.skills} />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Benefits */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="w-5 h-5" />
              <span>Benefits & Perks</span>
            </CardTitle>
            <CardDescription>
              Highlight what makes your company a great place to work
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Input
                  placeholder="Add a benefit (e.g., Health Insurance, Remote Work)"
                  value={newBenefit}
                  onChange={(e) => setNewBenefit(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addBenefit())}
                  disabled={isLoading}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={addBenefit}
                  disabled={!newBenefit.trim() || isLoading}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
              {formData.benefits.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.benefits.map((benefit, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="flex items-center space-x-1"
                    >
                      <span>{benefit}</span>
                      <button
                        type="button"
                        onClick={() => removeBenefit(benefit)}
                        className="ml-1 hover:text-red-600"
                        disabled={isLoading}
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Application Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="w-5 h-5" />
              <span>Application Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="deadline">Application Deadline (Optional)</Label>
              <Input
                id="deadline"
                type="date"
                value={formData.applicationDeadline}
                onChange={handleInputChange('applicationDeadline')}
                min={new Date().toISOString().split('T')[0]}
                disabled={isLoading}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Leave empty for no deadline
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Submit Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4 justify-end">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                  className="sm:w-auto w-full"
                >
                  Cancel
                </Button>
              )}

              <ButtonLoading
                type="submit"
                loading={isLoading}
                loadingText={mode === 'edit' ? 'Updating...' : 'Creating...'}
                disabled={isLoading}
                className="sm:w-auto w-full"
                size="lg"
              >
                {mode === 'edit' ? 'Update Job Posting' : 'Create Job Posting'}
              </ButtonLoading>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  )
}
