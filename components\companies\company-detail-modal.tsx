'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Company } from '@/lib/company-data'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  MapPin, 
  Star, 
  Users, 
  Calendar, 
  Heart, 
  MessageCircle,
  Building,
  CheckCircle,
  TrendingUp,
  Award,
  Briefcase,
  Globe,
  Mail,
  Phone,
  ExternalLink,
  Share2,
  Flag,
  X,
  ChevronRight,
  Target,
  Zap,
  Shield,
  Coffee,
  Gamepad2,
  GraduationCap,
  HeartHandshake,
  Plane,
  DollarSign
} from 'lucide-react'

// Company interface is now imported from company-data service

interface CompanyDetailModalProps {
  company: Company | null
  isOpen: boolean
  onClose: () => void
  onFollow: (company: Company) => void
  onViewJobs: (company: Company) => void
}

export function CompanyDetailModal({ 
  company, 
  isOpen, 
  onClose, 
  onFollow, 
  onViewJobs 
}: CompanyDetailModalProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [isFollowing, setIsFollowing] = useState(false)

  if (!company) return null

  const getCompanySizeLabel = (size: string) => {
    switch (size) {
      case 'startup':
        return '1-10 employees'
      case 'small':
        return '11-50 employees'
      case 'medium':
        return '51-200 employees'
      case 'large':
        return '201-1000 employees'
      case 'enterprise':
        return '1000+ employees'
      default:
        return size
    }
  }

  const jobs = company.jobs || []

  const team = company.team || []

  const mockBenefits = [
    { icon: Shield, title: "Health Insurance", description: "Comprehensive medical, dental, and vision coverage" },
    { icon: Coffee, title: "Flexible Work", description: "Remote-first culture with flexible hours" },
    { icon: DollarSign, title: "Stock Options", description: "Equity participation for all employees" },
    { icon: Plane, title: "Unlimited PTO", description: "Take time off when you need it" },
    { icon: GraduationCap, title: "Learning Budget", description: "$2,000 annual professional development budget" },
    { icon: Gamepad2, title: "Team Events", description: "Regular team building and social events" }
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 flex flex-col">
        <div className="flex flex-col h-full max-h-[90vh]">
          {/* Header */}
          <div className="p-6 border-b bg-gradient-to-r from-primary/5 to-primary/10">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="relative">
                  <Avatar className="w-20 h-20 border-2 border-primary/20">
                    <AvatarImage src={company.logo} alt={company.name} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-xl">
                      {company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  {company.verified && (
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                      <CheckCircle className="w-3 h-3 text-white" />
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <h1 className="text-2xl font-bold mb-1">{company.name}</h1>
                  <p className="text-primary font-medium text-lg mb-2">{company.industry}</p>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-3">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>{company.location}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span>{company.rating}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{getCompanySizeLabel(company.size)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>Founded {company.founded}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {company.verified && (
                      <Badge variant="secondary" className="bg-green-500 text-white">
                        Verified Company
                      </Badge>
                    )}
                    <Badge variant="outline">
                      {company.followers.toLocaleString()} followers
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <div className="text-right mr-4">
                  <div className="text-3xl font-bold text-primary">{company.openJobs}</div>
                  <div className="text-sm text-muted-foreground">open jobs</div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsFollowing(!isFollowing)}
                  className={`p-2 ${isFollowing ? 'text-red-500' : 'text-muted-foreground'}`}
                >
                  <Heart className={`w-5 h-5 ${isFollowing ? 'fill-current' : ''}`} />
                </Button>
                <Button variant="ghost" size="sm" className="p-2">
                  <Share2 className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3 mt-4">
              <Button 
                className="button-premium flex-1"
                onClick={() => onViewJobs(company)}
              >
                <Briefcase className="w-4 h-4 mr-2" />
                View Jobs ({company.openJobs})
              </Button>
              <Button 
                variant="outline" 
                className="flex-1"
                onClick={() => onFollow(company)}
              >
                <Heart className="w-4 h-4 mr-2" />
                Follow Company
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  window.open(`/company/${company.id}`, '_blank')
                }}
                className="button-enhanced"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                View Full Profile
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto min-h-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full h-full flex flex-col">
              <TabsList className="grid w-full grid-cols-5 sticky top-0 bg-background z-10 shrink-0">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="jobs">Jobs</TabsTrigger>
                <TabsTrigger value="culture">Culture</TabsTrigger>
                <TabsTrigger value="benefits">Benefits</TabsTrigger>
                <TabsTrigger value="team">Team</TabsTrigger>
              </TabsList>

              <div className="p-6 flex-1 overflow-y-auto">
                <TabsContent value="overview" className="space-y-6">
                  {/* About */}
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>About {company.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground leading-relaxed">{company.description}</p>
                    </CardContent>
                  </Card>

                  {/* Specialties */}
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>Specialties</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {company.specialties.map((specialty) => (
                          <Badge key={specialty} variant="secondary" className="theme-glow">
                            {specialty}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Stats */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{company.openJobs}</div>
                        <div className="text-sm text-muted-foreground">Open Positions</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{company.rating}</div>
                        <div className="text-sm text-muted-foreground">Company Rating</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{company.followers.toLocaleString()}</div>
                        <div className="text-sm text-muted-foreground">Followers</div>
                      </CardContent>
                    </Card>
                    <Card className="card-enhanced text-center">
                      <CardContent className="p-4">
                        <div className="text-2xl font-bold text-primary mb-1">{company.founded}</div>
                        <div className="text-sm text-muted-foreground">Founded</div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="jobs" className="space-y-6">
                  {jobs.map((job) => (
                    <Card key={job.id} className="card-enhanced">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="text-lg font-bold mb-1">{job.title}</h3>
                            <p className="text-primary font-medium">{job.department}</p>
                          </div>
                          <Badge variant="outline">{job.type}</Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                          <div>
                            <span className="font-medium">Location:</span> {job.location}
                          </div>
                          <div>
                            <span className="font-medium">Salary:</span> {job.salary}
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Posted {job.posted}</span>
                          <Button variant="outline" size="sm">
                            <ExternalLink className="w-4 h-4 mr-2" />
                            View Job
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>

                <TabsContent value="culture" className="space-y-6">
                  <Card className="card-enhanced">
                    <CardHeader>
                      <CardTitle>Company Culture</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground leading-relaxed">{company.culture}</p>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="benefits" className="space-y-6">
                  <div className="grid gap-4">
                    {mockBenefits.map((benefit, index) => (
                      <Card key={index} className="card-enhanced">
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                              <benefit.icon className="w-5 h-5 text-primary" />
                            </div>
                            <div>
                              <h4 className="font-medium mb-1">{benefit.title}</h4>
                              <p className="text-sm text-muted-foreground">{benefit.description}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="team" className="space-y-6">
                  {team.map((member, index) => (
                    <Card key={index} className="card-enhanced">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <Avatar className="w-16 h-16">
                            <AvatarImage src={member.avatar} alt={member.name} />
                            <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold">
                              {member.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h4 className="font-bold text-lg">{member.name}</h4>
                            <p className="text-primary font-medium mb-2">{member.role}</p>
                            <p className="text-muted-foreground">{member.bio}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
