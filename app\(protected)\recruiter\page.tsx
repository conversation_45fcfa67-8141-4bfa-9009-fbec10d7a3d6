import { Metadata } from 'next'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { RecruiterDashboard } from '@/components/recruiter/recruiter-dashboard'

export const metadata: Metadata = {
  title: 'Recruiter Dashboard | JobPortal',
  description: 'Manage your job postings, review applications, and track hiring progress with our comprehensive recruiter dashboard.',
}

function RecruiterDashboardContent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Recruiter Dashboard</h1>
              <p className="text-muted-foreground">
                Manage your hiring process and track candidate applications
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <RecruiterDashboard />
      </main>
    </div>
  )
}

export default function RecruiterDashboardPage() {
  return (
    <ProtectedRoute requiredRole="recruiter">
      <RecruiterDashboardContent />
    </ProtectedRoute>
  )
}
