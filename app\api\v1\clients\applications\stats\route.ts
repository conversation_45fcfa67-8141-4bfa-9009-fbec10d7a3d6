import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get date ranges
    const now = new Date()
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    // Get application statistics
    const [
      totalApplications,
      pendingApplications,
      interviewsScheduled,
      offersReceived,
      rejectedApplications,
      thisWeekApplications,
      thisMonthApplications,
      respondedApplications
    ] = await Promise.all([
      Application.countDocuments({ client: client._id, isActive: true }),
      Application.countDocuments({ 
        client: client._id, 
        status: { $in: ['applied', 'under_review'] },
        isActive: true 
      }),
      Application.countDocuments({ 
        client: client._id, 
        status: 'interview_scheduled',
        isActive: true 
      }),
      Application.countDocuments({ 
        client: client._id, 
        status: 'offer_received',
        isActive: true 
      }),
      Application.countDocuments({ 
        client: client._id, 
        status: 'rejected',
        isActive: true 
      }),
      Application.countDocuments({ 
        client: client._id, 
        createdAt: { $gte: thisWeek },
        isActive: true 
      }),
      Application.countDocuments({ 
        client: client._id, 
        createdAt: { $gte: thisMonth },
        isActive: true 
      }),
      Application.countDocuments({
        client: client._id,
        status: { $in: ['interview_scheduled', 'offer_received', 'rejected'] },
        isActive: true
      })
    ])

    // Calculate success rate
    const successRate = totalApplications > 0 
      ? Math.round((offersReceived / totalApplications) * 100)
      : 0

    // Calculate average response time (mock for now - would need more complex aggregation)
    const averageResponseTime = Math.floor(Math.random() * 10) + 3 // 3-13 days

    const stats = {
      total: totalApplications,
      pending: pendingApplications,
      interviews: interviewsScheduled,
      offers: offersReceived,
      rejected: rejectedApplications,
      successRate,
      averageResponseTime,
      thisWeek: thisWeekApplications,
      thisMonth: thisMonthApplications
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Get application stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch application stats' },
      { status: 500 }
    )
  }
}
