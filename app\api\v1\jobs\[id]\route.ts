import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validate<PERSON>ethod, validateRequestBody } from '@/lib/api/route-handler'
import { jobService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import type { UpdateJobRequest } from '@/lib/services'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

interface RouteParams {
  params: {
    id: string
  }
}

// Validation function for update job request
function validateUpdateJobRequest(data: any): UpdateJobRequest {
  const errors: string[] = []
  
  // Validate enums if provided
  if (data.type && !['full-time', 'part-time', 'contract', 'internship'].includes(data.type)) {
    errors.push('Invalid job type')
  }
  
  if (data.level && !['entry', 'mid', 'senior', 'executive'].includes(data.level)) {
    errors.push('Invalid job level')
  }
  
  // Validate salary if provided
  if (data.salary) {
    if (data.salary.min && data.salary.max && data.salary.min > data.salary.max) {
      errors.push('Minimum salary cannot be greater than maximum salary')
    }
  }
  
  // Validate application deadline if provided
  if (data.applicationDeadline) {
    const deadline = new Date(data.applicationDeadline)
    if (deadline <= new Date()) {
      errors.push('Application deadline must be in the future')
    }
  }
  
  if (errors.length > 0) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      `Validation failed: ${errors.join(', ')}`,
      undefined,
      { validationErrors: errors }
    )
  }
  
  const updateData: UpdateJobRequest = {}
  
  if (data.title) updateData.title = data.title.trim()
  if (data.description) updateData.description = data.description.trim()
  if (data.location) updateData.location = data.location
  if (data.salary) updateData.salary = data.salary
  if (data.requirements) updateData.requirements = data.requirements.map((req: string) => req.trim())
  if (data.benefits) updateData.benefits = data.benefits.map((benefit: string) => benefit.trim())
  if (data.type) updateData.type = data.type
  if (data.level) updateData.level = data.level
  if (data.category) updateData.category = data.category.trim()
  if (data.tags) updateData.tags = data.tags.map((tag: string) => tag.trim().toLowerCase())
  if (data.isActive !== undefined) updateData.isActive = Boolean(data.isActive)
  if (data.applicationDeadline) updateData.applicationDeadline = new Date(data.applicationDeadline)
  
  return updateData
}

// GET /api/v1/jobs/[id] - Get job by ID
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['GET'])
  
  const jobId = params.id
  const { searchParams } = new URL(request.url)
  const incrementViews = searchParams.get('incrementViews') === 'true'
  
  const result = await jobService.getJobById(jobId, incrementViews)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true
})

// PUT /api/v1/jobs/[id] - Update job
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['PUT'])
  
  const jobId = params.id
  const updateData = await validateRequestBody(request, validateUpdateJobRequest)
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const result = await jobService.updateJob(jobId, updateData, userId)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// DELETE /api/v1/jobs/[id] - Deactivate job
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['DELETE'])
  
  const jobId = params.id
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  await jobService.deactivateJob(jobId, userId)
  
  return createSuccessResponse({ message: 'Job deactivated successfully' })
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed for individual job resources'
  )
}
