// app/(company-dashboard)/company-dashboard/interviews/page.tsx
"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Calendar,
  Clock,
  Video,
  MapPin,
  Phone,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertCircle,
  Users,
  Briefcase,
  CalendarDays,
  MessageSquare
} from "lucide-react"

export default function InterviewsPage() {
  const [activeTab, setActiveTab] = useState("upcoming")
  const [searchQuery, setSearchQuery] = useState("")

  const interviews = [
    {
      id: "1",
      candidate: {
        name: "<PERSON>",
        email: "<EMAIL>",
        avatar: null,
        position: "Senior Frontend Developer"
      },
      job: {
        title: "Senior Frontend Developer",
        department: "Engineering"
      },
      date: "2024-02-16",
      time: "14:00",
      duration: 60,
      type: "video",
      status: "scheduled",
      interviewer: "John Doe",
      location: "Zoom Meeting",
      notes: "Technical interview focusing on React and TypeScript",
      round: "Technical Round"
    },
    {
      id: "2",
      candidate: {
        name: "Mike Chen",
        email: "<EMAIL>",
        avatar: null,
        position: "Backend Engineer"
      },
      job: {
        title: "Backend Engineer",
        department: "Engineering"
      },
      date: "2024-02-16",
      time: "10:30",
      duration: 45,
      type: "phone",
      status: "scheduled",
      interviewer: "Jane Smith",
      location: "Phone Call",
      notes: "Initial screening call",
      round: "Phone Screening"
    },
    {
      id: "3",
      candidate: {
        name: "Emily Davis",
        email: "<EMAIL>",
        avatar: null,
        position: "Product Manager"
      },
      job: {
        title: "Product Manager",
        department: "Product"
      },
      date: "2024-02-15",
      time: "15:30",
      duration: 90,
      type: "in-person",
      status: "completed",
      interviewer: "Bob Wilson",
      location: "Conference Room A",
      notes: "Final round interview with team",
      round: "Final Round"
    },
    {
      id: "4",
      candidate: {
        name: "Alex Rodriguez",
        email: "<EMAIL>",
        avatar: null,
        position: "UX Designer"
      },
      job: {
        title: "UX Designer",
        department: "Design"
      },
      date: "2024-02-14",
      time: "11:00",
      duration: 60,
      type: "video",
      status: "no-show",
      interviewer: "Lisa Brown",
      location: "Google Meet",
      notes: "Candidate did not attend",
      round: "Portfolio Review"
    }
  ]

  const getFilteredInterviews = () => {
    let filtered = interviews

    // Filter by tab
    const today = new Date()
    const todayStr = today.toISOString().split('T')[0]

    switch (activeTab) {
      case "upcoming":
        filtered = interviews.filter(interview => 
          interview.date >= todayStr && interview.status === 'scheduled'
        )
        break
      case "today":
        filtered = interviews.filter(interview => 
          interview.date === todayStr
        )
        break
      case "completed":
        filtered = interviews.filter(interview => 
          interview.status === 'completed'
        )
        break
      case "all":
      default:
        break
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(interview =>
        interview.candidate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        interview.job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        interview.candidate.email.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    return filtered
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      case 'no-show': return 'bg-red-100 text-red-800'
      case 'rescheduled': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="w-4 h-4" />
      case 'phone': return <Phone className="w-4 h-4" />
      case 'in-person': return <MapPin className="w-4 h-4" />
      default: return <Calendar className="w-4 h-4" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled': return <Clock className="w-4 h-4" />
      case 'completed': return <CheckCircle className="w-4 h-4" />
      case 'cancelled': return <XCircle className="w-4 h-4" />
      case 'no-show': return <AlertCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }

  const filteredInterviews = getFilteredInterviews()

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Interview Management</h1>
            <p className="text-muted-foreground mt-2">
              Schedule, manage, and track all your interviews
            </p>
          </div>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Schedule Interview
          </Button>
        </div>

        {/* Interview Stats */}
        <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Interviews</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{interviews.length}</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {interviews.filter(i => i.status === 'scheduled').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Next 7 days
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {interviews.filter(i => i.status === 'completed').length}
              </div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">No-Shows</CardTitle>
              <AlertCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {interviews.filter(i => i.status === 'no-show').length}
              </div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="w-full mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search interviews..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select defaultValue="all">
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="no-show">No Show</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              More Filters
            </Button>
          </div>
        </div>

        {/* Interview Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="upcoming">
              Upcoming ({interviews.filter(i => i.status === 'scheduled').length})
            </TabsTrigger>
            <TabsTrigger value="today">
              Today ({interviews.filter(i => i.date === new Date().toISOString().split('T')[0]).length})
            </TabsTrigger>
            <TabsTrigger value="completed">
              Completed ({interviews.filter(i => i.status === 'completed').length})
            </TabsTrigger>
            <TabsTrigger value="all">
              All ({interviews.length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="w-full">
            <Card>
              <CardContent className="p-0">
                {filteredInterviews.length === 0 ? (
                  <div className="text-center py-12">
                    <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No interviews found</h3>
                    <p className="text-muted-foreground mb-4">
                      {searchQuery 
                        ? "Try adjusting your search criteria"
                        : `No ${activeTab === 'all' ? '' : activeTab} interviews to show`
                      }
                    </p>
                    {!searchQuery && activeTab === 'upcoming' && (
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Schedule Interview
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="divide-y">
                    {filteredInterviews.map((interview) => (
                      <div key={interview.id} className="p-6 hover:bg-muted/50 transition-colors">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4 flex-1">
                            <Avatar className="w-12 h-12">
                              <AvatarImage src={interview.candidate.avatar} />
                              <AvatarFallback>
                                {interview.candidate.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-semibold">{interview.candidate.name}</h3>
                                <Badge className={getStatusColor(interview.status)}>
                                  <span className="flex items-center space-x-1">
                                    {getStatusIcon(interview.status)}
                                    <span className="capitalize">{interview.status.replace('-', ' ')}</span>
                                  </span>
                                </Badge>
                                <Badge variant="outline">
                                  {interview.round}
                                </Badge>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-muted-foreground">
                                <div className="flex items-center space-x-2">
                                  <Briefcase className="w-4 h-4" />
                                  <span>{interview.job.title}</span>
                                </div>

                                <div className="flex items-center space-x-2">
                                  <CalendarDays className="w-4 h-4" />
                                  <span>
                                    {new Date(interview.date).toLocaleDateString()} at {formatTime(interview.time)}
                                  </span>
                                </div>

                                <div className="flex items-center space-x-2">
                                  {getTypeIcon(interview.type)}
                                  <span className="capitalize">{interview.type}</span>
                                </div>

                                <div className="flex items-center space-x-2">
                                  <Users className="w-4 h-4" />
                                  <span>{interview.interviewer}</span>
                                </div>
                              </div>

                              <div className="mt-3 flex items-center space-x-4 text-sm">
                                <span className="text-muted-foreground">
                                  Duration: {interview.duration} minutes
                                </span>
                                <span className="text-muted-foreground">•</span>
                                <span className="text-muted-foreground">
                                  {interview.location}
                                </span>
                              </div>

                              {interview.notes && (
                                <div className="mt-3 p-3 bg-muted/50 rounded-lg">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <MessageSquare className="w-4 h-4 text-muted-foreground" />
                                    <span className="text-sm font-medium">Notes</span>
                                  </div>
                                  <p className="text-sm text-muted-foreground">{interview.notes}</p>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2 ml-4">
                            {interview.status === 'scheduled' && (
                              <>
                                <Button variant="outline" size="sm">
                                  Join
                                </Button>
                                <Button variant="outline" size="sm">
                                  Reschedule
                                </Button>
                              </>
                            )}
                            
                            {interview.status === 'completed' && (
                              <Button variant="outline" size="sm">
                                View Feedback
                              </Button>
                            )}

                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
