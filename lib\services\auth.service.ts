import { User } from '@/lib/models/user.model'
import { Company } from '@/lib/models/company.model'
import { Client } from '@/lib/models/client.model'
import { generateTokens } from '@/lib/auth/middleware'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { normalizeWebsiteUrl } from '@/lib/utils'

// Interfaces for type safety
interface UserDocument {
  _id: string
  email: string
  role: string
  profile: {
    firstName: string
    lastName: string
    phone?: string
    avatar?: string
    location?: {
      city?: string
      state?: string
      country?: string
    }
  }
  companyId?: string
  isActive: boolean
  isEmailVerified: boolean
  save: () => Promise<void>
}

interface CompanyData {
  name: string
  description?: string
  website?: string
  industry: string | string[]
  size: string
  location?: {
    city?: string
    state?: string
    country?: string
  }
}

interface LocationData {
  city?: string
  state?: string
  country?: string
}

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role?: 'job_seeker' | 'company_admin'
  phone?: string
  location?: {
    city?: string
    state?: string
    country?: string
  }
  company?: {
    name: string
    description?: string
    website?: string
    industry: string
    size: string
    location?: {
      city: string
      state?: string
      country: string
    }
  }
}

export interface AuthResponse {
  user: {
    id: string
    email: string
    role: string
    profile: {
      firstName: string
      lastName: string
      fullName: string
      avatar?: string
    }
    isEmailVerified: boolean
    companyId?: string
  }
  tokens: {
    accessToken: string
    refreshToken: string
  }
  company?: {
    _id: string
    name: string
    slug: string
    description: string
    website?: string
    industry: string[]
    size: string
    verification: {
      isVerified: boolean
      verifiedAt?: Date
      documents?: string[]
    }
  }
  client?: {
    _id: string
    headline: string
    summary: string
    experience: {
      years: number
      level: 'entry' | 'mid' | 'senior' | 'lead'
      positions?: Array<{
        title: string
        company: string
        duration: string
      }>
    }
    profileCompleteness: number
  }
  emailVerificationRequired?: boolean
}

export class AuthService {
  /**
   * Authenticate user with email and password
   */
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    try {
      // Find user by email and include password for comparison
      const user = await User.findOne({
        email: loginData.email
      }).select('+password')
      
      if (!user) {
        throw errorService.createError(
          ErrorCode.INVALID_CREDENTIALS,
          'Invalid email or password',
          'email'
        )
      }
      
      // Check if account is active
      if (!user.isActive) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'Account has been deactivated. Please contact support.',
          'account'
        )
      }
      
      // Verify password
      const isPasswordValid = await user.comparePassword(loginData.password)
      if (!isPasswordValid) {
        throw errorService.createError(
          ErrorCode.INVALID_CREDENTIALS,
          'Invalid email or password',
          'password'
        )
      }
      
      // Update last login
      user.lastLogin = new Date()
      await user.save()
      
      // Generate tokens
      const tokens = generateTokens(user)
      
      return await this.formatAuthResponse(user, tokens)
      
    } catch (error: unknown) {
      // Don't expose specific error details for security
      if (error instanceof Error && error.message.includes('Invalid email or password')) {
        throw error
      }
      
      // Log the actual error but return generic message
      console.error('Login error:', error)
      throw errorService.createError(
        ErrorCode.INVALID_CREDENTIALS,
        'Invalid email or password'
      )
    }
  }

  /**
   * Register a new user
   */
  async register(registerData: RegisterRequest): Promise<AuthResponse> {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({ email: registerData.email })
      if (existingUser) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'An account with this email already exists',
          'email'
        )
      }
      
      // Create new user
      const user = new User({
        email: registerData.email,
        password: registerData.password,
        role: registerData.role || 'job_seeker',
        profile: {
          firstName: registerData.firstName,
          lastName: registerData.lastName,
          phone: registerData.phone,
          location: registerData.location || {}
        },
        preferences: {
          emailNotifications: true,
          jobAlerts: true,
          marketingEmails: false,
          theme: 'system'
        },
        isActive: true,
        isEmailVerified: false // Will be verified via email
      })
      
      await user.save()

      // Create company FIRST if user is company admin
      let company = null
      if (registerData.role === 'company_admin' && registerData.company) {
        try {
          company = await this.createCompanyForUser(user, registerData.company)
          user.companyId = company._id
          await user.save()
        } catch (companyError) {
          console.error('Company creation error:', companyError)
          throw new Error('Failed to create company during registration')
        }
      }

      // Create client profile for job seekers
      if (registerData.role === 'job_seeker') {
        try {
          await this.createClientForUser(user, registerData.location || {})
        } catch (clientError) {
          console.error('Client creation error:', clientError)
          // Continue with user registration even if client creation fails
        }
      }

      // Generate tokens
      const tokens = generateTokens(user)

      // TODO: Send email verification email
      // await emailService.sendVerificationEmail(user)

      return await this.formatAuthResponse(user, tokens)
      
    } catch (error: unknown) {
      // Handle specific database errors
      if (typeof error === 'object' && error !== null && 'code' in error && (error as Record<string, unknown>).code === 11000) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'An account with this email already exists',
          'email'
        )
      }

      throw error
    }
  }

  /**
   * Create company for company admin user
   */
  private async createCompanyForUser(user: UserDocument, companyData: CompanyData): Promise<Record<string, unknown>> {
    // Map company size from registration format to model format
    const sizeMapping: Record<string, string> = {
      '1-10': 'startup',
      '11-50': 'small',
      '51-200': 'medium',
      '201-500': 'large',
      '501-1000': 'large',
      '1000+': 'enterprise'
    }

    const newCompany = new Company({
      name: companyData.name,
      slug: companyData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
      description: companyData.description, // Use exact description from registration
      tagline: `${companyData.name} - ${companyData.industry}`, // More professional tagline
      website: companyData.website ? normalizeWebsiteUrl(companyData.website) : undefined,
      industry: [companyData.industry],
      size: sizeMapping[companyData.size] || 'startup',
      contact: {
        email: user.email,
        phone: user.profile.phone || '',
        address: companyData.location?.address || '',
        supportEmail: user.email,
        hrEmail: user.email
      },
      locations: [{
        city: companyData.location?.city || user.profile.location?.city || '',
        state: companyData.location?.state || user.profile.location?.state || '',
        country: companyData.location?.country || user.profile.location?.country || '',
        isHeadquarters: true,
        address: companyData.location?.address || '',
        postalCode: companyData.location?.postalCode || ''
      }],
      culture: {
        values: [],
        benefits: [],
        workEnvironment: '',
        diversity: '',
        mission: '',
        vision: '',
        perks: []
      },
      socialLinks: {
        linkedin: '',
        twitter: '',
        facebook: '',
        instagram: '',
        github: '',
        youtube: '',
        glassdoor: ''
      },
      subscription: {
        plan: 'starter',
        status: 'trial',
        jobPostingLimit: 5,
        jobPostingsUsed: 0,
        featuredJobsLimit: 1,
        featuredJobsUsed: 0
      },
      admins: [user._id],
      recruiters: [],
      teamMembers: [{
        user: user._id,
        role: 'owner',
        department: 'Management',
        joinedAt: new Date(),
        isActive: true
      }],
      stats: {
        totalJobs: 0,
        activeJobs: 0,
        totalApplications: 0,
        totalHires: 0,
        profileViews: 0,
        followerCount: 0
      },
      verification: {
        isVerified: false,
        documents: [],
        status: 'pending'
      },
      settings: {
        // Profile & Privacy Settings
        allowPublicProfile: true,
        showSalaryRanges: true,
        allowDirectContact: true,
        showCompanyStats: false,

        // Application Management Settings
        autoRejectAfterDays: 30,
        requireCoverLetter: false,
        enableApplicationTracking: true,
        allowRemoteApplications: true,
        customApplicationQuestions: [],

        // Notification Settings
        emailNotifications: {
          newApplications: true,
          applicationUpdates: true,
          interviewReminders: true,
          jobExpiring: true,
          weeklyReports: false,
          marketingEmails: false
        },
        pushNotifications: {
          newApplications: true,
          applicationUpdates: false,
          interviewReminders: true,
          systemUpdates: true
        },

        // Branding Settings
        branding: {
          primaryColor: '#000000',
          secondaryColor: '#ffffff',
          logoUrl: '',
          coverImageUrl: '',
          customCss: ''
        },

        // Team Management Settings
        team: {
          allowMemberInvites: true,
          requireApprovalForInvites: true,
          defaultMemberRole: 'recruiter',
          maxTeamMembers: 10
        },

        // Integration Settings
        integrations: {
          atsEnabled: false,
          atsProvider: '',
          atsApiKey: '',
          slackWebhookUrl: '',
          googleAnalyticsId: '',
          linkedinCompanyId: ''
        },

        // Advanced Settings
        advanced: {
          customApplicationFields: [],
          autoResponseEnabled: true,
          autoResponseMessage: 'Thank you for your application. We will review it and get back to you soon.',
          applicationDeadlineReminder: true,
          bulkActionsEnabled: true
        }
      },
      isActive: true,
      createdBy: user._id,
      isFeatured: false,
      createdBy: user._id
    })

    await newCompany.save()
    return newCompany
  }

  /**
   * Create client profile for job seeker user
   */
  private async createClientForUser(user: UserDocument, location: LocationData): Promise<Record<string, unknown>> {
    const newClient = new Client({
      user: user._id,
      headline: `${user.profile.firstName} ${user.profile.lastName} - Professional`,
      summary: 'I am a motivated professional looking for new opportunities to grow my career.',
      experience: {
        level: 'entry',
        yearsOfExperience: 0,
        industries: []
      },
      jobPreferences: {
        desiredRoles: [],
        industries: [],
        locations: [{
          city: location?.city || '',
          state: location?.state || '',
          country: location?.country || '',
          remote: false,
          relocationWilling: false
        }],
        salaryExpectation: {
          min: 30000,
          max: 60000,
          currency: 'USD',
          period: 'yearly',
          negotiable: true
        },
        jobTypes: ['full-time'],
        workArrangement: ['onsite'],
        availability: 'immediately',
        benefits: [],
        companySize: []
      },
      privacy: {
        profileVisibility: 'public',
        showSalaryExpectation: true,
        showCurrentCompany: true,
        allowRecruiterContact: true,
        showProfileToCurrentEmployer: false
      },
      isActive: true,
      isPublic: true
    })

    await newClient.save()
    return newClient
  }

  /**
   * Format user data for API response
   */
  private async formatAuthResponse(user: UserDocument, tokens: { accessToken: string; refreshToken: string }): Promise<AuthResponse> {
    // Get company data if user is company admin
    let companyData: {
      _id: string
      name: string
      slug: string
      description: string
      website?: string
      industry: string[]
      size: string
      verification: {
        isVerified: boolean
        verifiedAt?: Date
        documents?: string[]
      }
    } | undefined = undefined

    if (user.role === 'company_admin' && user.companyId) {
      try {
        const company = await Company.findById(user.companyId).lean()
        if (company) {
          companyData = {
            _id: company._id.toString(),
            name: company.name || '',
            slug: company.slug || '',
            description: company.description || '',
            website: company.website,
            industry: company.industry || [],
            size: company.size || 'startup',
            verification: company.verification || { isVerified: false }
          }
        }
      } catch (companyError) {
        console.error('Error fetching company data:', companyError)
        // Continue without company data
      }
    }

    // Get client data if user is job seeker
    let clientData: {
      _id: string
      headline: string
      summary: string
      experience: {
        years: number
        level: 'entry' | 'mid' | 'senior' | 'lead'
        positions?: Array<{
          title: string
          company: string
          duration: string
        }>
      }
      profileCompleteness: number
    } | undefined = undefined

    if (user.role === 'job_seeker') {
      try {
        const client = await Client.findOne({ user: user._id }).lean()
        if (client) {
          clientData = {
            _id: client._id.toString(),
            headline: client.headline || '',
            summary: client.summary || '',
            experience: client.experience || { years: 0, level: 'entry' },
            profileCompleteness: client.activity?.profileCompleteness || 0
          }
        }
      } catch (clientError) {
        console.error('Error fetching client data:', clientError)
        // Continue without client data
      }
    }

    return {
      user: {
        id: user._id.toString(),
        email: user.email,
        role: user.role,
        profile: {
          firstName: user.profile.firstName,
          lastName: user.profile.lastName,
          fullName: `${user.profile.firstName} ${user.profile.lastName}`,
          avatar: user.profile.avatar
        },
        isEmailVerified: user.isEmailVerified,
        companyId: user.companyId?.toString()
      },
      tokens,
      company: companyData,
      client: clientData,
      emailVerificationRequired: !user.isEmailVerified
    }
  }
}

export const authService = new AuthService()
