const BASE_URL = 'http://localhost:3000/api'

async function testLoginFlow() {
  console.log('🧪 Testing Login Flow...\n')
  
  // Test data
  const testUser = {
    email: '<EMAIL>',
    password: 'Test123!',
    firstName: 'Test',
    lastName: 'User',
    role: 'job_seeker'
  }
  
  const testCompanyAdmin = {
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'Admin',
    lastName: 'User',
    role: 'company_admin',
    company: {
      name: 'Test Company',
      description: 'A test company',
      industry: 'Technology',
      size: '10-50',
      location: {
        city: 'San Francisco',
        state: 'CA',
        country: 'USA'
      }
    }
  }

  try {
    // Test 1: Register Job Seeker
    console.log('1️⃣ Registering Job Seeker...')
    const registerResponse1 = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testUser)
    })
    
    const registerData1 = await registerResponse1.json()
    console.log('✅ Job Seeker Registration:', registerData1.message || 'Success')
    console.log('   User Role:', registerData1.user?.role)
    console.log('   Client Created:', !!registerData1.client)
    
    // Test 2: Register Company Admin
    console.log('\n2️⃣ Registering Company Admin...')
    const registerResponse2 = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testCompanyAdmin)
    })
    
    const registerData2 = await registerResponse2.json()
    console.log('✅ Company Admin Registration:', registerData2.message || 'Success')
    console.log('   User Role:', registerData2.user?.role)
    console.log('   Company Created:', !!registerData2.company)
    
    // Test 3: Login Job Seeker
    console.log('\n3️⃣ Testing Job Seeker Login...')
    const loginResponse1 = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    })
    
    const loginData1 = await loginResponse1.json()
    console.log('✅ Job Seeker Login:', loginData1.message || 'Success')
    console.log('   User Role:', loginData1.user?.role)
    console.log('   Expected Redirect: /client/dashboard')
    
    // Test 4: Login Company Admin
    console.log('\n4️⃣ Testing Company Admin Login...')
    const loginResponse2 = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: testCompanyAdmin.email,
        password: testCompanyAdmin.password
      })
    })
    
    const loginData2 = await loginResponse2.json()
    console.log('✅ Company Admin Login:', loginData2.message || 'Success')
    console.log('   User Role:', loginData2.user?.role)
    console.log('   Expected Redirect: /dashboard')
    
    console.log('\n🎉 All tests completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
testLoginFlow()
