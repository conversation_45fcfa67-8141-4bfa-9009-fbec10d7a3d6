"use client"
import { useState, useEffect } from "react"
import { SharedLayout } from "@/components/layouts/shared-layout"
import { HeroSection } from "@/components/hero-section"
import { SkillsAssessment } from "@/components/skills-assessment"
import { CompanyShowcase } from "@/components/company-showcase"
import { TestimonialsSection } from "@/components/testimonials-section"
import { LocationBanner } from "@/components/location/location-banner"
import { LocationBasedJobsSection } from "@/components/home/<USER>"
import { SearchedJobsResults } from "@/components/search/searched-jobs-results"
import { getAllJobs, type Job } from "@/lib/job-data"

export default function HomePage() {
  const [isSearchActive, setIsSearchActive] = useState(false)
  const [searchResults, setSearchResults] = useState<Job[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [allJobs, setAllJobs] = useState<Job[]>([])

  // Load all jobs for search
  useEffect(() => {
    const loadJobs = () => {
      try {
        const jobs = getAllJobs()
        setAllJobs(jobs)
      } catch (error) {
        console.error('Failed to load jobs:', error)
      }
    }
    loadJobs()
  }, [])

  // Enhanced search function with robust matching
  const handleSearch = (query: string, location: string): void => {
    const trimmedQuery = query.trim()
    const trimmedLocation = location.trim()

    if (!trimmedQuery && !trimmedLocation) {
      setIsSearchActive(false)
      setSearchResults([])
      setSearchQuery("")
      return
    }

    // Create search terms arrays
    const queryTerms = trimmedQuery.toLowerCase().split(/\s+/).filter(term => term.length > 0)
    const locationTerms = trimmedLocation.toLowerCase().split(/\s+/).filter(term => term.length > 0)

    // Filter jobs with enhanced matching
    const filteredJobs = allJobs.filter((job: Job) => {
      let matchScore = 0

      // Prepare job data for searching
      const jobTitle = (job.title || '').toLowerCase()
      const jobCompany = (job.company?.name || '').toLowerCase()
      const jobLocation = (job.location || '').toLowerCase()
      const jobDescription = (job.description || '').toLowerCase()
      const jobDepartment = (job.department || '').toLowerCase()
      const jobCategory = (job.category || '').toLowerCase()
      const jobSkills = (job.skills || []).map((skill: string) => skill.toLowerCase())
      const jobRequirements = (job.requirements || []).map((req: string) => req.toLowerCase())
      const jobType = (job.type || '').toLowerCase()
      const jobExperience = (job.experience || '').toLowerCase()

      // Location matching (high priority)
      if (locationTerms.length > 0) {
        const locationMatch = locationTerms.some((term: string) => {
          return jobLocation.includes(term) ||
                 term.length >= 3 && jobLocation.includes(term.substring(0, 3)) ||
                 jobLocation.split(/[\s,]+/).some((locPart: string) =>
                   locPart.startsWith(term) || term.startsWith(locPart)
                 )
        })

        if (locationMatch) {
          matchScore += 10 // High score for location match
        } else if (locationTerms.length > 0 && queryTerms.length === 0) {
          // If only location is searched and no match, skip this job
          return false
        }
      }

      // Query term matching
      if (queryTerms.length > 0) {
        queryTerms.forEach((term: string) => {
          // Exact matches (highest priority)
          if (jobTitle.includes(term) || jobCompany.includes(term)) {
            matchScore += 5
          }

          // Partial matches for terms 3+ characters
          if (term.length >= 3) {
            const partialTerm = term.substring(0, 3)

            // Title partial matching
            if (jobTitle.includes(partialTerm) ||
                jobTitle.split(/\s+/).some((word: string) => word.startsWith(term))) {
              matchScore += 3
            }

            // Company partial matching
            if (jobCompany.includes(partialTerm) ||
                jobCompany.split(/\s+/).some((word: string) => word.startsWith(term))) {
              matchScore += 2
            }

            // Skills matching
            if (jobSkills.some((skill: string) =>
                skill.includes(term) ||
                skill.includes(partialTerm) ||
                skill.startsWith(term)
              )) {
              matchScore += 4
            }

            // Requirements matching
            if (jobRequirements.some((req: string) =>
                req.includes(term) ||
                req.includes(partialTerm)
              )) {
              matchScore += 2
            }

            // Category/Department matching
            if (jobCategory.includes(term) || jobCategory.includes(partialTerm) ||
                jobDepartment.includes(term) || jobDepartment.includes(partialTerm)) {
              matchScore += 3
            }

            // Description matching (lower priority)
            if (jobDescription.includes(term) || jobDescription.includes(partialTerm)) {
              matchScore += 1
            }

            // Type and experience matching
            if (jobType.includes(term) || jobType.includes(partialTerm) ||
                jobExperience.includes(term) || jobExperience.includes(partialTerm)) {
              matchScore += 2
            }
          }
        })
      }

      // Return jobs with any match score > 0
      return matchScore > 0
    })

    // Sort by relevance (jobs with higher match scores first)
    const sortedJobs = filteredJobs.sort((a, b) => {
      // Calculate match scores for sorting
      let scoreA = 0, scoreB = 0

      // Simple scoring for sorting
      const checkMatch = (job: Job, terms: string[]) => {
        let score = 0
        const jobText = [
          job.title || '',
          job.company?.name || '',
          job.location || '',
          ...(job.skills || []),
          job.category || '',
          job.department || ''
        ].join(' ').toLowerCase()

        terms.forEach((term: string) => {
          if (jobText.includes(term)) score += 1
        })
        return score
      }

      scoreA = checkMatch(a, [...queryTerms, ...locationTerms])
      scoreB = checkMatch(b, [...queryTerms, ...locationTerms])

      return scoreB - scoreA
    })

    const displayQuery = [trimmedQuery, trimmedLocation].filter(Boolean).join(' in ')
    setSearchResults(sortedJobs)
    setSearchQuery(displayQuery)
    setIsSearchActive(true)
  }

  const handleClearSearch = (): void => {
    setIsSearchActive(false)
    setSearchResults([])
    setSearchQuery("")
  }

  return (
    <SharedLayout>
      <LocationBanner />
      <HeroSection onSearch={handleSearch} isSearchActive={isSearchActive} />

      {isSearchActive ? (
        <SearchedJobsResults
          jobs={searchResults}
          searchQuery={searchQuery}
          onClearSearch={handleClearSearch}
        />
      ) : (
        <>
          <LocationBasedJobsSection />
          {/* <PremiumFeaturesSection /> */}
          <SkillsAssessment />
          {/* <CareerPathVisualizer /> */}
          <CompanyShowcase />
          <TestimonialsSection />
        </>
      )}
    </SharedLayout>
  )
}
