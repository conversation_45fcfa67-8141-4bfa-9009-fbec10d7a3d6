import { BaseDocument, ObjectId } from './base.types'

export type ExperienceLevel = 'entry' | 'mid' | 'senior' | 'executive'
export type SkillCategory = 'technical' | 'soft' | 'language' | 'tool' | 'framework'
export type SkillProficiency = 'beginner' | 'intermediate' | 'advanced' | 'expert'
export type LanguageProficiency = 'basic' | 'conversational' | 'fluent' | 'native'
export type ProfileVisibility = 'public' | 'private' | 'recruiters_only'
export type Availability = 'immediately' | '2_weeks' | '1_month' | '3_months' | 'not_looking'
export type JobType = 'full-time' | 'part-time' | 'contract' | 'freelance' | 'internship'
export type WorkArrangement = 'remote' | 'hybrid' | 'onsite'
export type CompanySize = 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
export type SalaryPeriod = 'hourly' | 'monthly' | 'yearly'
export type AlertFrequency = 'immediate' | 'daily' | 'weekly'

export interface ClientExperience {
  level: ExperienceLevel
  yearsOfExperience: number
  industries: string[]
  currentCompany?: string
  currentRole?: string
  currentSalary?: {
    amount: number
    currency: string
    period: SalaryPeriod
  }
}

export interface WorkHistory {
  company: string
  position: string
  startDate: Date
  endDate?: Date
  isCurrent: boolean
  description: string
  achievements: string[]
  technologies: string[]
  location: {
    city: string
    state?: string
    country: string
    remote: boolean
  }
}

export interface Education {
  institution: string
  degree: string
  fieldOfStudy: string
  startDate: Date
  endDate?: Date
  isCurrent: boolean
  gpa?: number
  achievements: string[]
  location: {
    city: string
    state?: string
    country: string
  }
}

export interface ClientSkill {
  name: string
  category: SkillCategory
  proficiency: SkillProficiency
  yearsOfExperience?: number
  endorsed: boolean
  endorsements: number
}

export interface Certification {
  name: string
  issuer: string
  issueDate: Date
  expiryDate?: Date
  credentialId?: string
  credentialUrl?: string
  isActive: boolean
}

export interface Portfolio {
  title: string
  description: string
  url?: string
  imageUrl?: string
  technologies: string[]
  startDate: Date
  endDate?: Date
  isOngoing: boolean
  role: string
  achievements: string[]
}

export interface ClientDocuments {
  resume: Array<{
    filename: string
    url: string
    uploadDate: Date
    isActive: boolean
    fileSize: number
    fileType: string
  }>
  coverLetter?: {
    content: string
    lastUpdated: Date
  }
  portfolio?: {
    url: string
    lastUpdated: Date
  }
}

export interface JobPreferences {
  desiredRoles: string[]
  industries: string[]
  locations: Array<{
    city: string
    state?: string
    country: string
    remote: boolean
    relocationWilling: boolean
  }>
  salaryExpectation: {
    min: number
    max: number
    currency: string
    period: SalaryPeriod
    negotiable: boolean
  }
  jobTypes: JobType[]
  workArrangement: WorkArrangement[]
  availability: Availability
  benefits: string[]
  companySize: CompanySize[]
}

export interface ClientLanguage {
  language: string
  proficiency: LanguageProficiency
  certified: boolean
}

export interface ClientSocialLinks {
  linkedin?: string
  github?: string
  portfolio?: string
  website?: string
  twitter?: string
  behance?: string
  dribbble?: string
  stackoverflow?: string
}

export interface ClientPrivacy {
  profileVisibility: ProfileVisibility
  showSalaryExpectation: boolean
  showCurrentCompany: boolean
  allowRecruiterContact: boolean
  showProfileToCurrentEmployer: boolean
}

export interface ClientActivity {
  profileViews: number
  searchAppearances: number
  recruiterViews: number
  lastProfileUpdate: Date
  profileCompleteness: number
}

export interface ApplicationStats {
  totalApplications: number
  pendingApplications: number
  interviewsReceived: number
  offersReceived: number
  successRate: number
}

export interface JobAlert {
  name: string
  criteria: {
    keywords: string[]
    locations: string[]
    industries: string[]
    salaryMin?: number
    jobTypes: string[]
  }
  frequency: AlertFrequency
  isActive: boolean
  createdAt: Date
}

export interface Reference {
  name: string
  position: string
  company: string
  email: string
  phone?: string
  relationship: string
  verified: boolean
}

export interface ClientVerification {
  emailVerified: boolean
  phoneVerified: boolean
  identityVerified: boolean
  backgroundCheckCompleted: boolean
  references: Reference[]
}

export interface Client extends BaseDocument {
  user: ObjectId
  headline: string
  summary: string
  currentTitle?: string
  experience: ClientExperience
  workHistory: WorkHistory[]
  education: Education[]
  skills: ClientSkill[]
  certifications: Certification[]
  portfolio: Portfolio[]
  documents: ClientDocuments
  jobPreferences: JobPreferences
  languages: ClientLanguage[]
  socialLinks: ClientSocialLinks
  privacy: ClientPrivacy
  activity: ClientActivity
  applicationStats: ApplicationStats
  savedJobs: ObjectId[]
  followedCompanies: ObjectId[]
  jobAlerts: JobAlert[]
  verification: ClientVerification
  isActive: boolean
  isPublic: boolean
  lastLogin: Date
}

// Request/Response types
export interface CreateClientRequest {
  headline: string
  summary: string
  currentTitle?: string
  experience: Omit<ClientExperience, 'currentSalary'> & {
    currentSalary?: {
      amount: number
      currency: string
      period: SalaryPeriod
    }
  }
  jobPreferences: JobPreferences
  privacy?: Partial<ClientPrivacy>
}

export interface UpdateClientRequest extends Partial<CreateClientRequest> {
  workHistory?: WorkHistory[]
  education?: Education[]
  skills?: ClientSkill[]
  certifications?: Certification[]
  portfolio?: Portfolio[]
  languages?: ClientLanguage[]
  socialLinks?: ClientSocialLinks
  jobAlerts?: JobAlert[]
}

export interface ClientProfile extends Client {
  fullName: string
  profileCompleteness: number
}

export interface ClientSearchFilters {
  skills?: string[]
  experience?: ExperienceLevel[]
  industries?: string[]
  locations?: string[]
  availability?: Availability[]
  salaryMin?: number
  salaryMax?: number
  profileVisibility?: ProfileVisibility[]
}

export interface ClientSearchResult {
  clients: ClientProfile[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// Validation schemas
export const validateCreateClientRequest = (data: any): CreateClientRequest => {
  // Add validation logic here
  return data as CreateClientRequest
}

export const validateUpdateClientRequest = (data: any): UpdateClientRequest => {
  // Add validation logic here
  return data as UpdateClientRequest
}
