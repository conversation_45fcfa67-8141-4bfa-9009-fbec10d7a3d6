// Simple test script to verify our API endpoints
const BASE_URL = 'http://localhost:3000/api/v1'

async function testRegisterAPI() {
  try {
    console.log('🧪 Testing Registration API...')
    
    const response = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword123',
        firstName: 'John',
        lastName: 'Doe',
        role: 'job_seeker'
      })
    })
    
    const data = await response.json()
    console.log('📝 Registration Response:', JSON.stringify(data, null, 2))
    
    if (data.success) {
      console.log('✅ Registration API working correctly!')
      return data.data.tokens.accessToken
    } else {
      console.log('❌ Registration failed:', data.error)
      return null
    }
  } catch (error) {
    console.error('❌ Registration API error:', error.message)
    return null
  }
}

async function testLoginAPI() {
  try {
    console.log('\n🧪 Testing Login API...')
    
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword123'
      })
    })
    
    const data = await response.json()
    console.log('📝 Login Response:', JSON.stringify(data, null, 2))
    
    if (data.success) {
      console.log('✅ Login API working correctly!')
      return data.data.tokens.accessToken
    } else {
      console.log('❌ Login failed:', data.error)
      return null
    }
  } catch (error) {
    console.error('❌ Login API error:', error.message)
    return null
  }
}

async function testErrorHandling() {
  try {
    console.log('\n🧪 Testing Error Handling...')
    
    // Test with invalid data
    const response = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: 'invalid-email',
        password: '123', // Too short
        firstName: '',
        lastName: ''
      })
    })
    
    const data = await response.json()
    console.log('📝 Error Response:', JSON.stringify(data, null, 2))
    
    if (!data.success && data.error) {
      console.log('✅ Error handling working correctly!')
    } else {
      console.log('❌ Error handling not working as expected')
    }
  } catch (error) {
    console.error('❌ Error handling test failed:', error.message)
  }
}

async function runTests() {
  console.log('🚀 Starting API Tests...\n')
  
  // Test registration
  const registerToken = await testRegisterAPI()
  
  // Test login (if registration failed, try login anyway)
  const loginToken = await testLoginAPI()
  
  // Test error handling
  await testErrorHandling()
  
  console.log('\n🏁 Tests completed!')
  
  if (registerToken || loginToken) {
    console.log('✅ Basic authentication flow is working!')
  } else {
    console.log('❌ Authentication flow needs debugging')
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests()
}

module.exports = { testRegisterAPI, testLoginAPI, testErrorHandling }
