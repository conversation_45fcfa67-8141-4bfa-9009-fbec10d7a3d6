// app/(company-dashboard)/company-dashboard/applications/page.tsx
"use client"

import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useCompanyDashboardStore } from "@/stores/company-dashboard.store"
import {
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  MessageSquare,
  Calendar,
  Users,
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  Star,
  Download
} from "lucide-react"

export default function CompanyApplicationsPage() {
  const router = useRouter()
  const {
    applications,
    applicationsLoading,
    fetchApplications,
    updateApplicationStatus,
    error
  } = useCompanyDashboardStore()

  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    fetchApplications()
  }, [fetchApplications])

  const filteredApplications = applications.filter(app => {
    const matchesSearch = 
      app.candidate?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.job?.title?.toLowerCase().includes(searchQuery.toLowerCase())
    
    switch (activeTab) {
      case 'pending':
        return matchesSearch && ['submitted', 'under_review'].includes(app.status)
      case 'interviews':
        return matchesSearch && ['interview_scheduled', 'interviewed'].includes(app.status)
      case 'offers':
        return matchesSearch && app.status === 'offer_extended'
      case 'hired':
        return matchesSearch && app.status === 'hired'
      case 'rejected':
        return matchesSearch && app.status === 'rejected'
      default:
        return matchesSearch
    }
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'submitted': return 'bg-blue-100 text-blue-800'
      case 'under_review': return 'bg-yellow-100 text-yellow-800'
      case 'interview_scheduled': return 'bg-purple-100 text-purple-800'
      case 'interviewed': return 'bg-indigo-100 text-indigo-800'
      case 'offer_extended': return 'bg-green-100 text-green-800'
      case 'hired': return 'bg-emerald-100 text-emerald-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted': return <FileText className="w-4 h-4" />
      case 'under_review': return <Eye className="w-4 h-4" />
      case 'interview_scheduled': return <Calendar className="w-4 h-4" />
      case 'interviewed': return <MessageSquare className="w-4 h-4" />
      case 'offer_extended': return <Star className="w-4 h-4" />
      case 'hired': return <CheckCircle className="w-4 h-4" />
      case 'rejected': return <XCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const handleStatusUpdate = async (applicationId: string, newStatus: string) => {
    await updateApplicationStatus(applicationId, newStatus)
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Applications</h1>
            <p className="text-muted-foreground mt-2">
              Review and manage job applications from candidates
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="w-full mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search by candidate name or job title..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        {/* Applications Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="all">All ({applications.length})</TabsTrigger>
            <TabsTrigger value="pending">
              Pending ({applications.filter(a => ['submitted', 'under_review'].includes(a.status)).length})
            </TabsTrigger>
            <TabsTrigger value="interviews">
              Interviews ({applications.filter(a => ['interview_scheduled', 'interviewed'].includes(a.status)).length})
            </TabsTrigger>
            <TabsTrigger value="offers">
              Offers ({applications.filter(a => a.status === 'offer_extended').length})
            </TabsTrigger>
            <TabsTrigger value="hired">
              Hired ({applications.filter(a => a.status === 'hired').length})
            </TabsTrigger>
            <TabsTrigger value="rejected">
              Rejected ({applications.filter(a => a.status === 'rejected').length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="w-full">
            {applicationsLoading ? (
              <div className="w-full space-y-4">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                        <div className="flex-1 space-y-2">
                          <div className="h-5 bg-gray-200 rounded w-1/3"></div>
                          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                        <div className="w-20 h-6 bg-gray-200 rounded"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredApplications.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    {searchQuery ? 'No matching applications found' : 'No applications yet'}
                  </h3>
                  <p className="text-muted-foreground">
                    {searchQuery 
                      ? 'Try adjusting your search criteria'
                      : 'Applications will appear here when candidates apply to your jobs'
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="w-full space-y-4">
                {filteredApplications.map((application) => (
                  <Card key={application._id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <Avatar className="w-12 h-12">
                            <AvatarImage src={application.candidate?.avatar} />
                            <AvatarFallback>
                              {application.candidate?.name?.split(' ').map(n => n[0]).join('') || 'CN'}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-1">
                              <h3 className="font-semibold text-lg">
                                {application.candidate?.name || 'Anonymous Candidate'}
                              </h3>
                              <Badge className={getStatusColor(application.status)}>
                                <span className="flex items-center space-x-1">
                                  {getStatusIcon(application.status)}
                                  <span className="capitalize">{application.status.replace('_', ' ')}</span>
                                </span>
                              </Badge>
                            </div>
                            
                            <div className="text-sm text-muted-foreground space-y-1">
                              <div className="flex items-center space-x-4">
                                <span>Applied for: <strong>{application.job?.title}</strong></span>
                                <span>•</span>
                                <span>Applied: {new Date(application.createdAt).toLocaleDateString()}</span>
                              </div>
                              {application.candidate?.email && (
                                <div>{application.candidate.email}</div>
                              )}
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => router.push(`/company-dashboard/applications/${application._id}`)}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            View
                          </Button>
                          
                          {application.status === 'submitted' && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleStatusUpdate(application._id, 'under_review')}
                            >
                              Review
                            </Button>
                          )}
                          
                          {application.status === 'under_review' && (
                            <>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleStatusUpdate(application._id, 'interview_scheduled')}
                              >
                                Interview
                              </Button>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleStatusUpdate(application._id, 'rejected')}
                              >
                                Reject
                              </Button>
                            </>
                          )}
                          
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
