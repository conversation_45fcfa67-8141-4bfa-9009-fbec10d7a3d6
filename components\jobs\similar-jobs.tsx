'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useJobsStore } from '@/stores'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  MapPin, 
  Building, 
  Clock, 
  DollarSign,
  Bookmark,
  BookmarkCheck,
  TrendingUp,
  Sparkles,
  ArrowRight,
  Star,
  Users,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Job {
  _id: string
  title: string
  company: {
    _id: string
    name: string
    logo?: string
  }
  location: {
    city: string
    state: string
    remote: boolean
  }
  type: string
  level: string
  salary?: {
    min?: number
    max?: number
    currency: string
  }
  description: string
  postedAt: Date
  viewsCount: number
  applicationsCount: number
  tags?: string[]
  matchScore?: number
}

interface SimilarJobsProps {
  currentJob: Job
  maxJobs?: number
  className?: string
}

interface JobCardProps {
  job: Job
  isSaved?: boolean
  onSave?: (jobId: string) => void
  onUnsave?: (jobId: string) => void
  showMatchScore?: boolean
}

function JobCard({ job, isSaved = false, onSave, onUnsave, showMatchScore = false }: JobCardProps) {
  const router = useRouter()

  const formatSalary = () => {
    if (!job.salary?.min && !job.salary?.max) return null
    
    const { min, max, currency = 'USD' } = job.salary
    const formatAmount = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    }

    if (min && max) {
      return `${formatAmount(min)} - ${formatAmount(max)}`
    } else if (min) {
      return `From ${formatAmount(min)}`
    } else if (max) {
      return `Up to ${formatAmount(max)}`
    }
  }

  const getTimeAgo = () => {
    const now = new Date()
    const posted = new Date(job.postedAt)
    const diffTime = Math.abs(now.getTime() - posted.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return '1 day ago'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
    return `${Math.ceil(diffDays / 30)} months ago`
  }

  const handleSaveToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (isSaved && onUnsave) {
      onUnsave(job._id)
    } else if (!isSaved && onSave) {
      onSave(job._id)
    }
  }

  const handleJobClick = () => {
    router.push(`/jobs/${job._id}`)
  }

  return (
    <Card 
      className="cursor-pointer hover:shadow-md transition-shadow duration-200 group"
      onClick={handleJobClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            {/* Company Logo */}
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-medium flex-shrink-0">
              {job.company.logo ? (
                <img 
                  src={job.company.logo} 
                  alt={job.company.name}
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                job.company.name.charAt(0)
              )}
            </div>
            
            {/* Job Info */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm line-clamp-1 group-hover:text-primary transition-colors">
                {job.title}
              </h3>
              <p className="text-sm text-muted-foreground line-clamp-1">
                {job.company.name}
              </p>
            </div>
          </div>

          {/* Match Score */}
          {showMatchScore && job.matchScore && (
            <div className="flex items-center space-x-1 text-xs bg-green-50 text-green-700 px-2 py-1 rounded-full">
              <Star className="w-3 h-3 fill-current" />
              <span>{job.matchScore}% match</span>
            </div>
          )}

          {/* Save Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSaveToggle}
            className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
          >
            {isSaved ? (
              <BookmarkCheck className="w-4 h-4 text-blue-600" />
            ) : (
              <Bookmark className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Job Details */}
        <div className="space-y-2">
          <div className="flex items-center space-x-4 text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <MapPin className="w-3 h-3" />
              <span>
                {job.location.remote ? 'Remote' : `${job.location.city}, ${job.location.state}`}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{getTimeAgo()}</span>
            </div>
          </div>

          {/* Salary */}
          {job.salary && (
            <div className="flex items-center space-x-1 text-xs text-green-600 font-medium">
              <DollarSign className="w-3 h-3" />
              <span>{formatSalary()}</span>
            </div>
          )}

          {/* Job Type & Level */}
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              {job.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {job.level.replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
            {job.location.remote && (
              <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                Remote
              </Badge>
            )}
          </div>

          {/* Job Stats */}
          <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-1">
                <Users className="w-3 h-3" />
                <span>{job.applicationsCount} applicants</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye className="w-3 h-3" />
                <span>{job.viewsCount} views</span>
              </div>
            </div>
            <ArrowRight className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

function JobCardSkeleton() {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-start space-x-3 mb-3">
          <Skeleton className="w-10 h-10 rounded-lg" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-2/3" />
          <div className="flex space-x-2">
            <Skeleton className="h-5 w-16 rounded-full" />
            <Skeleton className="h-5 w-20 rounded-full" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function SimilarJobs({ currentJob, maxJobs = 6, className }: SimilarJobsProps) {
  const { 
    jobs, 
    jobsLoading, 
    savedJobs,
    searchJobs,
    saveJob,
    unsaveJob
  } = useJobsStore()
  
  const [similarJobs, setSimilarJobs] = useState<Job[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch similar jobs
  useEffect(() => {
    const fetchSimilarJobs = async () => {
      setLoading(true)
      try {
        // Create search criteria based on current job
        const searchCriteria = {
          category: currentJob.tags?.[0] || '',
          location: currentJob.location.remote ? '' : `${currentJob.location.city}, ${currentJob.location.state}`,
          type: currentJob.type,
          level: currentJob.level,
          remote: currentJob.location.remote,
          excludeJobId: currentJob._id
        }

        // Search for similar jobs
        await searchJobs(searchCriteria)
        
        // Filter and score jobs based on similarity
        const filteredJobs = jobs
          .filter(job => job._id !== currentJob._id)
          .map(job => ({
            ...job,
            matchScore: calculateMatchScore(job, currentJob)
          }))
          .sort((a, b) => (b.matchScore || 0) - (a.matchScore || 0))
          .slice(0, maxJobs)

        setSimilarJobs(filteredJobs)
      } catch (error) {
        console.error('Failed to fetch similar jobs:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSimilarJobs()
  }, [currentJob, maxJobs, searchJobs, jobs])

  // Calculate match score between jobs
  const calculateMatchScore = (job: Job, currentJob: Job): number => {
    let score = 0
    
    // Company match (highest weight)
    if (job.company._id === currentJob.company._id) score += 30
    
    // Location match
    if (job.location.remote === currentJob.location.remote) score += 20
    if (!job.location.remote && !currentJob.location.remote) {
      if (job.location.city === currentJob.location.city) score += 15
      if (job.location.state === currentJob.location.state) score += 10
    }
    
    // Job type match
    if (job.type === currentJob.type) score += 15
    
    // Level match
    if (job.level === currentJob.level) score += 10
    
    // Title similarity (basic keyword matching)
    const jobTitleWords = job.title.toLowerCase().split(' ')
    const currentTitleWords = currentJob.title.toLowerCase().split(' ')
    const commonWords = jobTitleWords.filter(word => 
      currentTitleWords.includes(word) && word.length > 3
    )
    score += Math.min(commonWords.length * 5, 20)
    
    // Salary range overlap
    if (job.salary && currentJob.salary) {
      const jobMin = job.salary.min || 0
      const jobMax = job.salary.max || Number.MAX_SAFE_INTEGER
      const currentMin = currentJob.salary.min || 0
      const currentMax = currentJob.salary.max || Number.MAX_SAFE_INTEGER
      
      if (jobMin <= currentMax && jobMax >= currentMin) {
        score += 10
      }
    }
    
    return Math.min(score, 100)
  }

  const handleSaveJob = async (jobId: string) => {
    try {
      await saveJob(jobId)
    } catch (error) {
      console.error('Failed to save job:', error)
    }
  }

  const handleUnsaveJob = async (jobId: string) => {
    try {
      await unsaveJob(jobId)
    } catch (error) {
      console.error('Failed to unsave job:', error)
    }
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5" />
            <span>Similar Jobs</span>
          </CardTitle>
          <CardDescription>
            Jobs you might be interested in
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <JobCardSkeleton key={index} />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (similarJobs.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5" />
            <span>Similar Jobs</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <TrendingUp className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium mb-2">No similar jobs found</h3>
            <p className="text-sm text-muted-foreground">
              Try browsing our job categories to find more opportunities.
            </p>
            <Button variant="outline" className="mt-4">
              Browse All Jobs
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Sparkles className="w-5 h-5" />
              <span>Similar Jobs</span>
            </CardTitle>
            <CardDescription>
              {similarJobs.length} jobs that match your interests
            </CardDescription>
          </div>
          <Button variant="outline" size="sm">
            View All
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {similarJobs.map((job) => (
            <JobCard
              key={job._id}
              job={job}
              isSaved={savedJobs.includes(job._id)}
              onSave={handleSaveJob}
              onUnsave={handleUnsaveJob}
              showMatchScore={true}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
