import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { Application } from '@/lib/models/application.model'
import { User } from '@/lib/models/user.model'
import { Job } from '@/lib/models/job.model'

export async function PUT(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { applicationIds, status, reason, notes } = await request.json()

    // Validate input
    if (!Array.isArray(applicationIds) || applicationIds.length === 0) {
      return NextResponse.json(
        { error: 'applicationIds must be a non-empty array' },
        { status: 400 }
      )
    }

    if (applicationIds.length > 100) {
      return NextResponse.json(
        { error: 'Cannot update more than 100 applications at once' },
        { status: 400 }
      )
    }

    // Validate status
    const validStatuses = ['pending', 'reviewing', 'interviewed', 'offered', 'hired', 'rejected']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: ' + validStatuses.join(', ') },
        { status: 400 }
      )
    }

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Find all applications and verify ownership
    const applications = await Application.find({
      _id: { $in: applicationIds },
      company: companyId
    }).populate('job', 'title')

    if (applications.length === 0) {
      return NextResponse.json(
        { error: 'No valid applications found for your company' },
        { status: 404 }
      )
    }

    if (applications.length !== applicationIds.length) {
      return NextResponse.json(
        { 
          error: 'Some applications not found or do not belong to your company',
          found: applications.length,
          requested: applicationIds.length
        },
        { status: 400 }
      )
    }

    // Validate status transitions for all applications
    const validTransitions: Record<string, string[]> = {
      'pending': ['reviewing', 'rejected'],
      'reviewing': ['interviewed', 'offered', 'hired', 'rejected'],
      'interviewed': ['offered', 'hired', 'rejected', 'reviewing'],
      'offered': ['hired', 'rejected'],
      'hired': [],
      'rejected': []
    }

    const invalidTransitions: Array<{ id: string, currentStatus: string, targetStatus: string }> = []
    
    for (const app of applications) {
      const currentStatus = app.status
      if (!validTransitions[currentStatus]?.includes(status)) {
        invalidTransitions.push({
          id: app._id.toString(),
          currentStatus,
          targetStatus: status
        })
      }
    }

    if (invalidTransitions.length > 0) {
      return NextResponse.json(
        { 
          error: 'Invalid status transitions detected',
          invalidTransitions
        },
        { status: 400 }
      )
    }

    // Prepare bulk update data
    const updateData: any = {
      status,
      updatedAt: new Date(),
      updatedBy: userId
    }

    // Add status-specific data
    switch (status) {
      case 'reviewing':
        updateData.reviewedAt = new Date()
        updateData.reviewedBy = userId
        if (notes) updateData.reviewNotes = notes
        break
        
      case 'rejected':
        updateData.rejectedAt = new Date()
        updateData.rejectedBy = userId
        if (reason) updateData.rejectionReason = reason
        if (notes) updateData.rejectionNotes = notes
        break
        
      case 'hired':
        updateData.hiredAt = new Date()
        updateData.hiredBy = userId
        if (notes) updateData.hireNotes = notes
        break
    }

    // Update all applications
    const results = []
    const jobStatsUpdates: Record<string, { hired: number, rejected: number }> = {}

    for (const app of applications) {
      const currentStatus = app.status
      
      // Add to status history
      const statusHistoryEntry = {
        fromStatus: currentStatus,
        toStatus: status,
        changedBy: userId,
        reason: reason || undefined,
        notes: notes || undefined,
        timestamp: new Date()
      }

      const appUpdateData = {
        ...updateData,
        $push: {
          statusHistory: statusHistoryEntry
        }
      }

      const updatedApp = await Application.findByIdAndUpdate(
        app._id,
        appUpdateData,
        { new: true, runValidators: true }
      ).populate('job', 'title')
       .populate('client', 'user')
       .populate({
         path: 'client',
         populate: {
           path: 'user',
           select: 'profile.firstName profile.lastName email'
         }
       })

      results.push({
        id: updatedApp._id,
        status: updatedApp.status,
        previousStatus: currentStatus,
        job: {
          id: updatedApp.job._id,
          title: updatedApp.job.title
        },
        candidate: {
          id: updatedApp.client._id,
          name: `${updatedApp.client.user.profile.firstName} ${updatedApp.client.user.profile.lastName}`,
          email: updatedApp.client.user.email
        }
      })

      // Track job stats updates
      if (status === 'hired' || status === 'rejected') {
        const jobId = app.job._id.toString()
        if (!jobStatsUpdates[jobId]) {
          jobStatsUpdates[jobId] = { hired: 0, rejected: 0 }
        }
        jobStatsUpdates[jobId][status]++
      }
    }

    // Update job statistics
    for (const [jobId, stats] of Object.entries(jobStatsUpdates)) {
      const updateFields: any = {}
      if (stats.hired > 0) updateFields['stats.hiredCount'] = stats.hired
      if (stats.rejected > 0) updateFields['stats.rejectedCount'] = stats.rejected

      if (Object.keys(updateFields).length > 0) {
        await Job.findByIdAndUpdate(jobId, {
          $inc: updateFields
        })
      }
    }

    // Log the bulk operation
    console.log(`Bulk status update: ${applications.length} applications updated to ${status} by user ${userId}`)

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${results.length} applications to ${status}`,
      data: {
        updatedCount: results.length,
        status,
        applications: results
      }
    })

  } catch (error) {
    console.error('Bulk update applications error:', error)
    return NextResponse.json(
      { error: 'Failed to update applications' },
      { status: 500 }
    )
  }
}

// DELETE method for bulk rejection
export async function DELETE(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const { applicationIds, reason } = await request.json()

    // Validate input
    if (!Array.isArray(applicationIds) || applicationIds.length === 0) {
      return NextResponse.json(
        { error: 'applicationIds must be a non-empty array' },
        { status: 400 }
      )
    }

    if (applicationIds.length > 100) {
      return NextResponse.json(
        { error: 'Cannot reject more than 100 applications at once' },
        { status: 400 }
      )
    }

    // Get user's company
    const user = await User.findById(userId).populate('companyId')
    if (!user || !user.companyId) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const companyId = user.companyId._id

    // Find applications that can be rejected
    const applications = await Application.find({
      _id: { $in: applicationIds },
      company: companyId,
      status: { $nin: ['hired', 'rejected'] } // Can't reject already hired or rejected
    })

    if (applications.length === 0) {
      return NextResponse.json(
        { error: 'No valid applications found to reject' },
        { status: 404 }
      )
    }

    // Bulk reject applications
    const updateData = {
      status: 'rejected',
      rejectedAt: new Date(),
      rejectedBy: userId,
      rejectionReason: reason || 'Bulk rejection',
      updatedAt: new Date(),
      updatedBy: userId
    }

    const results = []
    for (const app of applications) {
      const statusHistoryEntry = {
        fromStatus: app.status,
        toStatus: 'rejected',
        changedBy: userId,
        reason: reason || 'Bulk rejection',
        timestamp: new Date()
      }

      const updatedApp = await Application.findByIdAndUpdate(
        app._id,
        {
          ...updateData,
          $push: { statusHistory: statusHistoryEntry }
        },
        { new: true }
      ).populate('job', 'title')
       .populate('client', 'user')
       .populate({
         path: 'client',
         populate: {
           path: 'user',
           select: 'profile.firstName profile.lastName email'
         }
       })

      results.push({
        id: updatedApp._id,
        job: updatedApp.job.title,
        candidate: `${updatedApp.client.user.profile.firstName} ${updatedApp.client.user.profile.lastName}`
      })

      // Update job stats
      await Job.findByIdAndUpdate(app.job, {
        $inc: { 'stats.rejectedCount': 1 }
      })
    }

    console.log(`Bulk rejection: ${results.length} applications rejected by user ${userId}`)

    return NextResponse.json({
      success: true,
      message: `Successfully rejected ${results.length} applications`,
      data: {
        rejectedCount: results.length,
        applications: results
      }
    })

  } catch (error) {
    console.error('Bulk reject applications error:', error)
    return NextResponse.json(
      { error: 'Failed to reject applications' },
      { status: 500 }
    )
  }
}
