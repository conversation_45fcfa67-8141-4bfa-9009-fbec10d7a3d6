'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  MapPin,
  Star,
  DollarSign,
  Clock,
  Eye,
  Heart,
  MessageCircle,
  Briefcase,
  CheckCircle,
  TrendingUp,
  Award,
  Users,
  Calendar,
  ExternalLink
} from 'lucide-react'

interface Talent {
  id: number
  name: string
  title: string
  location: string
  avatar: string
  rating: number
  experience: number
  hourlyRate: number
  skills: string[]
  bio: string
  availability: string
  projects: number
  reviews: number
  responseTime: string
  successRate: number
  portfolio?: string[]
  languages?: string[]
  timezone?: string
}

interface TalentCardProps {
  talent: Talent
  viewMode: 'grid' | 'list'
  onViewProfile: (talent: Talent) => void
  onContact: (talent: Talent) => void
  onSave?: (talent: Talent) => void
}

export function TalentCard({ 
  talent, 
  viewMode, 
  onViewProfile, 
  onContact, 
  onSave 
}: TalentCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  const getAvailabilityColor = (availability: string) => {
    switch (availability.toLowerCase()) {
      case 'available':
        return 'bg-green-500'
      case 'busy':
        return 'bg-yellow-500'
      case 'unavailable':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getExperienceLevel = (years: number) => {
    if (years < 2) return 'Junior'
    if (years < 5) return 'Mid-level'
    if (years < 8) return 'Senior'
    return 'Expert'
  }

  const handleSave = () => {
    setIsSaved(!isSaved)
    if (onSave) onSave(talent)
  }

  if (viewMode === 'list') {
    return (
      <>
        {/* List View - Hidden on Mobile */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="hidden md:block"
        >
        <Card className={`card-premium transition-all duration-300 ${
          isHovered ? 'scale-[1.02] theme-glow' : ''
        }`}>
          <CardContent className="p-6">
            <div className="flex items-start space-x-6">
              {/* Avatar and Status */}
              <div className="relative">
                <Avatar className="w-20 h-20 border-2 border-primary/20">
                  <AvatarImage src={talent.avatar} alt={talent.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-lg">
                    {talent.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className={`absolute -bottom-1 -right-1 w-6 h-6 ${getAvailabilityColor(talent.availability)} rounded-full border-2 border-background flex items-center justify-center`}>
                  <div className="w-2 h-2 bg-white rounded-full" />
                </div>
              </div>

              {/* Main Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="text-xl font-bold mb-1 truncate">{talent.name}</h3>
                    <p className="text-primary font-medium mb-2">{talent.title}</p>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{talent.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{talent.rating}</span>
                        <span className="text-muted-foreground">({talent.reviews} reviews)</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Briefcase className="w-4 h-4" />
                        <span>{talent.experience} years • {getExperienceLevel(talent.experience)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSave}
                      className={`p-2 ${isSaved ? 'text-red-500' : 'text-muted-foreground'}`}
                    >
                      <Heart className={`w-4 h-4 ${isSaved ? 'fill-current' : ''}`} />
                    </Button>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">${talent.hourlyRate}</div>
                      <div className="text-xs text-muted-foreground">per hour</div>
                    </div>
                  </div>
                </div>

                <p className="text-muted-foreground mb-4 line-clamp-2">{talent.bio}</p>

                {/* Skills */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {talent.skills.slice(0, 6).map((skill) => (
                    <Badge key={skill} variant="secondary" className="theme-glow">
                      {skill}
                    </Badge>
                  ))}
                  {talent.skills.length > 6 && (
                    <Badge variant="outline">+{talent.skills.length - 6} more</Badge>
                  )}
                </div>

                {/* Stats and Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>{talent.successRate}% success</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>Responds in {talent.responseTime}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{talent.projects} projects</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onContact(talent)}
                    >
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Contact
                    </Button>
                    <Button
                      size="sm"
                      className="button-premium"
                      onClick={() => onViewProfile(talent)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Quick View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        window.open(`/talent/${talent.id}`, '_blank')
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Full Profile
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        </motion.div>

        {/* Mobile Grid View Fallback - Shown on Mobile when list view is selected */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="block md:hidden"
        >
          <Card className={`card-premium h-full transition-all duration-300 ${
            isHovered ? 'scale-105 theme-glow' : ''
          }`}>
            <CardContent className="p-4">
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="relative">
                  <Avatar className="w-12 h-12 border-2 border-primary/20">
                    <AvatarImage src={talent.avatar} alt={talent.name} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm">
                      {talent.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className={`absolute -bottom-1 -right-1 w-4 h-4 ${getAvailabilityColor(talent.availability)} rounded-full border-2 border-background flex items-center justify-center`}>
                    <div className="w-1 h-1 bg-white rounded-full" />
                  </div>
                </div>

                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSave}
                    className={`p-1.5 ${isSaved ? 'text-red-500' : 'text-muted-foreground'}`}
                  >
                    <Heart className={`w-3 h-3 ${isSaved ? 'fill-current' : ''}`} />
                  </Button>
                  <div className="text-right">
                    <div className="text-sm font-bold text-primary">${talent.hourlyRate}</div>
                    <div className="text-xs text-muted-foreground">per hour</div>
                  </div>
                </div>
              </div>

              {/* Name and Title */}
              <div className="mb-3">
                <h3 className="text-base font-bold mb-1 truncate">{talent.name}</h3>
                <p className="text-primary font-medium text-xs mb-2 truncate">{talent.title}</p>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{talent.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                    <span>{talent.rating}</span>
                  </div>
                </div>
              </div>

              {/* Bio */}
              <p className="text-muted-foreground text-xs mb-3 line-clamp-2">{talent.bio}</p>

              {/* Skills */}
              <div className="flex flex-wrap gap-1 mb-3">
                {talent.skills.slice(0, 3).map((skill) => (
                  <Badge key={skill} variant="secondary" className="text-xs theme-glow">
                    {skill}
                  </Badge>
                ))}
                {talent.skills.length > 3 && (
                  <Badge variant="outline" className="text-xs">+{talent.skills.length - 3}</Badge>
                )}
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  className="button-premium flex-1 text-xs"
                  onClick={() => onContact(talent)}
                >
                  <MessageCircle className="w-3 h-3 mr-1" />
                  Contact
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewProfile(talent)}
                  className="flex-1 text-xs"
                >
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </>
    )
  }

  // Grid View
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={`card-premium h-full transition-all duration-300 ${
        isHovered ? 'scale-105 theme-glow' : ''
      }`}>
        <CardContent className="p-4 md:p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-3 md:mb-4">
            <div className="relative">
              <Avatar className="w-12 h-12 md:w-16 md:h-16 border-2 border-primary/20">
                <AvatarImage src={talent.avatar} alt={talent.name} />
                <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm md:text-base">
                  {talent.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className={`absolute -bottom-1 -right-1 w-4 h-4 md:w-5 md:h-5 ${getAvailabilityColor(talent.availability)} rounded-full border-2 border-background flex items-center justify-center`}>
                <div className="w-1 h-1 md:w-1.5 md:h-1.5 bg-white rounded-full" />
              </div>
            </div>

            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSave}
                className={`p-1.5 md:p-2 ${isSaved ? 'text-red-500' : 'text-muted-foreground'}`}
              >
                <Heart className={`w-3 h-3 md:w-4 md:h-4 ${isSaved ? 'fill-current' : ''}`} />
              </Button>
              <div className="text-right">
                <div className="text-base md:text-xl font-bold text-primary">${talent.hourlyRate}</div>
                <div className="text-xs text-muted-foreground">per hour</div>
              </div>
            </div>
          </div>

          {/* Name and Title */}
          <div className="mb-3">
            <h3 className="text-lg font-bold mb-1 truncate">{talent.name}</h3>
            <p className="text-primary font-medium text-sm mb-2">{talent.title}</p>
            <div className="flex items-center space-x-3 text-xs text-muted-foreground">
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>{talent.location}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                <span>{talent.rating}</span>
              </div>
            </div>
          </div>

          {/* Bio */}
          <p className="text-muted-foreground text-sm mb-4 line-clamp-3">{talent.bio}</p>

          {/* Skills - Reduced on mobile */}
          <div className="flex flex-wrap gap-1 mb-3 md:mb-4">
            {talent.skills.slice(0, 3).map((skill) => (
              <Badge key={skill} variant="secondary" className="text-xs theme-glow">
                {skill}
              </Badge>
            ))}
            {talent.skills.length > 3 && (
              <Badge variant="outline" className="text-xs">+{talent.skills.length - 3}</Badge>
            )}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-2 mb-4 text-xs">
            <div className="flex items-center space-x-1 text-muted-foreground">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>{talent.successRate}% success</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Users className="w-3 h-3" />
              <span>{talent.projects} projects</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>{talent.responseTime}</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Briefcase className="w-3 h-3" />
              <span>{talent.experience} years</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col space-y-2">
            <Button
              size="sm"
              className="w-full button-premium"
              onClick={() => onViewProfile(talent)}
            >
              <Eye className="w-4 h-4 mr-2" />
              Quick View
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => onContact(talent)}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => {
                window.open(`/talent/${talent.id}`, '_blank')
              }}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Full Profile
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
