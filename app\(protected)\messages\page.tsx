'use client'

import React from 'react'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { MessagingInterface } from '@/components/messaging/messaging-interface'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageSquare, Users, Clock } from 'lucide-react'

function MessagesContent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Messages</h1>
              <p className="text-muted-foreground">
                Communicate with candidates and recruiters
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Welcome Card */}
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <CardTitle>Message Center</CardTitle>
                  <CardDescription>
                    Stay connected throughout the hiring process
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Users className="w-4 h-4 text-blue-600" />
                  </div>
                  <h4 className="font-medium mb-1">Direct Communication</h4>
                  <p className="text-sm text-muted-foreground">
                    Chat directly with candidates or recruiters
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Clock className="w-4 h-4 text-green-600" />
                  </div>
                  <h4 className="font-medium mb-1">Real-time Updates</h4>
                  <p className="text-sm text-muted-foreground">
                    Get instant notifications for new messages
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <MessageSquare className="w-4 h-4 text-purple-600" />
                  </div>
                  <h4 className="font-medium mb-1">Organized Conversations</h4>
                  <p className="text-sm text-muted-foreground">
                    All conversations organized by job application
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Messaging Interface */}
          <Card>
            <CardContent className="p-0">
              <MessagingInterface />
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}

export default function MessagesPage() {
  return (
    <ProtectedRoute>
      <MessagesContent />
    </ProtectedRoute>
  )
}
