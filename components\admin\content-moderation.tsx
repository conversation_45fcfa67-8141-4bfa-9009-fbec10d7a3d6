'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  ArrowLeft,
  AlertTriangle,
  Flag,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Briefcase,
  MessageSquare,
  Users,
  Building
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ContentModerationProps {
  className?: string
}

export function ContentModeration({ className }: ContentModerationProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('reports')

  // Mock data - in real implementation, this would come from API
  const mockReports = [
    {
      id: '1',
      type: 'job',
      title: 'Senior Developer Position',
      reportReason: 'Inappropriate content',
      reportedBy: '<EMAIL>',
      status: 'pending',
      createdAt: new Date('2024-01-15'),
      company: 'Tech Corp'
    },
    {
      id: '2',
      type: 'company',
      title: 'Fake Company Profile',
      reportReason: 'Fraudulent information',
      reportedBy: '<EMAIL>',
      status: 'investigating',
      createdAt: new Date('2024-01-14'),
      company: 'Suspicious Inc'
    }
  ]

  const mockFlaggedJobs = [
    {
      id: '1',
      title: 'Remote Software Engineer',
      company: 'StartupXYZ',
      flagReason: 'Suspicious salary range',
      status: 'flagged',
      createdAt: new Date('2024-01-16')
    },
    {
      id: '2',
      title: 'Marketing Manager',
      company: 'Big Corp',
      flagReason: 'Potential discrimination',
      status: 'under_review',
      createdAt: new Date('2024-01-15')
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge className="bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </Badge>
        )
      case 'investigating':
        return (
          <Badge className="bg-blue-100 text-blue-800">
            <Eye className="w-3 h-3 mr-1" />
            Investigating
          </Badge>
        )
      case 'resolved':
        return (
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Resolved
          </Badge>
        )
      case 'dismissed':
        return (
          <Badge className="bg-gray-100 text-gray-800">
            <XCircle className="w-3 h-3 mr-1" />
            Dismissed
          </Badge>
        )
      case 'flagged':
        return (
          <Badge className="bg-red-100 text-red-800">
            <Flag className="w-3 h-3 mr-1" />
            Flagged
          </Badge>
        )
      case 'under_review':
        return (
          <Badge className="bg-orange-100 text-orange-800">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Under Review
          </Badge>
        )
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800">
            {status}
          </Badge>
        )
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/admin')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Content Moderation</h1>
            <p className="text-muted-foreground">
              Review reports and manage flagged content
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Reports</p>
                <p className="text-2xl font-bold">12</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Flagged Jobs</p>
                <p className="text-2xl font-bold">8</p>
              </div>
              <Flag className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Under Review</p>
                <p className="text-2xl font-bold">5</p>
              </div>
              <Eye className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Resolved Today</p>
                <p className="text-2xl font-bold">15</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="flagged-jobs">Flagged Jobs</TabsTrigger>
          <TabsTrigger value="flagged-companies">Flagged Companies</TabsTrigger>
          <TabsTrigger value="user-reports">User Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Reports</CardTitle>
              <CardDescription>User-submitted reports requiring review</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-orange-600 rounded-lg flex items-center justify-center text-white">
                        {report.type === 'job' ? <Briefcase className="w-5 h-5" /> : <Building className="w-5 h-5" />}
                      </div>
                      <div>
                        <h4 className="font-medium">{report.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          Reported by: {report.reportedBy}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Reason: {report.reportReason}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(report.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(report.status)}
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-2" />
                        Review
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="flagged-jobs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Flagged Job Postings</CardTitle>
              <CardDescription>Jobs flagged by automated systems or manual review</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockFlaggedJobs.map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white">
                        <Briefcase className="w-5 h-5" />
                      </div>
                      <div>
                        <h4 className="font-medium">{job.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          Company: {job.company}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Flag reason: {job.flagReason}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(job.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(job.status)}
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-2" />
                        Review
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="flagged-companies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Flagged Companies</CardTitle>
              <CardDescription>Companies flagged for suspicious activity or policy violations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Building className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Flagged Companies</h3>
                <p className="text-muted-foreground">
                  All companies are currently in good standing.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="user-reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Behavior Reports</CardTitle>
              <CardDescription>Reports about user conduct and policy violations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No User Reports</h3>
                <p className="text-muted-foreground">
                  No user behavior reports at this time.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common moderation tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <AlertTriangle className="w-6 h-6" />
              <span>Review All Pending</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <Flag className="w-6 h-6" />
              <span>Bulk Flag Review</span>
            </Button>
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center space-y-2">
              <MessageSquare className="w-6 h-6" />
              <span>Generate Report</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
