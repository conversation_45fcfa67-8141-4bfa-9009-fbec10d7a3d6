import { z } from 'zod'

// Base Settings Interfaces
export interface EmailNotifications {
  newApplications: boolean
  applicationUpdates: boolean
  interviewReminders: boolean
  jobExpiring: boolean
  weeklyReports: boolean
  marketingEmails: boolean
}

export interface PushNotifications {
  newApplications: boolean
  urgentUpdates: boolean
  dailyDigest: boolean
  teamMentions: boolean
}

export interface BrandingSettings {
  primaryColor?: string
  secondaryColor?: string
  customLogo?: string
  customBanner?: string
  customTheme?: 'light' | 'dark' | 'auto'
}

export interface TeamSettings {
  allowTeamCollaboration: boolean
  requireApprovalForJobPosting: boolean
  allowRecruiterAccess: boolean
  teamNotifications: boolean
}

export interface ATSIntegration {
  enabled: boolean
  provider?: string
  apiKey?: string
}

export interface CalendarIntegration {
  enabled: boolean
  provider?: 'google' | 'outlook' | 'calendly'
  settings?: Record<string, unknown>
}

export interface SlackIntegration {
  enabled: boolean
  webhookUrl?: string
  channels?: string[]
}

export interface IntegrationSettings {
  atsIntegration?: ATSIntegration
  calendarIntegration?: CalendarIntegration
  slackIntegration?: SlackIntegration
}

export interface AdvancedSettings {
  dataRetentionPeriod: number
  allowAnalytics: boolean
  allowCookies: boolean
  timezone: string
  language: string
  currency: string
}

// Main Company Settings Interface
export interface CompanySettings {
  // Profile & Privacy Settings
  allowPublicProfile: boolean
  showSalaryRanges: boolean
  allowDirectContact: boolean
  showCompanyStats: boolean
  
  // Application Management Settings
  autoRejectAfterDays?: number
  requireCoverLetter: boolean
  allowRemoteApplications: boolean
  autoResponseEnabled: boolean
  applicationDeadlineReminder: boolean
  
  // Notification Settings
  emailNotifications: EmailNotifications
  pushNotifications: PushNotifications
  
  // Branding & Customization
  branding: BrandingSettings
  
  // Team & Collaboration Settings
  teamSettings: TeamSettings
  
  // Integration Settings
  integrations: IntegrationSettings
  
  // Advanced Settings
  advanced: AdvancedSettings
}

// Partial settings for updates
export type CompanySettingsUpdate = Partial<CompanySettings>

// Settings categories for organized updates
export type ProfilePrivacySettings = Pick<CompanySettings, 
  'allowPublicProfile' | 'showSalaryRanges' | 'allowDirectContact' | 'showCompanyStats'>

export type ApplicationSettings = Pick<CompanySettings, 
  'autoRejectAfterDays' | 'requireCoverLetter' | 'allowRemoteApplications' | 
  'autoResponseEnabled' | 'applicationDeadlineReminder'>

export type NotificationSettings = Pick<CompanySettings, 
  'emailNotifications' | 'pushNotifications'>

// API Request/Response Types
export interface GetCompanySettingsResponse {
  success: boolean
  data: CompanySettings
  message?: string
}

export interface UpdateCompanySettingsRequest {
  settings: CompanySettingsUpdate
}

export interface UpdateCompanySettingsResponse {
  success: boolean
  data: CompanySettings
  message?: string
}

// Validation Schemas using Zod
export const emailNotificationsSchema = z.object({
  newApplications: z.boolean().default(true),
  applicationUpdates: z.boolean().default(true),
  interviewReminders: z.boolean().default(true),
  jobExpiring: z.boolean().default(true),
  weeklyReports: z.boolean().default(false),
  marketingEmails: z.boolean().default(false)
})

export const pushNotificationsSchema = z.object({
  newApplications: z.boolean().default(true),
  urgentUpdates: z.boolean().default(true),
  dailyDigest: z.boolean().default(false),
  teamMentions: z.boolean().default(true)
})

export const brandingSettingsSchema = z.object({
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
  secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i).optional(),
  customLogo: z.string().url().optional(),
  customBanner: z.string().url().optional(),
  customTheme: z.enum(['light', 'dark', 'auto']).default('light').optional()
})

export const teamSettingsSchema = z.object({
  allowTeamCollaboration: z.boolean().default(true),
  requireApprovalForJobPosting: z.boolean().default(false),
  allowRecruiterAccess: z.boolean().default(true),
  teamNotifications: z.boolean().default(true)
})

export const atsIntegrationSchema = z.object({
  enabled: z.boolean().default(false),
  provider: z.string().optional(),
  apiKey: z.string().optional()
})

export const calendarIntegrationSchema = z.object({
  enabled: z.boolean().default(false),
  provider: z.enum(['google', 'outlook', 'calendly']).optional(),
  settings: z.record(z.unknown()).optional()
})

export const slackIntegrationSchema = z.object({
  enabled: z.boolean().default(false),
  webhookUrl: z.string().url().optional(),
  channels: z.array(z.string()).optional()
})

export const integrationSettingsSchema = z.object({
  atsIntegration: atsIntegrationSchema.optional(),
  calendarIntegration: calendarIntegrationSchema.optional(),
  slackIntegration: slackIntegrationSchema.optional()
})

export const advancedSettingsSchema = z.object({
  dataRetentionPeriod: z.number().min(30).max(2555).default(730),
  allowAnalytics: z.boolean().default(true),
  allowCookies: z.boolean().default(true),
  timezone: z.string().default('UTC'),
  language: z.enum(['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja', 'ko']).default('en'),
  currency: z.enum(['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY', 'CNY', 'INR']).default('USD')
})

// Main settings validation schema
export const companySettingsSchema = z.object({
  // Profile & Privacy Settings
  allowPublicProfile: z.boolean().default(true),
  showSalaryRanges: z.boolean().default(true),
  allowDirectContact: z.boolean().default(true),
  showCompanyStats: z.boolean().default(true),
  
  // Application Management Settings
  autoRejectAfterDays: z.number().min(1).max(365).optional(),
  requireCoverLetter: z.boolean().default(false),
  allowRemoteApplications: z.boolean().default(true),
  autoResponseEnabled: z.boolean().default(true),
  applicationDeadlineReminder: z.boolean().default(true),
  
  // Notification Settings
  emailNotifications: emailNotificationsSchema.default({}),
  pushNotifications: pushNotificationsSchema.default({}),
  
  // Branding & Customization
  branding: brandingSettingsSchema.default({}),
  
  // Team & Collaboration Settings
  teamSettings: teamSettingsSchema.default({}),
  
  // Integration Settings
  integrations: integrationSettingsSchema.default({}),
  
  // Advanced Settings
  advanced: advancedSettingsSchema.default({})
})

// Partial schema for updates
export const updateCompanySettingsSchema = companySettingsSchema.partial()

// Request validation schemas
export const updateCompanySettingsRequestSchema = z.object({
  settings: updateCompanySettingsSchema
})

// Type exports from schemas
export type CompanySettingsSchema = z.infer<typeof companySettingsSchema>
export type UpdateCompanySettingsSchema = z.infer<typeof updateCompanySettingsSchema>
export type UpdateCompanySettingsRequestSchema = z.infer<typeof updateCompanySettingsRequestSchema>
