// Test script for Company Settings CRUD operations
// Run with: node test-company-settings.js

const BASE_URL = 'http://localhost:3000'

// Mock authentication token (replace with actual token in real testing)
const AUTH_TOKEN = 'mock-jwt-token'

// Mock company ID (replace with actual company ID in real testing)
const COMPANY_ID = '507f1f77bcf86cd799439011'

// Test data for settings
const testSettings = {
  // Profile & Privacy Settings
  allowPublicProfile: true,
  showSalaryRanges: true,
  allowDirectContact: true,
  showCompanyStats: true,
  
  // Application Management Settings
  autoRejectAfterDays: 30,
  requireCoverLetter: false,
  allowRemoteApplications: true,
  autoResponseEnabled: true,
  applicationDeadlineReminder: true,
  
  // Notification Settings
  emailNotifications: {
    newApplications: true,
    applicationUpdates: true,
    interviewReminders: true,
    jobExpiring: true,
    weeklyReports: false,
    marketingEmails: false
  },
  pushNotifications: {
    newApplications: true,
    urgentUpdates: true,
    dailyDigest: false,
    teamMentions: true
  },
  
  // Branding & Customization
  branding: {
    primaryColor: '#3b82f6',
    secondaryColor: '#64748b',
    customTheme: 'light'
  },
  
  // Team & Collaboration Settings
  teamSettings: {
    allowTeamCollaboration: true,
    requireApprovalForJobPosting: false,
    allowRecruiterAccess: true,
    teamNotifications: true
  },
  
  // Integration Settings
  integrations: {
    atsIntegration: {
      enabled: false
    },
    calendarIntegration: {
      enabled: false
    },
    slackIntegration: {
      enabled: false
    }
  },
  
  // Advanced Settings
  advanced: {
    dataRetentionPeriod: 730,
    allowAnalytics: true,
    allowCookies: true,
    timezone: 'UTC',
    language: 'en',
    currency: 'USD'
  }
}

// Helper function to make API requests
async function makeRequest(method, endpoint, data = null) {
  const url = `${BASE_URL}${endpoint}`
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${AUTH_TOKEN}`,
      'x-user-id': '507f1f77bcf86cd799439012' // Mock user ID
    }
  }
  
  if (data) {
    options.body = JSON.stringify(data)
  }
  
  try {
    console.log(`\n🔄 ${method} ${endpoint}`)
    if (data) {
      console.log('📤 Request data:', JSON.stringify(data, null, 2))
    }
    
    const response = await fetch(url, options)
    const responseData = await response.json()
    
    console.log(`📊 Status: ${response.status}`)
    console.log('📥 Response:', JSON.stringify(responseData, null, 2))
    
    return { status: response.status, data: responseData, ok: response.ok }
  } catch (error) {
    console.error(`❌ Request failed:`, error.message)
    return { status: 0, data: null, ok: false, error: error.message }
  }
}

// Test functions
async function testGetSettings() {
  console.log('\n🧪 Testing GET company settings...')
  const result = await makeRequest('GET', `/api/v1/companies/${COMPANY_ID}/settings`)
  
  if (result.ok) {
    console.log('✅ GET settings successful')
    return result.data
  } else {
    console.log('❌ GET settings failed')
    return null
  }
}

async function testUpdateSettings() {
  console.log('\n🧪 Testing PUT company settings...')
  const result = await makeRequest('PUT', `/api/v1/companies/${COMPANY_ID}/settings`, {
    settings: testSettings
  })
  
  if (result.ok) {
    console.log('✅ PUT settings successful')
    return result.data
  } else {
    console.log('❌ PUT settings failed')
    return null
  }
}

async function testPartialUpdate() {
  console.log('\n🧪 Testing PATCH company settings (partial update)...')
  const partialUpdate = {
    settings: {
      emailNotifications: {
        newApplications: false,
        weeklyReports: true
      }
    }
  }
  
  const result = await makeRequest('PATCH', `/api/v1/companies/${COMPANY_ID}/settings?section=emailNotifications`, partialUpdate)
  
  if (result.ok) {
    console.log('✅ PATCH settings successful')
    return result.data
  } else {
    console.log('❌ PATCH settings failed')
    return null
  }
}

async function testResetSettings() {
  console.log('\n🧪 Testing POST reset company settings...')
  const result = await makeRequest('POST', `/api/v1/companies/${COMPANY_ID}/settings?action=reset`)
  
  if (result.ok) {
    console.log('✅ POST reset settings successful')
    return result.data
  } else {
    console.log('❌ POST reset settings failed')
    return null
  }
}

async function testValidationErrors() {
  console.log('\n🧪 Testing validation errors...')
  
  // Test invalid color format
  const invalidSettings = {
    settings: {
      branding: {
        primaryColor: 'invalid-color',
        secondaryColor: '#xyz123'
      }
    }
  }
  
  const result = await makeRequest('PUT', `/api/v1/companies/${COMPANY_ID}/settings`, invalidSettings)
  
  if (!result.ok && result.status === 400) {
    console.log('✅ Validation error handling working correctly')
    return true
  } else {
    console.log('❌ Validation error handling failed')
    return false
  }
}

async function testUnauthorizedAccess() {
  console.log('\n🧪 Testing unauthorized access...')
  
  const url = `${BASE_URL}/api/v1/companies/${COMPANY_ID}/settings`
  const options = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
      // No Authorization header
    }
  }
  
  try {
    const response = await fetch(url, options)
    const responseData = await response.json()
    
    console.log(`📊 Status: ${response.status}`)
    console.log('📥 Response:', JSON.stringify(responseData, null, 2))
    
    if (response.status === 401) {
      console.log('✅ Unauthorized access properly blocked')
      return true
    } else {
      console.log('❌ Unauthorized access not properly blocked')
      return false
    }
  } catch (error) {
    console.error(`❌ Request failed:`, error.message)
    return false
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Company Settings API Tests')
  console.log('=====================================')
  
  const results = {
    getSettings: false,
    updateSettings: false,
    partialUpdate: false,
    resetSettings: false,
    validationErrors: false,
    unauthorizedAccess: false
  }
  
  try {
    // Test basic CRUD operations
    const initialSettings = await testGetSettings()
    results.getSettings = !!initialSettings
    
    const updatedSettings = await testUpdateSettings()
    results.updateSettings = !!updatedSettings
    
    const partiallyUpdated = await testPartialUpdate()
    results.partialUpdate = !!partiallyUpdated
    
    const resetSettings = await testResetSettings()
    results.resetSettings = !!resetSettings
    
    // Test error handling
    results.validationErrors = await testValidationErrors()
    results.unauthorizedAccess = await testUnauthorizedAccess()
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message)
  }
  
  // Print summary
  console.log('\n📋 Test Results Summary')
  console.log('=======================')
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL'
    console.log(`${status} ${test}`)
  })
  
  const passedTests = Object.values(results).filter(Boolean).length
  const totalTests = Object.keys(results).length
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`)
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Company Settings implementation is working correctly.')
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.')
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = {
  runTests,
  testGetSettings,
  testUpdateSettings,
  testPartialUpdate,
  testResetSettings,
  testValidationErrors,
  testUnauthorizedAccess
}
