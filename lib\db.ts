import mongoose from 'mongoose'

const MONGODB_URI = process.env.MONGODB_URI

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local')
}

/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */
declare global {
  var mongoose: {
    conn: typeof mongoose | null
    promise: Promise<typeof mongoose> | null
  }
}

let cached = global.mongoose

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null }
}

async function connectDB() {
  if (cached.conn) {
    return cached.conn
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    }

    cached.promise = mongoose.connect(MONGODB_URI!, opts).then((mongoose) => {
      return mongoose
    })
  }

  try {
    cached.conn = await cached.promise
  } catch (e) {
    cached.promise = null
    throw e
  }

  return cached.conn
}

export { connectDB }

// Database health check
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await connectDB()
    // Ping the database
    if (mongoose.connection.db) {
      await mongoose.connection.db.admin().ping()
    }
    return true
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}

// Get database statistics
export async function getDatabaseStats() {
  try {
    await connectDB()
    const db = mongoose.connection.db
    
    if (db) {
      const [stats, collections] = await Promise.all([
        db.stats(),
        db.listCollections().toArray()
      ])

      return {
        connected: mongoose.connection.readyState === 1,
        database: db.databaseName,
        collections: collections.length,
        stats: {
          dataSize: stats.dataSize,
          storageSize: stats.storageSize,
          indexes: stats.indexes,
          objects: stats.objects
        }
      }
    }

    return null
  } catch (error) {
    console.error('Failed to get database stats:', error)
    return null
  }
}

// Close database connection
export async function disconnectDB() {
  try {
    await mongoose.disconnect()
    cached.conn = null
    cached.promise = null
  } catch (error) {
    console.error('Error disconnecting from database:', error)
  }
}
