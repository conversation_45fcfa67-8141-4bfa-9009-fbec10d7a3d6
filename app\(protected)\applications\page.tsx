import { Metadata } from 'next'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { ApplicationsDashboard } from '@/components/applications/applications-dashboard'

export const metadata: Metadata = {
  title: 'My Applications | JobPortal',
  description: 'Track and manage your job applications, view application status, and get insights on your job search progress.',
}

function ApplicationsContent() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Applications</h1>
              <p className="text-muted-foreground">
                Track your job applications and manage your career journey
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <ApplicationsDashboard />
      </main>
    </div>
  )
}

export default function ApplicationsPage() {
  return (
    <ProtectedRoute requiredRole="job_seeker">
      <ApplicationsContent />
    </ProtectedRoute>
  )
}
