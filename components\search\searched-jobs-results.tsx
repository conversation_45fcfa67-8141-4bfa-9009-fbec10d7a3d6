'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { getApplicationStatusInfo } from '@/lib/job-data'
import { 
  MapPin, 
  DollarSign, 
  Clock, 
  Eye, 
  Heart, 
  Briefcase,
  Users,
  Share2,
  Bookmark,
  Zap,
  Search,
  X
} from 'lucide-react'
import Link from 'next/link'

interface SearchedJobsResultsProps {
  jobs: any[]
  searchQuery: string
  onClearSearch: () => void
}

export function SearchedJobsResults({ jobs, searchQuery, onClearSearch }: SearchedJobsResultsProps) {
  const [likedJobs, setLikedJobs] = useState<number[]>([])
  const [bookmarkedJobs, setBookmarkedJobs] = useState<number[]>([])

  const toggleLike = (jobId: number) => {
    setLikedJobs((prev) => (prev.includes(jobId) ? prev.filter((id) => id !== jobId) : [...prev, jobId]))
  }

  const toggleBookmark = (jobId: number) => {
    setBookmarkedJobs((prev) => (prev.includes(jobId) ? prev.filter((id) => id !== jobId) : [...prev, jobId]))
  }

  const renderJobCard = (job: any) => {
    const applicationStatusInfo = getApplicationStatusInfo(job)
    
    return (
      <Card
        className={`glass hover:shadow-xl transition-all duration-300 ${
          job.featured ? "border-primary/50 bg-primary/5" : "border-border/50"
        } ${job.urgent ? "ring-2 ring-orange-500/20" : ""}`}
      >
        {job.urgent && (
          <div className="absolute -top-2 -right-2">
            <Badge className="bg-orange-500 text-white animate-pulse">
              <Zap className="w-3 h-3 mr-1" />
              Urgent
            </Badge>
          </div>
        )}

        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="w-12 h-12">
                <AvatarImage src={job.company?.logo || "/placeholder.svg"} alt={job.company?.name || job.company} />
                <AvatarFallback>{(job.company?.name || job.company)[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-xl font-bold hover:text-primary transition-colors duration-200 cursor-pointer">
                  {job.title}
                </h3>
                <p className="text-muted-foreground">{job.company?.name || job.company}</p>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{job.posted}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span>{job.location}</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => toggleLike(job.id)}
                className={`p-2 rounded-full transition-colors duration-200 ${
                  likedJobs.includes(job.id) ? "bg-red-100 text-red-500" : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Heart className={`w-4 h-4 ${likedJobs.includes(job.id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => toggleBookmark(job.id)}
                className={`p-2 rounded-full transition-colors duration-200 ${
                  bookmarkedJobs.includes(job.id)
                    ? "bg-primary/10 text-primary"
                    : "bg-muted hover:bg-muted/80"
                }`}
              >
                <Bookmark className={`w-4 h-4 ${bookmarkedJobs.includes(job.id) ? "fill-current" : ""}`} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="p-2 rounded-full bg-muted hover:bg-muted/80 transition-colors duration-200"
              >
                <Share2 className="w-4 h-4" />
              </motion.button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-muted-foreground" />
              <span className="font-semibold text-primary">
                {job.salary ? `$${job.salary.min?.toLocaleString()} - $${job.salary.max?.toLocaleString()}` : 'Salary not specified'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Briefcase className="w-4 h-4 text-muted-foreground" />
              <span>{job.type} • {job.experience || 'Experience not specified'}</span>
            </div>
          </div>

          {job.skills && job.skills.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {job.skills.slice(0, 4).map((skill: string) => (
                <Badge key={skill} variant="secondary" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {job.skills.length > 4 && (
                <Badge variant="outline" className="text-xs">
                  +{job.skills.length - 4} more
                </Badge>
              )}
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t border-border/50">
            <div className="flex items-center space-x-4">
              <Badge 
                variant="secondary" 
                className={`text-xs ${
                  applicationStatusInfo.color === 'green' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                  applicationStatusInfo.color === 'orange' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                  applicationStatusInfo.color === 'red' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                  'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                }`}
              >
                {applicationStatusInfo.label}
              </Badge>
              {job.featured && (
                <Badge className="bg-primary/10 text-primary border-primary/20">Featured</Badge>
              )}
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                Quick Apply
              </Button>
              <Button size="sm" asChild>
                <Link href={`/jobs/${job.id}`}>View Details</Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <section className="py-12 bg-background/95 backdrop-blur-sm">
      <div className="container mx-auto px-4">
        {/* Search Results Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Search className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Search Results</h2>
                <p className="text-muted-foreground">
                  Found {jobs.length} job{jobs.length !== 1 ? 's' : ''} matching your search
                </p>
                {jobs.length > 0 && (
                  <p className="text-sm text-muted-foreground mt-1">
                    Results sorted by relevance • Showing best matches first
                  </p>
                )}
              </div>
            </div>
            <Button
              variant="outline"
              onClick={onClearSearch}
              className="flex items-center space-x-2"
            >
              <X className="w-4 h-4" />
              <span>Clear Search</span>
            </Button>
          </div>

          {/* Search Query Display */}
          <div className="flex items-center space-x-2 p-3 bg-primary/5 border border-primary/20 rounded-lg">
            <Search className="w-4 h-4 text-primary" />
            <span className="text-sm">
              Searching for: <span className="font-semibold text-primary">"{searchQuery}"</span>
            </span>
          </div>
        </motion.div>

        {/* Results */}
        {jobs.length > 0 ? (
          <div className="max-w-4xl mx-auto space-y-6">
            <AnimatePresence>
              {jobs.map((job, index) => (
                <motion.div
                  key={job.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                  className="relative"
                >
                  {renderJobCard(job)}
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center py-12"
          >
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No jobs found</h3>
            <p className="text-muted-foreground mb-4">
              We couldn't find any jobs matching your search criteria.
            </p>
            <div className="text-sm text-muted-foreground mb-6 space-y-1">
              <p>Try:</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Using broader search terms (e.g., "dev" instead of "developer")</li>
                <li>Checking your spelling</li>
                <li>Searching by location only</li>
                <li>Using partial words (e.g., "clin" for clinical jobs)</li>
              </ul>
            </div>
            <Button onClick={onClearSearch} variant="outline">
              Clear Search and Browse All Jobs
            </Button>
          </motion.div>
        )}

        {/* Load More Button */}
        {jobs.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-center mt-12"
          >
            <Button size="lg" variant="outline" className="text-lg px-8 bg-transparent">
              Load More Results
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  )
}
