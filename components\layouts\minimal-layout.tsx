"use client"

import React from "react"
import { BackgroundPattern } from "@/components/background-pattern"
import { NotificationSystem } from "@/components/notification-system"

interface MinimalLayoutProps {
  children: React.ReactNode
  showBackgroundPattern?: boolean
  showNotificationSystem?: boolean
  className?: string
}

export function MinimalLayout({
  children,
  showBackgroundPattern = true,
  showNotificationSystem = true,
  className = "min-h-screen bg-background"
}: MinimalLayoutProps) {
  return (
    <div className={className}>
      {showBackgroundPattern && <BackgroundPattern />}
      <main className="relative">
        {children}
      </main>
      {showNotificationSystem && <NotificationSystem />}
    </div>
  )
}
