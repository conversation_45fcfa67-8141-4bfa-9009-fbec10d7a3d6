// components\admin\company-verification.tsx
'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { 
  Search, 
  Filter, 
  Building,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  ArrowLeft,
  RefreshCw,
  Calendar,
  MapPin,
  Users,
  Globe
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CompanyVerificationProps {
  className?: string
}

interface Company {
  _id: string
  name: string
  slug: string
  description: string
  website?: string
  industry: string
  size: string
  location: {
    city?: string
    state?: string
    country?: string
  }
  verification: {
    isVerified: boolean
    verifiedAt?: Date
    notes?: string
  }
  createdAt: Date
  admins: Array<{
    profile: {
      firstName: string
      lastName: string
    }
    email: string
  }>
}

interface CompaniesResponse {
  companies: Company[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export function CompanyVerification({ className }: CompanyVerificationProps) {
  const router = useRouter()
  const [companies, setCompanies] = useState<Company[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('pending')
  
  // Dialog states
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null)
  const [verificationDialog, setVerificationDialog] = useState<{
    open: boolean
    company?: Company
    action: 'verify' | 'reject'
    notes: string
  }>({ open: false, action: 'verify', notes: '' })

  useEffect(() => {
    fetchCompanies()
  }, [pagination.page, searchTerm, statusFilter])

  const fetchCompanies = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { verificationStatus: statusFilter })
      })

      const response = await fetch(`/api/admin/companies?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to fetch companies')
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to fetch companies')
      }

      const data: CompaniesResponse = result.data
      setCompanies(data.companies)
      setPagination(data.pagination)
    } catch (error: any) {
      console.error('Companies fetch error:', error)
      setError(error.message || 'Failed to load companies')
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerification = async (companyId: string, verified: boolean, notes: string) => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/admin/companies/${companyId}/verify`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ verified, notes })
      })

      if (!response.ok) {
        throw new Error('Failed to update company verification')
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update company verification')
      }

      // Refresh companies list
      await fetchCompanies()
      setVerificationDialog({ open: false, action: 'verify', notes: '' })
    } catch (error: any) {
      console.error('Company verification error:', error)
      setError(error.message || 'Failed to update company verification')
    }
  }

  const getStatusBadge = (company: Company) => {
    if (company.verification.isVerified) {
      return (
        <Badge className="bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Verified
        </Badge>
      )
    } else {
      return (
        <Badge className="bg-yellow-100 text-yellow-800">
          <Clock className="w-3 h-3 mr-1" />
          Pending
        </Badge>
      )
    }
  }

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (isLoading && companies.length === 0) {
    return <PageLoader />
  }

  return (
    <div className={cn('space-y-6 p-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push('/admin')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Company Verification</h1>
            <p className="text-muted-foreground">
              Review and verify company registrations
            </p>
          </div>
        </div>
        <Button onClick={fetchCompanies} disabled={isLoading}>
          <RefreshCw className={cn('w-4 h-4 mr-2', isLoading && 'animate-spin')} />
          Refresh
        </Button>
      </div>

      {error && (
        <ErrorAlert
          message={error}
          dismissible={true}
          onDismiss={() => setError(null)}
          actions={
            <Button
              variant="outline"
              size="sm"
              onClick={fetchCompanies}
              className="ml-2"
            >
              Retry
            </Button>
          }
        />
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="w-5 h-5 mr-2" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search companies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="verified">Verified</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Companies Table */}
      <Card>
        <CardHeader>
          <CardTitle>Companies ({pagination.total.toLocaleString()})</CardTitle>
          <CardDescription>
            Showing {companies.length} of {pagination.total} companies
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Industry</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>Submitted</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {companies.map((company) => (
                  <TableRow key={company._id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-medium">
                          <Building className="w-5 h-5" />
                        </div>
                        <div>
                          <p className="font-medium">{company.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {company.slug}
                          </p>
                          {company.website && (
                            <p className="text-xs text-muted-foreground flex items-center mt-1">
                              <Globe className="w-3 h-3 mr-1" />
                              {company.website}
                            </p>
                          )}
                          {company.location.city && (
                            <p className="text-xs text-muted-foreground flex items-center">
                              <MapPin className="w-3 h-3 mr-1" />
                              {company.location.city}, {company.location.state}
                            </p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="text-sm">{company.industry}</p>
                        <p className="text-xs text-muted-foreground flex items-center">
                          <Users className="w-3 h-3 mr-1" />
                          {company.size}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(company)}
                      {company.verification.notes && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {company.verification.notes}
                        </p>
                      )}
                    </TableCell>
                    <TableCell>
                      <div>
                        {company.admins && company.admins.length > 0 ? (
                          <>
                            <p className="text-sm font-medium">
                              {company.admins[0].profile.firstName} {company.admins[0].profile.lastName}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {company.admins[0].email}
                            </p>
                          </>
                        ) : (
                          <p className="text-sm text-muted-foreground">No admin assigned</p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Calendar className="w-3 h-3 mr-1" />
                        {formatDate(company.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedCompany(company)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        {!company.verification.isVerified && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-green-600 hover:text-green-700"
                              onClick={() => setVerificationDialog({
                                open: true,
                                company,
                                action: 'verify',
                                notes: ''
                              })}
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => setVerificationDialog({
                                open: true,
                                company,
                                action: 'reject',
                                notes: ''
                              })}
                            >
                              <XCircle className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <p className="text-sm text-muted-foreground">
                Page {pagination.page} of {pagination.pages}
              </p>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page <= 1}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page >= pagination.pages}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Company Details Dialog */}
      {selectedCompany && (
        <Dialog open={!!selectedCompany} onOpenChange={() => setSelectedCompany(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{selectedCompany.name}</DialogTitle>
              <DialogDescription>Company details and verification information</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Description</h4>
                <p className="text-sm text-muted-foreground">{selectedCompany.description}</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-1">Industry</h4>
                  <p className="text-sm text-muted-foreground">{selectedCompany.industry}</p>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Company Size</h4>
                  <p className="text-sm text-muted-foreground">{selectedCompany.size}</p>
                </div>
              </div>
              {selectedCompany.website && (
                <div>
                  <h4 className="font-medium mb-1">Website</h4>
                  <a 
                    href={selectedCompany.website} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    {selectedCompany.website}
                  </a>
                </div>
              )}
              <div>
                <h4 className="font-medium mb-1">Location</h4>
                <p className="text-sm text-muted-foreground">
                  {selectedCompany.location.city}, {selectedCompany.location.state}, {selectedCompany.location.country}
                </p>
              </div>
              <div>
                <h4 className="font-medium mb-1">Verification Status</h4>
                {getStatusBadge(selectedCompany)}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Verification Dialog */}
      {verificationDialog.open && (
        <Dialog
          open={verificationDialog.open}
          onOpenChange={(open) => setVerificationDialog(prev => ({ ...prev, open }))}
        >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {verificationDialog.action === 'verify' ? 'Verify Company' : 'Reject Company'}
            </DialogTitle>
            <DialogDescription>
              {verificationDialog.action === 'verify' 
                ? 'Approve this company for verification. They will be able to post jobs and access premium features.'
                : 'Reject this company verification. Please provide a reason for rejection.'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Notes (optional)</label>
              <Textarea
                placeholder={verificationDialog.action === 'verify' 
                  ? 'Add any verification notes...' 
                  : 'Reason for rejection...'
                }
                value={verificationDialog.notes}
                onChange={(e) => setVerificationDialog(prev => ({ ...prev, notes: e.target.value }))}
                className="mt-1"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setVerificationDialog(prev => ({ ...prev, open: false }))}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (verificationDialog.company) {
                  handleVerification(
                    verificationDialog.company._id,
                    verificationDialog.action === 'verify',
                    verificationDialog.notes
                  )
                }
              }}
              className={verificationDialog.action === 'verify' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'}
            >
              {verificationDialog.action === 'verify' ? 'Verify Company' : 'Reject Company'}
            </Button>
          </DialogFooter>
        </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
