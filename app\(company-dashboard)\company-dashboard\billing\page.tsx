// app/(company-dashboard)/company-dashboard/billing/page.tsx
"use client"

import React, { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  CreditCard,
  Download,
  Calendar,
  Check,
  X,
  Star,
  Users,
  Briefcase,
  BarChart3,
  Shield,
  Zap,
  Crown,
  ArrowUpRight,
  AlertCircle,
  CheckCircle
} from "lucide-react"

export default function BillingPage() {
  const [activeTab, setActiveTab] = useState("overview")

  // Mock data for current subscription
  const currentPlan = {
    name: "Professional",
    price: 99,
    currency: "USD",
    interval: "month",
    status: "active",
    nextBilling: "2024-03-15",
    features: [
      "Up to 50 job postings per month",
      "Unlimited applications",
      "Advanced analytics",
      "Team collaboration (up to 5 members)",
      "Priority support",
      "Custom branding"
    ],
    usage: {
      jobPostings: { used: 12, limit: 50 },
      teamMembers: { used: 3, limit: 5 },
      applications: { used: 156, limit: "unlimited" }
    }
  }

  const plans = [
    {
      name: "Starter",
      price: 29,
      interval: "month",
      description: "Perfect for small companies just getting started",
      features: [
        "Up to 5 job postings per month",
        "Up to 50 applications",
        "Basic analytics",
        "1 team member",
        "Email support"
      ],
      limitations: [
        "Limited customization",
        "Basic reporting"
      ],
      popular: false
    },
    {
      name: "Professional",
      price: 99,
      interval: "month",
      description: "Ideal for growing companies with regular hiring needs",
      features: [
        "Up to 50 job postings per month",
        "Unlimited applications",
        "Advanced analytics",
        "Up to 5 team members",
        "Priority support",
        "Custom branding",
        "API access"
      ],
      limitations: [],
      popular: true
    },
    {
      name: "Enterprise",
      price: 299,
      interval: "month",
      description: "For large organizations with complex hiring requirements",
      features: [
        "Unlimited job postings",
        "Unlimited applications",
        "Advanced analytics & reporting",
        "Unlimited team members",
        "24/7 phone support",
        "Custom integrations",
        "Dedicated account manager",
        "SSO & advanced security"
      ],
      limitations: [],
      popular: false
    }
  ]

  const invoices = [
    {
      id: "INV-2024-001",
      date: "2024-02-15",
      amount: 99.00,
      status: "paid",
      description: "Professional Plan - February 2024"
    },
    {
      id: "INV-2024-002",
      date: "2024-01-15",
      amount: 99.00,
      status: "paid",
      description: "Professional Plan - January 2024"
    },
    {
      id: "INV-2023-012",
      date: "2023-12-15",
      amount: 99.00,
      status: "paid",
      description: "Professional Plan - December 2023"
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'past_due': return 'bg-red-100 text-red-800'
      case 'canceled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getInvoiceStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Billing & Subscription</h1>
            <p className="text-muted-foreground mt-2">
              Manage your subscription, billing information, and usage
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download Invoice
            </Button>
            <Button>
              Upgrade Plan
            </Button>
          </div>
        </div>

        {/* Billing Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="plans">Plans & Pricing</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
            <TabsTrigger value="payment">Payment Methods</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-3">
              {/* Current Plan */}
              <div className="lg:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center space-x-2">
                          <Crown className="w-5 h-5 text-yellow-500" />
                          <span>Current Plan: {currentPlan.name}</span>
                        </CardTitle>
                        <CardDescription>
                          Your subscription is active and will renew on {new Date(currentPlan.nextBilling).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      <Badge className={getStatusColor(currentPlan.status)}>
                        {currentPlan.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-2xl font-bold">
                          ${currentPlan.price}
                          <span className="text-sm font-normal text-muted-foreground">
                            /{currentPlan.interval}
                          </span>
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Next billing: {new Date(currentPlan.nextBilling).toLocaleDateString()}
                        </p>
                      </div>
                      <Button variant="outline">
                        Change Plan
                      </Button>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-medium mb-3">Plan Features</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {currentPlan.features.map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Check className="w-4 h-4 text-green-500" />
                            <span className="text-sm">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Usage Statistics */}
                <Card>
                  <CardHeader>
                    <CardTitle>Usage This Month</CardTitle>
                    <CardDescription>
                      Track your usage against plan limits
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Job Postings</span>
                        <span className="text-sm text-muted-foreground">
                          {currentPlan.usage.jobPostings.used} / {currentPlan.usage.jobPostings.limit}
                        </span>
                      </div>
                      <Progress 
                        value={(currentPlan.usage.jobPostings.used / currentPlan.usage.jobPostings.limit) * 100} 
                        className="h-2"
                      />
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Team Members</span>
                        <span className="text-sm text-muted-foreground">
                          {currentPlan.usage.teamMembers.used} / {currentPlan.usage.teamMembers.limit}
                        </span>
                      </div>
                      <Progress 
                        value={(currentPlan.usage.teamMembers.used / currentPlan.usage.teamMembers.limit) * 100} 
                        className="h-2"
                      />
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Applications Received</span>
                        <span className="text-sm text-muted-foreground">
                          {currentPlan.usage.applications.used} (Unlimited)
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-green-600">
                        <CheckCircle className="w-4 h-4" />
                        <span>No limits on applications</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions & Info */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <Button className="w-full justify-start">
                      <ArrowUpRight className="w-4 h-4 mr-2" />
                      Upgrade Plan
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Download className="w-4 h-4 mr-2" />
                      Download Invoice
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <CreditCard className="w-4 h-4 mr-2" />
                      Update Payment
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Calendar className="w-4 h-4 mr-2" />
                      Billing History
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Payment Method</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-3">
                      <CreditCard className="w-8 h-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">•••• •••• •••• 4242</p>
                        <p className="text-sm text-muted-foreground">Expires 12/25</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm" className="w-full mt-3">
                      Update
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Billing Support</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">
                      Need help with billing or have questions about your subscription?
                    </p>
                    <Button variant="outline" size="sm" className="w-full">
                      Contact Support
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="plans" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-3">
              {plans.map((plan, index) => (
                <Card key={index} className={`relative ${plan.popular ? 'border-primary shadow-lg' : ''}`}>
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-primary text-primary-foreground">
                        <Star className="w-3 h-3 mr-1" />
                        Most Popular
                      </Badge>
                    </div>
                  )}
                  
                  <CardHeader className="text-center">
                    <CardTitle className="text-xl">{plan.name}</CardTitle>
                    <div className="mt-4">
                      <span className="text-3xl font-bold">${plan.price}</span>
                      <span className="text-muted-foreground">/{plan.interval}</span>
                    </div>
                    <CardDescription className="mt-2">
                      {plan.description}
                    </CardDescription>
                  </CardHeader>
                  
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Features included:</h4>
                      <ul className="space-y-2">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center space-x-2">
                            <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                            <span className="text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    {plan.limitations.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Limitations:</h4>
                        <ul className="space-y-2">
                          {plan.limitations.map((limitation, limitIndex) => (
                            <li key={limitIndex} className="flex items-center space-x-2">
                              <X className="w-4 h-4 text-red-500 flex-shrink-0" />
                              <span className="text-sm text-muted-foreground">{limitation}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    <Button 
                      className="w-full" 
                      variant={plan.name === currentPlan.name ? "outline" : "default"}
                      disabled={plan.name === currentPlan.name}
                    >
                      {plan.name === currentPlan.name ? 'Current Plan' : 'Choose Plan'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="invoices" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Billing History</CardTitle>
                <CardDescription>
                  View and download your past invoices
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <CreditCard className="w-5 h-5 text-primary" />
                        </div>
                        <div>
                          <h4 className="font-medium">{invoice.description}</h4>
                          <p className="text-sm text-muted-foreground">
                            {invoice.id} • {new Date(invoice.date).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <p className="font-medium">${invoice.amount.toFixed(2)}</p>
                          <Badge className={getInvoiceStatusColor(invoice.status)}>
                            {invoice.status}
                          </Badge>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="w-4 h-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payment" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Methods</CardTitle>
                  <CardDescription>
                    Manage your payment methods and billing information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <CreditCard className="w-8 h-8 text-muted-foreground" />
                      <div>
                        <p className="font-medium">•••• •••• •••• 4242</p>
                        <p className="text-sm text-muted-foreground">Expires 12/25 • Default</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">Edit</Button>
                      <Button variant="outline" size="sm">Remove</Button>
                    </div>
                  </div>
                  
                  <Button variant="outline" className="w-full">
                    <CreditCard className="w-4 h-4 mr-2" />
                    Add Payment Method
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Billing Information</CardTitle>
                  <CardDescription>
                    Update your billing address and tax information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <p className="font-medium">Acme Corporation</p>
                    <p className="text-sm text-muted-foreground">
                      123 Business Street<br />
                      San Francisco, CA 94105<br />
                      United States
                    </p>
                  </div>
                  
                  <Button variant="outline" className="w-full">
                    Update Billing Address
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
