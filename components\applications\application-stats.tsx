'use client'

import React from 'react'
import { type ApplicationStats } from '@/stores'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { 
  Briefcase, 
  Clock, 
  Calendar, 
  CheckCircle, 
  XCircle,
  TrendingUp,
  Target,
  Timer
} from 'lucide-react'

interface ApplicationStatsProps {
  stats: ApplicationStats
  className?: string
}

export function ApplicationStats({ stats, className }: ApplicationStatsProps) {
  // Calculate percentages
  const getPercentage = (value: number, total: number) => {
    return total > 0 ? Math.round((value / total) * 100) : 0
  }

  const interviewRate = getPercentage(stats.interviewed, stats.total)
  const offerRate = getPercentage(stats.offers, stats.total)
  const rejectionRate = getPercentage(stats.rejected, stats.total)

  // Format response time
  const formatResponseTime = (hours: number) => {
    if (hours < 24) return `${Math.round(hours)} hours`
    const days = Math.round(hours / 24)
    return `${days} day${days !== 1 ? 's' : ''}`
  }

  const statCards = [
    {
      title: 'Total Applications',
      value: stats.total,
      description: 'Jobs you\'ve applied to',
      icon: Briefcase,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Under Review',
      value: stats.underReview,
      description: `${getPercentage(stats.underReview, stats.total)}% of applications`,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      title: 'Interviews',
      value: stats.interviewed,
      description: `${interviewRate}% interview rate`,
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Offers Received',
      value: stats.offers,
      description: `${offerRate}% offer rate`,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    }
  ]

  return (
    <div className={className}>
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {statCards.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`w-4 h-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Application Success Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5" />
              <span>Success Metrics</span>
            </CardTitle>
            <CardDescription>
              Your application performance overview
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Response Rate */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Response Rate</span>
                <span className="text-sm text-muted-foreground">
                  {stats.responseRate}%
                </span>
              </div>
              <Progress value={stats.responseRate} className="h-2" />
              <p className="text-xs text-muted-foreground">
                Percentage of applications that received a response
              </p>
            </div>

            {/* Interview Rate */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Interview Rate</span>
                <span className="text-sm text-muted-foreground">
                  {interviewRate}%
                </span>
              </div>
              <Progress value={interviewRate} className="h-2" />
              <p className="text-xs text-muted-foreground">
                Applications that led to interviews
              </p>
            </div>

            {/* Offer Rate */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Offer Rate</span>
                <span className="text-sm text-muted-foreground">
                  {offerRate}%
                </span>
              </div>
              <Progress value={offerRate} className="h-2" />
              <p className="text-xs text-muted-foreground">
                Applications that resulted in job offers
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Application Status Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Application Status</span>
            </CardTitle>
            <CardDescription>
              Current status of all your applications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Status Breakdown */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">Submitted</span>
                </div>
                <span className="text-sm font-medium">{stats.submitted}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm">Under Review</span>
                </div>
                <span className="text-sm font-medium">{stats.underReview}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm">Interviewed</span>
                </div>
                <span className="text-sm font-medium">{stats.interviewed}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Offers</span>
                </div>
                <span className="text-sm font-medium">{stats.offers}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm">Rejected</span>
                </div>
                <span className="text-sm font-medium">{stats.rejected}</span>
              </div>
            </div>

            {/* Average Response Time */}
            <div className="pt-4 border-t">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Timer className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Avg. Response Time</span>
                </div>
                <span className="text-sm text-muted-foreground">
                  {formatResponseTime(stats.averageResponseTime)}
                </span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Average time to hear back from employers
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tips and Insights */}
      {stats.total > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>💡 Insights & Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {stats.responseRate < 20 && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="font-medium text-blue-900">Improve Your Response Rate</p>
                  <p className="text-blue-700 mt-1">
                    Consider tailoring your resume and cover letter more specifically to each job posting.
                  </p>
                </div>
              )}
              
              {interviewRate > 30 && (
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="font-medium text-green-900">Great Interview Rate!</p>
                  <p className="text-green-700 mt-1">
                    You're getting interviews at an above-average rate. Keep up the good work!
                  </p>
                </div>
              )}
              
              {stats.total < 10 && (
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <p className="font-medium text-yellow-900">Apply to More Jobs</p>
                  <p className="text-yellow-700 mt-1">
                    Consider applying to more positions to increase your chances of success.
                  </p>
                </div>
              )}
              
              {stats.averageResponseTime > 168 && (
                <div className="p-3 bg-purple-50 rounded-lg">
                  <p className="font-medium text-purple-900">Follow Up Strategy</p>
                  <p className="text-purple-700 mt-1">
                    Consider following up on applications after 1-2 weeks if you haven't heard back.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
