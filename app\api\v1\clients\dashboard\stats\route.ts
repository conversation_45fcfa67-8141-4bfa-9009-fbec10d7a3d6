import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get current date ranges
    const now = new Date()
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    // Get application stats
    const totalApplications = await Application.countDocuments({ client: client._id })
    const applicationsThisMonth = await Application.countDocuments({
      client: client._id,
      createdAt: { $gte: lastMonth }
    })
    const applicationsLastMonth = await Application.countDocuments({
      client: client._id,
      createdAt: { 
        $gte: new Date(lastMonth.getFullYear(), lastMonth.getMonth() - 1, lastMonth.getDate()),
        $lt: lastMonth
      }
    })

    // Calculate changes
    const applicationsChange = applicationsLastMonth > 0 
      ? Math.round(((applicationsThisMonth - applicationsLastMonth) / applicationsLastMonth) * 100)
      : applicationsThisMonth > 0 ? 100 : 0

    // Get interview stats
    const interviews = await Application.countDocuments({
      client: client._id,
      status: 'interview_scheduled'
    })
    const interviewsThisWeek = await Application.countDocuments({
      client: client._id,
      status: 'interview_scheduled',
      updatedAt: { $gte: lastWeek }
    })

    // Get profile views (mock data for now - would come from analytics)
    const profileViews = client.activity?.profileViews || 0
    const profileViewsChange = Math.floor(Math.random() * 20) + 5 // Mock change

    // Get saved jobs count
    const savedJobs = client.savedJobs?.length || 0
    const savedJobsChange = Math.floor(Math.random() * 5) // Mock change

    // Calculate response rate
    const respondedApplications = await Application.countDocuments({
      client: client._id,
      status: { $in: ['interview_scheduled', 'offer_received', 'rejected'] }
    })
    const responseRate = totalApplications > 0 
      ? Math.round((respondedApplications / totalApplications) * 100)
      : 0

    // Calculate average response time (mock for now)
    const averageResponseTime = Math.floor(Math.random() * 10) + 3 // 3-13 days

    const stats = {
      profileViews,
      profileViewsChange,
      applications: totalApplications,
      applicationsChange,
      savedJobs,
      savedJobsChange,
      interviews,
      interviewsChange: interviewsThisWeek,
      profileCompleteness: client.activity?.profileCompleteness || 0,
      responseRate,
      averageResponseTime
    }

    return NextResponse.json({
      success: true,
      data: stats
    })

  } catch (error) {
    console.error('Get dashboard stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard stats' },
      { status: 500 }
    )
  }
}
