'use client'

import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { LucideIcon, TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface StatsCardProps {
  title: string
  value: string | number
  description?: string
  icon?: LucideIcon
  trend?: {
    value: number
    label: string
    type: 'increase' | 'decrease' | 'neutral'
  }
  className?: string
  variant?: 'default' | 'success' | 'warning' | 'danger'
  loading?: boolean
}

export function StatsCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  className,
  variant = 'default',
  loading = false
}: StatsCardProps) {
  const getTrendIcon = () => {
    switch (trend?.type) {
      case 'increase':
        return TrendingUp
      case 'decrease':
        return TrendingDown
      default:
        return Minus
    }
  }

  const getTrendColor = () => {
    switch (trend?.type) {
      case 'increase':
        return 'text-green-600'
      case 'decrease':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getVariantStyles = () => {
    switch (variant) {
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'warning':
        return 'border-yellow-200 bg-yellow-50'
      case 'danger':
        return 'border-red-200 bg-red-50'
      default:
        return ''
    }
  }

  if (loading) {
    return (
      <Card className={cn('animate-pulse', className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 bg-gray-200 rounded w-24"></div>
          <div className="h-4 w-4 bg-gray-200 rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-32"></div>
        </CardContent>
      </Card>
    )
  }

  const TrendIcon = getTrendIcon()

  return (
    <Card className={cn(getVariantStyles(), className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {Icon && (
          <Icon className="h-4 w-4 text-muted-foreground" />
        )}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-1">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </div>
        
        <div className="flex items-center justify-between">
          {description && (
            <p className="text-xs text-muted-foreground flex-1">
              {description}
            </p>
          )}
          
          {trend && (
            <div className="flex items-center space-x-1">
              <TrendIcon className={cn('h-3 w-3', getTrendColor())} />
              <Badge 
                variant="secondary" 
                className={cn(
                  'text-xs',
                  trend.type === 'increase' && 'bg-green-100 text-green-800',
                  trend.type === 'decrease' && 'bg-red-100 text-red-800',
                  trend.type === 'neutral' && 'bg-gray-100 text-gray-800'
                )}
              >
                {trend.type === 'increase' ? '+' : trend.type === 'decrease' ? '-' : ''}
                {Math.abs(trend.value)}
                {trend.label}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Preset stats cards for common use cases
export function ActiveJobsCard({ value, trend, loading }: { 
  value: number
  trend?: StatsCardProps['trend']
  loading?: boolean 
}) {
  return (
    <StatsCard
      title="Active Jobs"
      value={value}
      description="Currently open positions"
      icon={require('lucide-react').Briefcase}
      trend={trend}
      loading={loading}
    />
  )
}

export function ApplicationsCard({ value, trend, loading }: { 
  value: number
  trend?: StatsCardProps['trend']
  loading?: boolean 
}) {
  return (
    <StatsCard
      title="Total Applications"
      value={value}
      description="All time applications"
      icon={require('lucide-react').Users}
      trend={trend}
      loading={loading}
    />
  )
}

export function PendingReviewsCard({ value, loading }: { 
  value: number
  loading?: boolean 
}) {
  return (
    <StatsCard
      title="Pending Reviews"
      value={value}
      description="Requires attention"
      icon={require('lucide-react').Clock}
      variant={value > 10 ? 'warning' : 'default'}
      loading={loading}
    />
  )
}

export function InterviewsCard({ value, loading }: { 
  value: number
  loading?: boolean 
}) {
  return (
    <StatsCard
      title="Interviews Scheduled"
      value={value}
      description="This week"
      icon={require('lucide-react').Calendar}
      loading={loading}
    />
  )
}

export function HiredCandidatesCard({ value, trend, loading }: { 
  value: number
  trend?: StatsCardProps['trend']
  loading?: boolean 
}) {
  return (
    <StatsCard
      title="Hired Candidates"
      value={value}
      description="This month"
      icon={require('lucide-react').CheckCircle}
      variant="success"
      trend={trend}
      loading={loading}
    />
  )
}

export function ResponseRateCard({ value, trend, loading }: { 
  value: number
  trend?: StatsCardProps['trend']
  loading?: boolean 
}) {
  return (
    <StatsCard
      title="Response Rate"
      value={`${value}%`}
      description="Application responses"
      icon={require('lucide-react').TrendingUp}
      trend={trend}
      loading={loading}
    />
  )
}
