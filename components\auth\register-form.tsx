'use client'

import React, { useState } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { useAuthStore } from '@/stores'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ButtonLoading } from '@/components/ui/button-loading'
import { ErrorAlert, InlineError } from '@/components/ui/error-alert'
import { Eye, EyeOff, Mail, Lock, User, Phone, MapPin, Building2, Check, X } from 'lucide-react'
import { cn, validatePasswordStrength, validateWebsiteUrl, normalizeWebsiteUrl } from '@/lib/utils'
import { PasswordStrengthIndicator } from '@/components/ui/password-strength-indicator'

interface RegisterFormData {
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  role: 'job_seeker' | 'company_admin' | ''
  phone: string
  location: {
    city: string
    state: string
    country: string
  }
  // Company fields (only for company_admin role)
  companyName?: string
  companyWebsite?: string
  companyIndustry?: string
  companySize?: string
  companyDescription?: string
}

interface RegisterFormErrors {
  email?: string
  password?: string
  confirmPassword?: string
  firstName?: string
  lastName?: string
  role?: string
  phone?: string
  companyName?: string
  companyWebsite?: string
  companyIndustry?: string
  companySize?: string
  companyDescription?: string
  general?: string
}

export function RegisterForm() {
  const router = useRouter()
  const { register, registerLoading, error, clearError } = useAuthStore()
  
  const [formData, setFormData] = useState<RegisterFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    role: '',
    phone: '',
    location: {
      city: '',
      state: '',
      country: 'United States'
    },
    companyName: '',
    companyWebsite: '',
    companyIndustry: '',
    companySize: '',
    companyDescription: ''
  })
  
  const [errors, setErrors] = useState<RegisterFormErrors>({})
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState(validatePasswordStrength(''))
  const [passwordsMatch, setPasswordsMatch] = useState<boolean | null>(null)

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: RegisterFormErrors = {}

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (!passwordStrength.isValid) {
      newErrors.password = 'Password does not meet security requirements'
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    // Name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    }

    // Role validation
    if (!formData.role) {
      newErrors.role = 'Please select your role'
    }

    // Company fields validation (only for company_admin role)
    if (formData.role === 'company_admin') {
      if (!formData.companyName?.trim()) {
        newErrors.companyName = 'Company name is required'
      }
      if (!formData.companyIndustry?.trim()) {
        newErrors.companyIndustry = 'Company industry is required'
      }
      if (!formData.companySize?.trim()) {
        newErrors.companySize = 'Company size is required'
      }
      if (!formData.companyDescription?.trim()) {
        newErrors.companyDescription = 'Company description is required'
      }
      if (formData.companyWebsite && !validateWebsiteUrl(formData.companyWebsite)) {
        newErrors.companyWebsite = 'Please enter a valid website URL'
      }
    }

    // Phone validation (optional but if provided, should be valid)
    if (formData.phone && !/^\+?[\d\s\-\(\)]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()

    if (!validateForm()) {
      return
    }

    try {
      const registrationData: any = {
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        role: formData.role as 'job_seeker' | 'company_admin',
        phone: formData.phone || undefined,
        location: formData.location.city ? formData.location : undefined
      }

      // Add company data if registering as company admin
      if (formData.role === 'company_admin') {
        registrationData.company = {
          name: formData.companyName?.trim(),
          website: formData.companyWebsite?.trim(),
          industry: formData.companyIndustry?.trim(),
          size: formData.companySize?.trim(),
          description: formData.companyDescription?.trim(),
          location: formData.location
        }
      }

      const result = await register(registrationData)

      // Redirect based on user role
      if (formData.role === 'company_admin') {
        router.push('/company-dashboard')
      } else {
        router.push('/')
      }
    } catch (error) {
      console.error('Registration failed:', error)
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof RegisterFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))

    // Handle password strength validation
    if (field === 'password') {
      setPasswordStrength(validatePasswordStrength(value))
      // Check password match if confirm password exists
      if (formData.confirmPassword) {
        setPasswordsMatch(value === formData.confirmPassword)
      }
    }

    // Handle confirm password matching
    if (field === 'confirmPassword') {
      setPasswordsMatch(value === formData.password)
    }

    // Handle website URL normalization
    if (field === 'companyWebsite' && value) {
      const normalizedUrl = normalizeWebsiteUrl(value)
      if (normalizedUrl !== value) {
        setFormData(prev => ({ ...prev, [field]: normalizedUrl }))
      }
    }

    // Clear field error when user starts typing
    if (errors[field as keyof RegisterFormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Handle location changes
  const handleLocationChange = (field: keyof RegisterFormData['location']) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      location: { ...prev.location, [field]: e.target.value }
    }))
  }

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">Create your account</CardTitle>
        <CardDescription className="text-center">
          Join thousands of professionals finding their dream jobs
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* General Error */}
          {error && (
            <ErrorAlert
              type="error"
              message={error.message || 'Registration failed. Please try again.'}
              dismissible
              onDismiss={clearError}
            />
          )}

          {/* Role Selection */}
          <div className="space-y-2">
            <Label htmlFor="role">I am a *</Label>
            <Select
              value={formData.role}
              onValueChange={(value) => {
                setFormData(prev => ({ ...prev, role: value as any }))
                if (errors.role) {
                  setErrors(prev => ({ ...prev, role: undefined }))
                }
              }}
              disabled={registerLoading}
            >
              <SelectTrigger className={cn(errors.role && 'border-red-500')}>
                <SelectValue placeholder="Select your role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="job_seeker">Job Seeker</SelectItem>
                <SelectItem value="company_admin">Company Admin</SelectItem>
              </SelectContent>
            </Select>
            <InlineError message={errors.role} />
          </div>

          {/* Company Fields - Only show for Company Admin */}
          {formData.role === 'company_admin' && (
            <div className="space-y-4 p-4 bg-muted/30 rounded-lg border">
              <div className="flex items-center space-x-2 mb-3">
                <Building2 className="w-5 h-5 text-primary" />
                <h3 className="font-semibold text-lg">Company Information</h3>
              </div>

              {/* Company Name */}
              <div className="space-y-2">
                <Label htmlFor="companyName">Company Name *</Label>
                <Input
                  id="companyName"
                  placeholder="Enter your company name"
                  value={formData.companyName}
                  onChange={handleInputChange('companyName')}
                  className={cn(errors.companyName && 'border-red-500')}
                  disabled={registerLoading}
                />
                <InlineError message={errors.companyName} />
              </div>

              {/* Company Website */}
              <div className="space-y-2">
                <Label htmlFor="companyWebsite">Company Website</Label>
                <Input
                  id="companyWebsite"
                  placeholder="https://www.yourcompany.com"
                  value={formData.companyWebsite}
                  onChange={handleInputChange('companyWebsite')}
                  className={cn(errors.companyWebsite && 'border-red-500')}
                  disabled={registerLoading}
                />
                <InlineError message={errors.companyWebsite} />
              </div>

              {/* Industry and Size */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="companyIndustry">Industry *</Label>
                  <Select
                    value={formData.companyIndustry}
                    onValueChange={(value) => {
                      setFormData(prev => ({ ...prev, companyIndustry: value }))
                      if (errors.companyIndustry) {
                        setErrors(prev => ({ ...prev, companyIndustry: undefined }))
                      }
                    }}
                    disabled={registerLoading}
                  >
                    <SelectTrigger className={cn(errors.companyIndustry && 'border-red-500')}>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="technology">Technology</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                      <SelectItem value="finance">Finance</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="consulting">Consulting</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <InlineError message={errors.companyIndustry} />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="companySize">Company Size *</Label>
                  <Select
                    value={formData.companySize}
                    onValueChange={(value) => {
                      setFormData(prev => ({ ...prev, companySize: value }))
                      if (errors.companySize) {
                        setErrors(prev => ({ ...prev, companySize: undefined }))
                      }
                    }}
                    disabled={registerLoading}
                  >
                    <SelectTrigger className={cn(errors.companySize && 'border-red-500')}>
                      <SelectValue placeholder="Select size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1-10">1-10 employees</SelectItem>
                      <SelectItem value="11-50">11-50 employees</SelectItem>
                      <SelectItem value="51-200">51-200 employees</SelectItem>
                      <SelectItem value="201-500">201-500 employees</SelectItem>
                      <SelectItem value="501-1000">501-1000 employees</SelectItem>
                      <SelectItem value="1000+">1000+ employees</SelectItem>
                    </SelectContent>
                  </Select>
                  <InlineError message={errors.companySize} />
                </div>
              </div>

              {/* Company Description */}
              <div className="space-y-2">
                <Label htmlFor="companyDescription">Company Description *</Label>
                <textarea
                  id="companyDescription"
                  placeholder="Brief description of your company..."
                  value={formData.companyDescription}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, companyDescription: e.target.value }))
                    if (errors.companyDescription) {
                      setErrors(prev => ({ ...prev, companyDescription: undefined }))
                    }
                  }}
                  className={cn(
                    'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
                    errors.companyDescription && 'border-red-500'
                  )}
                  disabled={registerLoading}
                  rows={3}
                />
                <InlineError message={errors.companyDescription} />
              </div>
            </div>
          )}

          {/* Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name *</Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  id="firstName"
                  placeholder="First name"
                  value={formData.firstName}
                  onChange={handleInputChange('firstName')}
                  className={cn(
                    'pl-10',
                    errors.firstName && 'border-red-500'
                  )}
                  disabled={registerLoading}
                  autoComplete="given-name"
                />
              </div>
              <InlineError message={errors.firstName} />
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                placeholder="Last name"
                value={formData.lastName}
                onChange={handleInputChange('lastName')}
                className={cn(errors.lastName && 'border-red-500')}
                disabled={registerLoading}
                autoComplete="family-name"
              />
              <InlineError message={errors.lastName} />
            </div>
          </div>

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">Email *</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleInputChange('email')}
                className={cn(
                  'pl-10',
                  errors.email && 'border-red-500'
                )}
                disabled={registerLoading}
                autoComplete="email"
              />
            </div>
            <InlineError message={errors.email} />
          </div>

          {/* Password Fields */}
          <div className="grid grid-cols-1 gap-4">
            <div className="space-y-2">
              <Label htmlFor="password">Password *</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Create a password"
                  value={formData.password}
                  onChange={handleInputChange('password')}
                  className={cn(
                    'pl-10 pr-10',
                    errors.password && 'border-red-500'
                  )}
                  disabled={registerLoading}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  disabled={registerLoading}
                >
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
              <InlineError message={errors.password} />

              {/* Password Strength Indicator */}
              {formData.password && (
                <PasswordStrengthIndicator
                  password={formData.password}
                  strength={passwordStrength}
                  className="mt-3"
                />
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password *</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange('confirmPassword')}
                  className={cn(
                    'pl-10 pr-10',
                    errors.confirmPassword && 'border-red-500',
                    passwordsMatch === true && 'border-green-500',
                    passwordsMatch === false && 'border-red-500'
                  )}
                  disabled={registerLoading}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  disabled={registerLoading}
                >
                  {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>

                {/* Password Match Indicator */}
                {formData.confirmPassword && passwordsMatch !== null && (
                  <div className="absolute right-10 top-1/2 transform -translate-y-1/2">
                    {passwordsMatch ? (
                      <Check className="w-4 h-4 text-green-500" />
                    ) : (
                      <X className="w-4 h-4 text-red-500" />
                    )}
                  </div>
                )}
              </div>
              <InlineError message={errors.confirmPassword} />

              {/* Password Match Feedback */}
              {formData.confirmPassword && passwordsMatch !== null && (
                <div className={cn(
                  'text-xs flex items-center space-x-1',
                  passwordsMatch ? 'text-green-600' : 'text-red-600'
                )}>
                  {passwordsMatch ? (
                    <>
                      <Check className="w-3 h-3" />
                      <span>Passwords match</span>
                    </>
                  ) : (
                    <>
                      <X className="w-3 h-3" />
                      <span>Passwords do not match</span>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Optional Fields */}
          <div className="space-y-4 pt-2 border-t">
            <h4 className="text-sm font-medium text-muted-foreground">Optional Information</h4>
            
            {/* Phone */}
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  id="phone"
                  type="tel"
                  placeholder="Your phone number"
                  value={formData.phone}
                  onChange={handleInputChange('phone')}
                  className={cn(
                    'pl-10',
                    errors.phone && 'border-red-500'
                  )}
                  disabled={registerLoading}
                  autoComplete="tel"
                />
              </div>
              <InlineError message={errors.phone} />
            </div>

            {/* Location */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="city"
                    placeholder="Your city"
                    value={formData.location.city}
                    onChange={handleLocationChange('city')}
                    className="pl-10"
                    disabled={registerLoading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  placeholder="Your state"
                  value={formData.location.state}
                  onChange={handleLocationChange('state')}
                  disabled={registerLoading}
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <ButtonLoading
            type="submit"
            className="w-full"
            loading={registerLoading}
            loadingText="Creating account..."
            disabled={registerLoading}
          >
            Create Account
          </ButtonLoading>

          {/* Sign In Link */}
          <div className="text-center text-sm">
            <span className="text-muted-foreground">Already have an account? </span>
            <Button
              type="button"
              variant="link"
              className="px-0 font-normal"
              onClick={() => router.push('/login')}
              disabled={registerLoading}
            >
              Sign in
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
