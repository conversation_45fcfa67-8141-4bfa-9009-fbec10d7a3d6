import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// Generic validation middleware
export function withValidation<T>(
  schema: z.ZodSchema<T>,
  handler: (request: NextRequest, data: T, context: Record<string, unknown>) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: Record<string, unknown>) => {
    try {
      let data: unknown

      // Parse request body based on content type
      const contentType = request.headers.get('content-type')
      
      if (contentType?.includes('application/json')) {
        data = await request.json()
      } else if (contentType?.includes('application/x-www-form-urlencoded')) {
        const formData = await request.formData()
        data = Object.fromEntries(formData.entries())
      } else {
        // For GET requests, use URL search params
        const url = new URL(request.url)
        data = Object.fromEntries(url.searchParams.entries())
      }

      // Validate data against schema
      const validatedData = schema.parse(data)

      // Call handler with validated data
      return handler(request, validatedData, context)
    } catch (error) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: 'Validation failed',
            details: error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            }))
          },
          { status: 400 }
        )
      }

      console.error('Validation middleware error:', error)
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      )
    }
  }
}

// Common validation schemas
export const schemas = {
  // Authentication schemas
  login: z.object({
    email: z.string().email('Invalid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    rememberMe: z.boolean().optional()
  }),

  register: z.object({
    email: z.string().email('Invalid email address'),
    password: z.string()
      .min(8, 'Password must be at least 8 characters')
      .regex(/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
    role: z.enum(['job_seeker', 'company_admin'], {
      errorMap: () => ({ message: 'Role must be either job_seeker or company_admin' })
    }),
    phone: z.string().optional(),
    location: z.object({
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().default('United States')
    }).optional()
  }),

  // Profile update schema
  updateProfile: z.object({
    firstName: z.string().min(1, 'First name is required').max(50, 'First name too long').optional(),
    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long').optional(),
    phone: z.string().optional(),
    location: z.object({
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().optional()
    }).optional(),
    bio: z.string().max(1000, 'Bio too long').optional(),
    skills: z.array(z.string()).optional(),
    experience: z.string().optional(),
    education: z.string().optional(),
    portfolioUrl: z.string().url('Invalid portfolio URL').optional().or(z.literal('')),
    linkedinUrl: z.string().url('Invalid LinkedIn URL').optional().or(z.literal(''))
  }),

  // Job search schema
  jobSearch: z.object({
    q: z.string().optional(),
    location: z.string().optional(),
    jobTypes: z.array(z.string()).optional(),
    experienceLevels: z.array(z.string()).optional(),
    salaryRange: z.tuple([z.number(), z.number()]).optional(),
    remote: z.boolean().optional(),
    datePosted: z.enum(['any', '24h', '7d', '30d']).optional(),
    sortBy: z.enum(['relevance', 'date', 'salary']).optional(),
    page: z.number().min(1).optional(),
    limit: z.number().min(1).max(100).optional()
  }),

  // Job application schema
  jobApplication: z.object({
    coverLetter: z.string().min(100, 'Cover letter must be at least 100 characters'),
    resumeId: z.string().min(1, 'Resume is required'),
    customFields: z.record(z.any()).optional()
  }),

  // Company creation schema
  createCompany: z.object({
    name: z.string().min(1, 'Company name is required').max(100, 'Company name too long'),
    description: z.string().min(10, 'Description must be at least 10 characters').max(5000, 'Description too long'),
    website: z.string().url('Invalid website URL').optional().or(z.literal('')),
    industry: z.string().min(1, 'Industry is required'),
    size: z.enum(['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']),
    location: z.object({
      city: z.string().min(1, 'City is required'),
      state: z.string().min(1, 'State is required'),
      country: z.string().default('United States')
    }),
    logo: z.string().url('Invalid logo URL').optional().or(z.literal(''))
  }),

  // Job posting schema
  createJob: z.object({
    title: z.string().min(1, 'Job title is required').max(100, 'Job title too long'),
    description: z.string().min(50, 'Description must be at least 50 characters').max(10000, 'Description too long'),
    type: z.enum(['full-time', 'part-time', 'contract', 'internship']),
    location: z.object({
      city: z.string().optional(),
      state: z.string().optional(),
      country: z.string().default('United States'),
      remote: z.boolean().default(false)
    }),
    salary: z.object({
      min: z.number().min(0).optional(),
      max: z.number().min(0).optional(),
      currency: z.string().default('USD'),
      period: z.enum(['hourly', 'monthly', 'yearly']).default('yearly')
    }).optional(),
    requirements: z.object({
      experience: z.string().min(1, 'Experience requirement is required'),
      education: z.string().min(1, 'Education requirement is required'),
      skills: z.array(z.string()).min(1, 'At least one skill is required')
    }),
    benefits: z.array(z.string()).optional(),
    applicationDeadline: z.string().datetime().optional()
  }),

  // Pagination schema
  pagination: z.object({
    page: z.number().min(1).default(1),
    limit: z.number().min(1).max(100).default(20)
  }),

  // ID parameter schema
  mongoId: z.object({
    id: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid ID format')
  })
}

// Validate MongoDB ObjectId
export function validateObjectId(id: string): boolean {
  return /^[0-9a-fA-F]{24}$/.test(id)
}

// Sanitize input data
export function sanitizeInput(data: unknown): unknown {
  if (typeof data === 'string') {
    return data.trim()
  }

  if (Array.isArray(data)) {
    return data.map(sanitizeInput)
  }

  if (typeof data === 'object' && data !== null) {
    const sanitized: Record<string, unknown> = {}
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value)
    }
    return sanitized
  }

  return data
}

// Rate limiting helper
export function createRateLimiter(windowMs: number, maxRequests: number) {
  const requests = new Map<string, { count: number; resetTime: number }>()

  return (identifier: string): { allowed: boolean; remaining: number; resetTime: number } => {
    const now = Date.now()

    // Clean up old entries
    for (const [key, value] of requests.entries()) {
      if (value.resetTime < now) {
        requests.delete(key)
      }
    }

    const current = requests.get(identifier)
    
    if (!current || current.resetTime < now) {
      // New window
      requests.set(identifier, {
        count: 1,
        resetTime: now + windowMs
      })
      return {
        allowed: true,
        remaining: maxRequests - 1,
        resetTime: now + windowMs
      }
    }

    if (current.count >= maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: current.resetTime
      }
    }

    current.count++
    return {
      allowed: true,
      remaining: maxRequests - current.count,
      resetTime: current.resetTime
    }
  }
}

// Common rate limiters
export const rateLimiters = {
  auth: createRateLimiter(15 * 60 * 1000, 5), // 5 requests per 15 minutes
  api: createRateLimiter(60 * 1000, 100), // 100 requests per minute
  search: createRateLimiter(60 * 1000, 50) // 50 requests per minute
}
