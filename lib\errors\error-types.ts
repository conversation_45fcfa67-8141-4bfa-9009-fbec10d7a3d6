// lib/errors/error-types.ts

export enum ErrorCode {
  // Authentication Errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  EMAIL_NOT_VERIFIED = 'EMAIL_NOT_VERIFIED',
  
  // Validation Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  REQUIRED_FIELD_MISSING = 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT = 'INVALID_FORMAT',
  INVALID_EMAIL_FORMAT = 'INVALID_EMAIL_FORMAT',
  PASSWORD_TOO_WEAK = 'PASSWORD_TOO_WEAK',
  
  // Database Errors
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  NOT_FOUND = 'NOT_FOUND', // Alias for RESOURCE_NOT_FOUND for backward compatibility
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  INVALID_OBJECT_ID = 'INVALID_OBJECT_ID',
  
  // Business Logic Errors
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  SUBSCRIPTION_REQUIRED = 'SUBSCRIPTION_REQUIRED',
  COMPANY_NOT_VERIFIED = 'COMPANY_NOT_VERIFIED',
  JOB_APPLICATION_EXISTS = 'JOB_APPLICATION_EXISTS',
  JOB_EXPIRED = 'JOB_EXPIRED',
  
  // File Upload Errors
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  
  // External Service Errors
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  EMAIL_SEND_FAILED = 'EMAIL_SEND_FAILED',
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  
  // Generic Errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  METHOD_NOT_ALLOWED = 'METHOD_NOT_ALLOWED',
  REQUEST_TIMEOUT = 'REQUEST_TIMEOUT'
}

// Type for error details that can be attached to errors
export type ErrorDetailsValue =
  | string
  | number
  | boolean
  | Date
  | null
  | undefined
  | ErrorDetailsValue[]
  | { [key: string]: ErrorDetailsValue }
  | Record<string, unknown> // Allow any object structure for complex error details

export interface ErrorDetails {
  code: ErrorCode
  message: string
  statusCode: number
  field?: string
  details?: Record<string, ErrorDetailsValue>
  timestamp: Date
  requestId?: string
  stack?: string
}

export class AppError extends Error {
  public readonly code: ErrorCode
  public readonly statusCode: number
  public readonly field?: string
  public readonly details?: Record<string, ErrorDetailsValue>
  public readonly timestamp: Date
  public readonly requestId?: string
  public readonly isOperational: boolean = true

  constructor(errorDetails: Omit<ErrorDetails, 'timestamp'>) {
    super(errorDetails.message)
    
    this.name = 'AppError'
    this.code = errorDetails.code
    this.statusCode = errorDetails.statusCode
    this.field = errorDetails.field
    this.details = errorDetails.details
    this.timestamp = new Date()
    this.requestId = errorDetails.requestId
    
    Error.captureStackTrace(this, this.constructor)
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      field: this.field,
      details: this.details,
      timestamp: this.timestamp,
      requestId: this.requestId,
      stack: process.env.NODE_ENV === 'development' ? this.stack : undefined
    }
  }
}

// Predefined error configurations
export const ERROR_CONFIGS = {
  [ErrorCode.INVALID_CREDENTIALS]: { 
    statusCode: 401, 
    defaultMessage: 'Invalid email or password' 
  },
  [ErrorCode.TOKEN_EXPIRED]: { 
    statusCode: 401, 
    defaultMessage: 'Token has expired' 
  },
  [ErrorCode.TOKEN_INVALID]: { 
    statusCode: 401, 
    defaultMessage: 'Invalid token' 
  },
  [ErrorCode.UNAUTHORIZED]: { 
    statusCode: 401, 
    defaultMessage: 'Authentication required' 
  },
  [ErrorCode.FORBIDDEN]: { 
    statusCode: 403, 
    defaultMessage: 'Access denied' 
  },
  [ErrorCode.EMAIL_NOT_VERIFIED]: { 
    statusCode: 403, 
    defaultMessage: 'Email verification required' 
  },
  [ErrorCode.VALIDATION_ERROR]: { 
    statusCode: 400, 
    defaultMessage: 'Validation failed' 
  },
  [ErrorCode.REQUIRED_FIELD_MISSING]: { 
    statusCode: 400, 
    defaultMessage: 'Required field is missing' 
  },
  [ErrorCode.INVALID_FORMAT]: { 
    statusCode: 400, 
    defaultMessage: 'Invalid format' 
  },
  [ErrorCode.INVALID_EMAIL_FORMAT]: { 
    statusCode: 400, 
    defaultMessage: 'Invalid email format' 
  },
  [ErrorCode.PASSWORD_TOO_WEAK]: { 
    statusCode: 400, 
    defaultMessage: 'Password does not meet security requirements' 
  },
  [ErrorCode.DUPLICATE_ENTRY]: { 
    statusCode: 409, 
    defaultMessage: 'Resource already exists' 
  },
  [ErrorCode.RESOURCE_NOT_FOUND]: {
    statusCode: 404,
    defaultMessage: 'Resource not found'
  },
  [ErrorCode.NOT_FOUND]: {
    statusCode: 404,
    defaultMessage: 'Resource not found'
  },
  [ErrorCode.DATABASE_CONNECTION_ERROR]: { 
    statusCode: 500, 
    defaultMessage: 'Database connection failed' 
  },
  [ErrorCode.INVALID_OBJECT_ID]: { 
    statusCode: 400, 
    defaultMessage: 'Invalid ID format' 
  },
  [ErrorCode.INSUFFICIENT_PERMISSIONS]: { 
    statusCode: 403, 
    defaultMessage: 'Insufficient permissions' 
  },
  [ErrorCode.QUOTA_EXCEEDED]: { 
    statusCode: 429, 
    defaultMessage: 'Quota exceeded' 
  },
  [ErrorCode.SUBSCRIPTION_REQUIRED]: { 
    statusCode: 402, 
    defaultMessage: 'Subscription required' 
  },
  [ErrorCode.COMPANY_NOT_VERIFIED]: { 
    statusCode: 403, 
    defaultMessage: 'Company verification required' 
  },
  [ErrorCode.JOB_APPLICATION_EXISTS]: { 
    statusCode: 409, 
    defaultMessage: 'Application already exists for this job' 
  },
  [ErrorCode.JOB_EXPIRED]: { 
    statusCode: 410, 
    defaultMessage: 'Job posting has expired' 
  },
  [ErrorCode.FILE_TOO_LARGE]: { 
    statusCode: 413, 
    defaultMessage: 'File size too large' 
  },
  [ErrorCode.INVALID_FILE_TYPE]: { 
    statusCode: 400, 
    defaultMessage: 'Invalid file type' 
  },
  [ErrorCode.UPLOAD_FAILED]: { 
    statusCode: 500, 
    defaultMessage: 'File upload failed' 
  },
  [ErrorCode.FILE_NOT_FOUND]: { 
    statusCode: 404, 
    defaultMessage: 'File not found' 
  },
  [ErrorCode.PAYMENT_FAILED]: { 
    statusCode: 402, 
    defaultMessage: 'Payment processing failed' 
  },
  [ErrorCode.EMAIL_SEND_FAILED]: { 
    statusCode: 500, 
    defaultMessage: 'Email sending failed' 
  },
  [ErrorCode.AI_SERVICE_ERROR]: { 
    statusCode: 503, 
    defaultMessage: 'AI service unavailable' 
  },
  [ErrorCode.EXTERNAL_API_ERROR]: { 
    statusCode: 502, 
    defaultMessage: 'External service error' 
  },
  [ErrorCode.INTERNAL_SERVER_ERROR]: { 
    statusCode: 500, 
    defaultMessage: 'Internal server error' 
  },
  [ErrorCode.RATE_LIMIT_EXCEEDED]: { 
    statusCode: 429, 
    defaultMessage: 'Rate limit exceeded' 
  },
  [ErrorCode.SERVICE_UNAVAILABLE]: { 
    statusCode: 503, 
    defaultMessage: 'Service temporarily unavailable' 
  },
  [ErrorCode.METHOD_NOT_ALLOWED]: { 
    statusCode: 405, 
    defaultMessage: 'Method not allowed' 
  },
  [ErrorCode.REQUEST_TIMEOUT]: { 
    statusCode: 408, 
    defaultMessage: 'Request timeout' 
  }
} as const
