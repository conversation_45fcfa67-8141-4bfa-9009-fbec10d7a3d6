// app/api/v1/companies/[id]/settings/route.ts
import { NextRequest } from 'next/server'
import { companySettingsService } from '@/lib/services/company-settings.service'
import { errorService, ErrorCode } from '@/lib/error-service'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON>eth<PERSON>,
  validateRequestBody,
  createSuccessResponse
} from '@/lib/middleware'
import { updateCompanySettingsRequestSchema } from '@/types/company-settings.types'

interface RouteContext {
  params: Record<string, string>
}

// GET /api/v1/companies/[id]/settings - Get company settings
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['GET'])

  const companyId = params.id
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  const result = await companySettingsService.getCompanySettings(companyId)
  
  return createSuccessResponse(result.data, 200, result.message)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// PUT /api/v1/companies/[id]/settings - Update company settings
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['PUT'])

  const companyId = params.id
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  // Validate request body
  const requestData = await validateRequestBody(request, updateCompanySettingsRequestSchema)
  
  const result = await companySettingsService.updateCompanySettings(
    companyId,
    requestData.settings,
    userId
  )
  
  return createSuccessResponse(result.data, 200, result.message)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// POST /api/v1/companies/[id]/settings/reset - Reset settings to defaults
export const POST = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['POST'])

  const companyId = params.id
  const { searchParams } = new URL(request.url)
  
  // Only allow reset action
  if (searchParams.get('action') !== 'reset') {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Invalid action. Only "reset" is allowed.',
      'action'
    )
  }
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  const result = await companySettingsService.resetCompanySettings(companyId, userId)
  
  return createSuccessResponse(result.data, 200, result.message)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// PATCH /api/v1/companies/[id]/settings - Partial update of specific settings sections
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  validateMethod(request, ['PATCH'])

  const companyId = params.id
  const { searchParams } = new URL(request.url)
  const section = searchParams.get('section')
  
  if (!companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Company ID is required',
      'companyId'
    )
  }

  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  // Validate request body for partial updates
  const requestData = await validateRequestBody(request, updateCompanySettingsRequestSchema)
  
  // If section is specified, validate that only that section is being updated
  if (section) {
    const allowedSections = [
      'emailNotifications',
      'pushNotifications', 
      'branding',
      'teamSettings',
      'integrations',
      'advanced'
    ]
    
    if (!allowedSections.includes(section)) {
      throw errorService.createError(
        ErrorCode.BAD_REQUEST,
        `Invalid section. Allowed sections: ${allowedSections.join(', ')}`,
        'section'
      )
    }

    // Ensure only the specified section is being updated
    const sectionKeys = Object.keys(requestData.settings)
    if (sectionKeys.length !== 1 || !sectionKeys.includes(section)) {
      throw errorService.createError(
        ErrorCode.BAD_REQUEST,
        `When using section parameter, only ${section} can be updated`,
        'settings'
      )
    }
  }
  
  const result = await companySettingsService.updateCompanySettings(
    companyId,
    requestData.settings,
    userId
  )
  
  return createSuccessResponse(result.data, 200, result.message)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['company_admin', 'admin', 'super_admin']
})

// Method not allowed for other HTTP methods
export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed. Use POST with action=reset to reset settings.'
  )
}

export async function HEAD() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'HEAD method not allowed.'
  )
}

export async function OPTIONS() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'OPTIONS method not allowed.'
  )
}
