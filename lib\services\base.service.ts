import { errorService } from '@/lib/errors/error-service'
import { ErrorCode, ErrorDetailsValue } from '@/lib/errors/error-types'

export interface PaginationOptions {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface SearchOptions extends PaginationOptions {
  searchTerm?: string
  filters?: Record<string, unknown>
}

export abstract class BaseService {
  /**
   * Create pagination metadata
   */
  protected createPaginationMeta(
    page: number,
    limit: number,
    total: number
  ): PaginationResult<unknown>['pagination'] {
    const pages = Math.ceil(total / limit)
    
    return {
      page,
      limit,
      total,
      pages,
      hasNext: page < pages,
      hasPrev: page > 1
    }
  }

  /**
   * Validate pagination parameters
   */
  protected validatePaginationParams(page?: number, limit?: number): { page: number; limit: number } {
    const validatedPage = Math.max(1, page || 1)
    const validatedLimit = Math.min(100, Math.max(1, limit || 10)) // Max 100 items per page
    
    return {
      page: validatedPage,
      limit: validatedLimit
    }
  }

  /**
   * Build MongoDB sort object
   */
  protected buildSortObject(sortBy?: string, sortOrder?: 'asc' | 'desc'): Record<string, 1 | -1> {
    if (!sortBy) {
      return { createdAt: -1 } // Default sort by creation date, newest first
    }
    
    return {
      [sortBy]: sortOrder === 'asc' ? 1 : -1
    }
  }

  /**
   * Build search query for text fields
   */
  protected buildTextSearchQuery(searchTerm: string, fields: string[]): Record<string, unknown> {
    if (!searchTerm || !fields.length) {
      return {}
    }
    
    return {
      $or: fields.map(field => ({
        [field]: { $regex: searchTerm, $options: 'i' }
      }))
    }
  }

  /**
   * Validate ObjectId format
   */
  protected validateObjectId(id: string, fieldName: string = 'id'): void {
    const objectIdRegex = /^[0-9a-fA-F]{24}$/
    
    if (!objectIdRegex.test(id)) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Invalid ${fieldName} format`,
        fieldName
      )
    }
  }

  /**
   * Sanitize user input
   */
  protected sanitizeInput(input: unknown): unknown {
    if (typeof input === 'string') {
      return input.trim()
    }
    
    if (Array.isArray(input)) {
      return input.map(item => this.sanitizeInput(item))
    }
    
    if (typeof input === 'object' && input !== null) {
      const sanitized: Record<string, unknown> = {}
      for (const [key, value] of Object.entries(input)) {
        sanitized[key] = this.sanitizeInput(value)
      }
      return sanitized
    }
    
    return input
  }

  /**
   * Check if user has permission for resource
   */
  protected checkResourcePermission(
    resourceOwnerId: string,
    currentUserId: string,
    userRole: string,
    allowedRoles: string[] = ['admin', 'super_admin']
  ): void {
    const isOwner = resourceOwnerId === currentUserId
    const hasAdminRole = allowedRoles.includes(userRole)
    
    if (!isOwner && !hasAdminRole) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'You do not have permission to access this resource',
        'permission'
      )
    }
  }

  /**
   * Format date for API response
   */
  protected formatDate(date: Date): string {
    return date.toISOString()
  }

  /**
   * Generate slug from text
   */
  protected generateSlug(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  /**
   * Validate required fields
   */
  protected validateRequiredFields(data: Record<string, unknown>, requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => {
      const value = data[field]
      return value === undefined || value === null || (typeof value === 'string' && value.trim() === '')
    })
    
    if (missingFields.length > 0) {
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Missing required fields: ${missingFields.join(', ')}`,
        undefined,
        { missingFields }
      )
    }
  }

  /**
   * Create standardized error response
   */
  protected createNotFoundError(resource: string, identifier?: string): never {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      `${resource} not found${identifier ? ` with identifier: ${identifier}` : ''}`,
      'resource'
    )
  }

  /**
   * Create standardized duplicate error
   */
  protected createDuplicateError(resource: string, field: string): never {
    throw errorService.createError(
      ErrorCode.DUPLICATE_ENTRY,
      `${resource} with this ${field} already exists`,
      field
    )
  }

  /**
   * Create standardized validation error
   */
  protected createValidationError(message: string, field?: string, details?: Record<string, ErrorDetailsValue>): never {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      message,
      field,
      details
    )
  }

  /**
   * Log service operation
   */
  protected logOperation(operation: string, data?: Record<string, unknown>): void {
    console.log(`[${this.constructor.name}] ${operation}`, data ? JSON.stringify(data) : '')
  }

  /**
   * Handle database errors
   */
  protected handleDatabaseError(error: unknown, operation: string): never {
    console.error(`[${this.constructor.name}] Database error in ${operation}:`, error)
    
    // Handle specific MongoDB errors
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>

      if (errorObj.code === 11000) {
        const keyPattern = errorObj.keyPattern as Record<string, unknown> || {}
        const field = Object.keys(keyPattern)[0] || 'field'
        this.createDuplicateError('Resource', field)
      }
    }
    
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as Record<string, unknown>

      if (errorObj.name === 'ValidationError' && errorObj.errors) {
        const errors = errorObj.errors as Record<string, { message: string }>
        const validationErrors = Object.values(errors).map(err => err.message)
        this.createValidationError(`Validation failed: ${validationErrors.join(', ')}`)
      }

      if (errorObj.name === 'CastError') {
        const path = String(errorObj.path || 'field')
        const value = String(errorObj.value || 'unknown')
        this.createValidationError(`Invalid ${path}: ${value}`, path)
      }
    }
    
    // Generic database error
    throw errorService.createError(
      ErrorCode.INTERNAL_SERVER_ERROR,
      'Database operation failed',
      'database'
    )
  }
}

export default BaseService
