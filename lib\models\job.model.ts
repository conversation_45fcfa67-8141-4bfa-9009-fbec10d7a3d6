import mongoose, { Schema, Document } from 'mongoose'

export interface IJob extends Document {
  title: string
  description: string
  companyId: mongoose.Types.ObjectId
  location: {
    city?: string
    state?: string
    country?: string
    remote?: boolean
    address?: string
  }
  salary?: {
    min?: number
    max?: number
    currency?: string
    period?: 'hourly' | 'monthly' | 'yearly'
  }
  requirements: string[]
  benefits?: string[]
  type: 'full-time' | 'part-time' | 'contract' | 'internship'
  level: 'entry' | 'mid' | 'senior' | 'executive'
  category: string
  tags: string[]
  isActive: boolean
  isRemote: boolean
  applicationDeadline?: Date
  createdBy: mongoose.Types.ObjectId
  applicationsCount: number
  viewsCount: number
  createdAt: Date
  updatedAt: Date
}

const jobSchema = new Schema<IJob>({
  title: {
    type: String,
    required: [true, 'Job title is required'],
    trim: true,
    maxlength: [200, 'Job title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Job description is required'],
    trim: true,
    maxlength: [10000, 'Job description cannot exceed 10000 characters']
  },
  companyId: {
    type: Schema.Types.ObjectId,
    ref: 'Company',
    required: [true, 'Company ID is required']
  },
  location: {
    city: {
      type: String,
      trim: true,
      maxlength: [100, 'City name cannot exceed 100 characters']
    },
    state: {
      type: String,
      trim: true,
      maxlength: [100, 'State name cannot exceed 100 characters']
    },
    country: {
      type: String,
      trim: true,
      maxlength: [100, 'Country name cannot exceed 100 characters']
    },
    remote: {
      type: Boolean,
      default: false
    },
    address: {
      type: String,
      trim: true,
      maxlength: [500, 'Address cannot exceed 500 characters']
    }
  },
  salary: {
    min: {
      type: Number,
      min: [0, 'Minimum salary cannot be negative']
    },
    max: {
      type: Number,
      min: [0, 'Maximum salary cannot be negative'],
      validate: {
        validator: function(this: IJob, value: number) {
          return !this.salary?.min || value >= this.salary.min
        },
        message: 'Maximum salary must be greater than or equal to minimum salary'
      }
    },
    currency: {
      type: String,
      enum: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'INR'],
      default: 'USD'
    },
    period: {
      type: String,
      enum: ['hourly', 'monthly', 'yearly'],
      default: 'yearly'
    }
  },
  requirements: [{
    type: String,
    trim: true,
    maxlength: [500, 'Each requirement cannot exceed 500 characters']
  }],
  benefits: [{
    type: String,
    trim: true,
    maxlength: [200, 'Each benefit cannot exceed 200 characters']
  }],
  type: {
    type: String,
    enum: ['full-time', 'part-time', 'contract', 'internship'],
    required: [true, 'Job type is required']
  },
  level: {
    type: String,
    enum: ['entry', 'mid', 'senior', 'executive'],
    required: [true, 'Job level is required']
  },
  category: {
    type: String,
    required: [true, 'Job category is required'],
    trim: true,
    maxlength: [100, 'Category cannot exceed 100 characters']
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
    maxlength: [50, 'Each tag cannot exceed 50 characters']
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  isRemote: {
    type: Boolean,
    default: false
  },
  applicationDeadline: {
    type: Date,
    validate: {
      validator: function(value: Date) {
        return !value || value > new Date()
      },
      message: 'Application deadline must be in the future'
    }
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Creator ID is required']
  },
  applicationsCount: {
    type: Number,
    default: 0,
    min: [0, 'Applications count cannot be negative']
  },
  viewsCount: {
    type: Number,
    default: 0,
    min: [0, 'Views count cannot be negative']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for better query performance
jobSchema.index({ companyId: 1, isActive: 1 })
jobSchema.index({ category: 1, isActive: 1 })
jobSchema.index({ type: 1, level: 1, isActive: 1 })
jobSchema.index({ 'location.city': 1, 'location.state': 1, 'location.country': 1 })
jobSchema.index({ isRemote: 1, isActive: 1 })
jobSchema.index({ tags: 1, isActive: 1 })
jobSchema.index({ createdAt: -1 })
jobSchema.index({ applicationDeadline: 1, isActive: 1 })

// Text search index
jobSchema.index({
  title: 'text',
  description: 'text',
  category: 'text',
  tags: 'text'
})

// Virtual for checking if application deadline has passed
jobSchema.virtual('isExpired').get(function(this: IJob) {
  return this.applicationDeadline ? this.applicationDeadline < new Date() : false
})

// Pre-save middleware to set isRemote based on location
jobSchema.pre('save', function(this: IJob) {
  if (this.location?.remote) {
    this.isRemote = true
  }
})

// Static methods
jobSchema.statics.findActiveJobs = function() {
  return this.find({ isActive: true, $or: [{ applicationDeadline: { $gte: new Date() } }, { applicationDeadline: null }] })
}

jobSchema.statics.findByCompany = function(companyId: string) {
  return this.find({ companyId, isActive: true })
}

jobSchema.statics.searchJobs = function(searchTerm: string) {
  return this.find({
    $text: { $search: searchTerm },
    isActive: true
  }).sort({ score: { $meta: 'textScore' } })
}

// Instance methods
jobSchema.methods.incrementViews = function() {
  this.viewsCount += 1
  return this.save()
}

jobSchema.methods.incrementApplications = function() {
  this.applicationsCount += 1
  return this.save()
}

jobSchema.methods.decrementApplications = function() {
  if (this.applicationsCount > 0) {
    this.applicationsCount -= 1
    return this.save()
  }
  return Promise.resolve(this)
}

export const Job = mongoose.models.Job || mongoose.model<IJob>('Job', jobSchema)
