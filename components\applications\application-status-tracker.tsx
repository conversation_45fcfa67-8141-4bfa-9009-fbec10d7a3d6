'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  CheckCircle,
  Clock,
  Eye,
  MessageCircle,
  Calendar,
  User,
  Building,
  MapPin,
  DollarSign,
  FileText,
  Phone,
  Video,
  Award,
  XCircle,
  AlertCircle,
  ArrowRight
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Application {
  _id: string
  status: 'submitted' | 'under_review' | 'interview_scheduled' | 'interview_completed' | 'offer_extended' | 'hired' | 'rejected'
  submittedAt: Date
  lastUpdated: Date
  job: {
    _id: string
    title: string
    company: {
      name: string
      logo?: string
    }
    location: {
      city: string
      state: string
      remote: boolean
    }
    salary?: {
      min?: number
      max?: number
      currency: string
    }
  }
  timeline: Array<{
    status: string
    date: Date
    note?: string
    actor?: string
  }>
  interviews?: Array<{
    type: 'phone' | 'video' | 'in_person'
    scheduledAt: Date
    duration: number
    interviewer?: string
    notes?: string
    status: 'scheduled' | 'completed' | 'cancelled'
  }>
  feedback?: string
  nextSteps?: string
}

interface ApplicationStatusTrackerProps {
  application: Application
  onViewJob?: () => void
  onWithdraw?: () => void
  className?: string
}

const STATUS_CONFIG = {
  submitted: {
    label: 'Application Submitted',
    color: 'bg-blue-100 text-blue-800',
    icon: FileText,
    progress: 20
  },
  under_review: {
    label: 'Under Review',
    color: 'bg-yellow-100 text-yellow-800',
    icon: Eye,
    progress: 40
  },
  interview_scheduled: {
    label: 'Interview Scheduled',
    color: 'bg-purple-100 text-purple-800',
    icon: Calendar,
    progress: 60
  },
  interview_completed: {
    label: 'Interview Completed',
    color: 'bg-indigo-100 text-indigo-800',
    icon: CheckCircle,
    progress: 80
  },
  offer_extended: {
    label: 'Offer Extended',
    color: 'bg-green-100 text-green-800',
    icon: Award,
    progress: 90
  },
  hired: {
    label: 'Hired',
    color: 'bg-green-100 text-green-800',
    icon: Award,
    progress: 100
  },
  rejected: {
    label: 'Not Selected',
    color: 'bg-red-100 text-red-800',
    icon: XCircle,
    progress: 100
  }
}

export function ApplicationStatusTracker({ 
  application, 
  onViewJob, 
  onWithdraw,
  className 
}: ApplicationStatusTrackerProps) {
  const statusConfig = STATUS_CONFIG[application.status]
  const StatusIcon = statusConfig.icon

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDateTime = (date: Date) => {
    return new Date(date).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit'
    })
  }

  const getTimeAgo = (date: Date) => {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return '1 day ago'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
    return `${Math.ceil(diffDays / 30)} months ago`
  }

  const getNextInterview = () => {
    if (!application.interviews) return null
    
    const upcoming = application.interviews
      .filter(interview => interview.status === 'scheduled' && new Date(interview.scheduledAt) > new Date())
      .sort((a, b) => new Date(a.scheduledAt).getTime() - new Date(b.scheduledAt).getTime())
    
    return upcoming[0] || null
  }

  const nextInterview = getNextInterview()

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg mb-2">{application.job.title}</CardTitle>
            <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3">
              <div className="flex items-center space-x-1">
                <Building className="w-4 h-4" />
                <span>{application.job.company.name}</span>
              </div>
              <div className="flex items-center space-x-1">
                <MapPin className="w-4 h-4" />
                <span>
                  {application.job.location.remote 
                    ? 'Remote' 
                    : `${application.job.location.city}, ${application.job.location.state}`
                  }
                </span>
              </div>
              {application.job.salary && (
                <div className="flex items-center space-x-1">
                  <DollarSign className="w-4 h-4" />
                  <span>
                    {application.job.salary.min && application.job.salary.max 
                      ? `$${application.job.salary.min.toLocaleString()} - $${application.job.salary.max.toLocaleString()}`
                      : application.job.salary.min 
                      ? `From $${application.job.salary.min.toLocaleString()}`
                      : 'Competitive'
                    }
                  </span>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>Applied {getTimeAgo(application.submittedAt)}</span>
              <span>•</span>
              <span>Last updated {getTimeAgo(application.lastUpdated)}</span>
            </div>
          </div>
          
          <div className="flex flex-col items-end space-y-2">
            <Badge className={statusConfig.color}>
              <StatusIcon className="w-3 h-3 mr-1" />
              {statusConfig.label}
            </Badge>
            {onViewJob && (
              <Button variant="outline" size="sm" onClick={onViewJob}>
                View Job
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Application Progress</span>
            <span>{statusConfig.progress}%</span>
          </div>
          <Progress value={statusConfig.progress} className="h-2" />
        </div>

        {/* Next Interview */}
        {nextInterview && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                {nextInterview.type === 'phone' && <Phone className="w-4 h-4 text-blue-600" />}
                {nextInterview.type === 'video' && <Video className="w-4 h-4 text-blue-600" />}
                {nextInterview.type === 'in_person' && <User className="w-4 h-4 text-blue-600" />}
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-blue-900 mb-1">
                  Upcoming {nextInterview.type.replace('_', ' ')} Interview
                </h4>
                <p className="text-sm text-blue-800 mb-1">
                  {formatDateTime(nextInterview.scheduledAt)} ({nextInterview.duration} minutes)
                </p>
                {nextInterview.interviewer && (
                  <p className="text-sm text-blue-700">
                    with {nextInterview.interviewer}
                  </p>
                )}
              </div>
              <Button size="sm" variant="outline" className="border-blue-300 text-blue-700">
                View Details
              </Button>
            </div>
          </div>
        )}

        {/* Feedback */}
        {application.feedback && (
          <div className="p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium mb-2 flex items-center">
              <MessageCircle className="w-4 h-4 mr-2" />
              Feedback
            </h4>
            <p className="text-sm text-muted-foreground">{application.feedback}</p>
          </div>
        )}

        {/* Next Steps */}
        {application.nextSteps && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2 flex items-center">
              <ArrowRight className="w-4 h-4 mr-2" />
              Next Steps
            </h4>
            <p className="text-sm text-green-800">{application.nextSteps}</p>
          </div>
        )}

        <Separator />

        {/* Timeline */}
        <div>
          <h4 className="font-medium mb-4">Application Timeline</h4>
          <div className="space-y-4">
            {application.timeline.map((event, index) => {
              const isLast = index === application.timeline.length - 1
              const eventConfig = STATUS_CONFIG[event.status as keyof typeof STATUS_CONFIG]
              const EventIcon = eventConfig?.icon || Clock
              
              return (
                <div key={index} className="flex items-start space-x-3">
                  <div className="relative">
                    <div className={cn(
                      'w-8 h-8 rounded-full flex items-center justify-center border-2',
                      eventConfig ? eventConfig.color.replace('text-', 'border-').replace('bg-', 'bg-') : 'border-muted-foreground bg-muted'
                    )}>
                      <EventIcon className="w-4 h-4" />
                    </div>
                    {!isLast && (
                      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-0.5 h-6 bg-muted-foreground/20" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="font-medium text-sm">
                        {eventConfig?.label || event.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </p>
                      <span className="text-xs text-muted-foreground">
                        {formatDate(event.date)}
                      </span>
                    </div>
                    {event.note && (
                      <p className="text-sm text-muted-foreground mt-1">{event.note}</p>
                    )}
                    {event.actor && (
                      <p className="text-xs text-muted-foreground mt-1">by {event.actor}</p>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Actions */}
        {application.status !== 'hired' && application.status !== 'rejected' && (
          <div className="flex justify-between pt-4 border-t">
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <MessageCircle className="w-4 h-4 mr-2" />
                Contact Recruiter
              </Button>
            </div>
            {onWithdraw && (
              <Button variant="outline" size="sm" onClick={onWithdraw} className="text-red-600 hover:text-red-700">
                Withdraw Application
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
