import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validate<PERSON>eth<PERSON> } from '@/lib/api/route-handler'
import { notificationService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/v1/notifications/[id] - Get notification by ID
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['GET'])
  
  const notificationId = params.id
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const result = await notificationService.getNotificationById(notificationId, userId)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true,
  requireAuth: true
})

// PUT /api/v1/notifications/[id] - Mark notification as read/unread
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['PUT'])
  
  const notificationId = params.id
  const { searchParams } = new URL(request.url)
  const action = searchParams.get('action')
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  if (action === 'markRead') {
    await notificationService.markAsRead(notificationId, userId)
    return createSuccessResponse({ message: 'Notification marked as read' })
  } else {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      'Invalid action. Use action=markRead to mark notification as read',
      'action'
    )
  }
}, {
  requireDatabase: true,
  requireAuth: true
})

// DELETE /api/v1/notifications/[id] - Delete notification
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  validateMethod(request, ['DELETE'])
  
  const notificationId = params.id
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  await notificationService.deleteNotification(notificationId, userId)
  
  return createSuccessResponse({ message: 'Notification deleted successfully' })
}, {
  requireDatabase: true,
  requireAuth: true
})

// Method not allowed for other HTTP methods
export async function POST() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'POST method not allowed for individual notification resources'
  )
}
