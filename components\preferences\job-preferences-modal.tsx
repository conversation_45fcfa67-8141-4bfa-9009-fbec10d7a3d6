'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useLocationStore } from '@/stores/location-store'
import { 
  MapPin, 
  DollarSign, 
  Briefcase, 
  Clock, 
  Globe,
  Target,
  Filter,
  Settings,
  CheckCircle,
  X,
  Plus,
  Minus,
  Save,
  RotateCcw
} from 'lucide-react'

interface JobPreferencesModalProps {
  isOpen: boolean
  onClose: () => void
}

const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship']
const experienceLevels = ['Entry Level', 'Mid Level', 'Senior Level', 'Executive']
const workModels = ['On-site', 'Remote', 'Hybrid']
const industries = [
  'Technology', 'Healthcare', 'Finance', 'Education', 'Marketing', 
  'Sales', 'Design', 'Engineering', 'Consulting', 'Retail', 
  'Manufacturing', 'Media', 'Legal', 'Real Estate', 'Other'
]
const companyTypes = ['Startup', 'Small Business', 'Corporation', 'Non-profit', 'Government', 'Agency']

export function JobPreferencesModal({ isOpen, onClose }: JobPreferencesModalProps) {
  const { 
    currentLocation, 
    jobPreferences, 
    updateJobPreferences, 
    resetJobPreferences 
  } = useLocationStore()

  const [localPreferences, setLocalPreferences] = useState(jobPreferences)
  const [activeTab, setActiveTab] = useState('job-types')

  const handleSave = () => {
    updateJobPreferences(localPreferences)
    onClose()
  }

  const handleReset = () => {
    resetJobPreferences()
    setLocalPreferences(jobPreferences)
  }

  const toggleArrayItem = (array: string[], item: string) => {
    return array.includes(item) 
      ? array.filter(i => i !== item)
      : [...array, item]
  }

  const addSpecificLocation = (location: string) => {
    if (location && !localPreferences.locations.specificLocations.includes(location)) {
      setLocalPreferences(prev => ({
        ...prev,
        locations: {
          ...prev.locations,
          specificLocations: [...prev.locations.specificLocations, location]
        }
      }))
    }
  }

  const removeSpecificLocation = (location: string) => {
    setLocalPreferences(prev => ({
      ...prev,
      locations: {
        ...prev.locations,
        specificLocations: prev.locations.specificLocations.filter(l => l !== location)
      }
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        <div className="flex flex-col h-full max-h-[90vh]">
          {/* Header */}
          <DialogHeader className="p-6 pb-4 border-b">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Settings className="w-5 h-5 text-primary" />
              </div>
              <div>
                <DialogTitle className="text-2xl">Job Preferences</DialogTitle>
                <DialogDescription className="text-base">
                  Customize your job discovery experience with personalized filters and preferences
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-6">
                <TabsTrigger value="job-types" className="flex items-center space-x-2">
                  <Briefcase className="w-4 h-4" />
                  <span className="hidden sm:inline">Job Types</span>
                </TabsTrigger>
                <TabsTrigger value="location" className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4" />
                  <span className="hidden sm:inline">Location</span>
                </TabsTrigger>
                <TabsTrigger value="salary" className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4" />
                  <span className="hidden sm:inline">Salary</span>
                </TabsTrigger>
                <TabsTrigger value="advanced" className="flex items-center space-x-2">
                  <Filter className="w-4 h-4" />
                  <span className="hidden sm:inline">Advanced</span>
                </TabsTrigger>
              </TabsList>

              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <TabsContent value="job-types" className="space-y-6">
                    {/* Job Types */}
                    <Card className="card-premium">
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <Briefcase className="w-5 h-5" />
                          <span>Job Types</span>
                        </CardTitle>
                        <CardDescription>Select the types of positions you're interested in</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {jobTypes.map(type => (
                            <div key={type} className="flex items-center space-x-2">
                              <Checkbox
                                id={`job-type-${type}`}
                                checked={localPreferences.jobTypes.includes(type)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      jobTypes: [...prev.jobTypes, type]
                                    }))
                                  } else {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      jobTypes: prev.jobTypes.filter(t => t !== type)
                                    }))
                                  }
                                }}
                              />
                              <Label htmlFor={`job-type-${type}`} className="text-sm font-medium">
                                {type}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Experience Levels */}
                    <Card className="card-premium">
                      <CardHeader>
                        <CardTitle>Experience Levels</CardTitle>
                        <CardDescription>Choose your preferred experience levels</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-3">
                          {experienceLevels.map(level => (
                            <div key={level} className="flex items-center space-x-2">
                              <Checkbox
                                id={`exp-level-${level}`}
                                checked={localPreferences.experienceLevels.includes(level)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      experienceLevels: [...prev.experienceLevels, level]
                                    }))
                                  } else {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      experienceLevels: prev.experienceLevels.filter(l => l !== level)
                                    }))
                                  }
                                }}
                              />
                              <Label htmlFor={`exp-level-${level}`} className="text-sm font-medium">
                                {level}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Work Models */}
                    <Card className="card-premium">
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <Globe className="w-5 h-5" />
                          <span>Work Models</span>
                        </CardTitle>
                        <CardDescription>Select your preferred work arrangements</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-3 gap-3">
                          {workModels.map(model => (
                            <div key={model} className="flex items-center space-x-2">
                              <Checkbox
                                id={`work-model-${model}`}
                                checked={localPreferences.workModels.includes(model)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      workModels: [...prev.workModels, model]
                                    }))
                                  } else {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      workModels: prev.workModels.filter(m => m !== model)
                                    }))
                                  }
                                }}
                              />
                              <Label htmlFor={`work-model-${model}`} className="text-sm font-medium">
                                {model}
                              </Label>
                            </div>
                          ))}
                        </div>
                        
                        <div className="mt-4 pt-4 border-t">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="remote-only"
                              checked={localPreferences.remoteOnly}
                              onCheckedChange={(checked) => {
                                setLocalPreferences(prev => ({
                                  ...prev,
                                  remoteOnly: checked as boolean
                                }))
                              }}
                            />
                            <Label htmlFor="remote-only" className="text-sm font-medium">
                              Show only remote positions
                            </Label>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="location" className="space-y-6">
                    {/* Current Location */}
                    {currentLocation && (
                      <Card className="card-premium">
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2">
                            <Target className="w-5 h-5 text-green-500" />
                            <span>Current Location</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center space-x-2 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                            <MapPin className="w-4 h-4 text-green-500" />
                            <span className="font-medium">
                              {currentLocation.city}, {currentLocation.region}, {currentLocation.country}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {/* Location Preferences */}
                    <Card className="card-premium">
                      <CardHeader>
                        <CardTitle>Location Preferences</CardTitle>
                        <CardDescription>Choose which locations to include in your job search</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="include-local"
                              checked={localPreferences.locations.includeLocal}
                              onCheckedChange={(checked) => {
                                setLocalPreferences(prev => ({
                                  ...prev,
                                  locations: { ...prev.locations, includeLocal: checked as boolean }
                                }))
                              }}
                            />
                            <Label htmlFor="include-local">Local jobs (your city)</Label>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="include-regional"
                              checked={localPreferences.locations.includeRegional}
                              onCheckedChange={(checked) => {
                                setLocalPreferences(prev => ({
                                  ...prev,
                                  locations: { ...prev.locations, includeRegional: checked as boolean }
                                }))
                              }}
                            />
                            <Label htmlFor="include-regional">Regional jobs (your state/province)</Label>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="include-national"
                              checked={localPreferences.locations.includeNational}
                              onCheckedChange={(checked) => {
                                setLocalPreferences(prev => ({
                                  ...prev,
                                  locations: { ...prev.locations, includeNational: checked as boolean }
                                }))
                              }}
                            />
                            <Label htmlFor="include-national">National jobs (your country)</Label>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="include-continental"
                              checked={localPreferences.locations.includeContinental}
                              onCheckedChange={(checked) => {
                                setLocalPreferences(prev => ({
                                  ...prev,
                                  locations: { ...prev.locations, includeContinental: checked as boolean }
                                }))
                              }}
                            />
                            <Label htmlFor="include-continental">Continental jobs ({currentLocation?.continent || 'your continent'})</Label>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="include-international"
                              checked={localPreferences.locations.includeInternational}
                              onCheckedChange={(checked) => {
                                setLocalPreferences(prev => ({
                                  ...prev,
                                  locations: { ...prev.locations, includeInternational: checked as boolean }
                                }))
                              }}
                            />
                            <Label htmlFor="include-international">International jobs</Label>
                          </div>
                        </div>

                        {/* Max Distance */}
                        <div className="pt-4 border-t">
                          <Label className="text-sm font-medium mb-3 block">
                            Maximum commute distance: {localPreferences.locations.maxDistance}km
                          </Label>
                          <Slider
                            value={[localPreferences.locations.maxDistance || 50]}
                            onValueChange={(value) => {
                              setLocalPreferences(prev => ({
                                ...prev,
                                locations: { ...prev.locations, maxDistance: value[0] }
                              }))
                            }}
                            max={200}
                            min={5}
                            step={5}
                            className="w-full"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>5km</span>
                            <span>200km</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="salary" className="space-y-6">
                    <Card className="card-premium">
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <DollarSign className="w-5 h-5" />
                          <span>Salary Range</span>
                        </CardTitle>
                        <CardDescription>Set your expected salary range</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="salary-min">Minimum Salary</Label>
                            <Input
                              id="salary-min"
                              type="number"
                              value={localPreferences.salaryRange.min}
                              onChange={(e) => {
                                setLocalPreferences(prev => ({
                                  ...prev,
                                  salaryRange: { ...prev.salaryRange, min: parseInt(e.target.value) || 0 }
                                }))
                              }}
                            />
                          </div>
                          <div>
                            <Label htmlFor="salary-max">Maximum Salary</Label>
                            <Input
                              id="salary-max"
                              type="number"
                              value={localPreferences.salaryRange.max}
                              onChange={(e) => {
                                setLocalPreferences(prev => ({
                                  ...prev,
                                  salaryRange: { ...prev.salaryRange, max: parseInt(e.target.value) || 0 }
                                }))
                              }}
                            />
                          </div>
                        </div>
                        
                        <div className="text-center p-4 bg-muted/50 rounded-lg">
                          <p className="text-sm text-muted-foreground">
                            Salary range: ${localPreferences.salaryRange.min.toLocaleString()} - ${localPreferences.salaryRange.max.toLocaleString()}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="advanced" className="space-y-6">
                    {/* Industries */}
                    <Card className="card-premium">
                      <CardHeader>
                        <CardTitle>Industries</CardTitle>
                        <CardDescription>Select industries you're interested in</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {industries.map(industry => (
                            <div key={industry} className="flex items-center space-x-2">
                              <Checkbox
                                id={`industry-${industry}`}
                                checked={localPreferences.industries.includes(industry)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      industries: [...prev.industries, industry]
                                    }))
                                  } else {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      industries: prev.industries.filter(i => i !== industry)
                                    }))
                                  }
                                }}
                              />
                              <Label htmlFor={`industry-${industry}`} className="text-sm">
                                {industry}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Company Types */}
                    <Card className="card-premium">
                      <CardHeader>
                        <CardTitle>Company Types</CardTitle>
                        <CardDescription>Choose your preferred company types</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {companyTypes.map(type => (
                            <div key={type} className="flex items-center space-x-2">
                              <Checkbox
                                id={`company-type-${type}`}
                                checked={localPreferences.companyTypes.includes(type)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      companyTypes: [...prev.companyTypes, type]
                                    }))
                                  } else {
                                    setLocalPreferences(prev => ({
                                      ...prev,
                                      companyTypes: prev.companyTypes.filter(t => t !== type)
                                    }))
                                  }
                                }}
                              />
                              <Label htmlFor={`company-type-${type}`} className="text-sm">
                                {type}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </motion.div>
              </AnimatePresence>
            </Tabs>
          </div>

          {/* Footer */}
          <div className="p-6 border-t bg-muted/30">
            <div className="flex items-center justify-between">
              <Button variant="outline" onClick={handleReset}>
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset to Defaults
              </Button>
              
              <div className="flex items-center space-x-3">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button onClick={handleSave} className="button-premium">
                  <Save className="w-4 h-4 mr-2" />
                  Save Preferences
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
