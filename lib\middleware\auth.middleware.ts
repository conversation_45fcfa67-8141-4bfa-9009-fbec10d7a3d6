// lib/middleware/auth.middleware.ts
import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { User } from '@/lib/models/user.model'
import {
  JWTPayload,
  AuthenticatedUser,
  AuthenticationResult,
  AuthMiddlewareResult,
  AuthorizationResult,
  TokenGenerationOptions,
  UserRole
} from '@/types/auth.types'

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string
    email: string
    role: string
  }
}

// JWT secret
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// Verify JWT token
export function verifyToken(token: string): JWTPayload {
  try {
    const decoded = jwt.verify(token, JWT_SECRET)
    return decoded as JWTPayload
  } catch {
    throw new Error('Invalid token')
  }
}

// Generate JWT token
export function generateToken(payload: JWTPayload, options: TokenGenerationOptions = {}): string {
  const { expiresIn = '7d', issuer, audience, subject } = options

  // Build sign options with proper typing
  const signOptions: Record<string, unknown> = {}

  if (expiresIn) {
    signOptions.expiresIn = expiresIn
  }
  if (issuer) signOptions.issuer = issuer
  if (audience) signOptions.audience = audience
  if (subject) signOptions.subject = subject

  return jwt.sign(payload, JWT_SECRET, signOptions as jwt.SignOptions)
}

// Authentication middleware
export async function authenticateUser(request: NextRequest): Promise<AuthenticationResult> {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { user: null, error: 'No token provided' }
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Verify token
    const decoded = verifyToken(token)

    // Get user from database
    const userDoc = await User.findById(decoded.userId).select('-password')
    if (!userDoc) {
      return { user: null, error: 'User not found' }
    }

    if (!userDoc.isActive) {
      return { user: null, error: 'Account is deactivated' }
    }

    // Map to AuthenticatedUser interface
    const user: AuthenticatedUser = {
      id: userDoc._id.toString(),
      email: userDoc.email,
      role: userDoc.role as UserRole,
      companyId: userDoc.companyId?.toString(),
      isActive: userDoc.isActive,
      emailVerified: userDoc.isEmailVerified,
      profile: {
        firstName: userDoc.profile?.firstName,
        lastName: userDoc.profile?.lastName,
        avatar: userDoc.profile?.avatar
      }
    }

    return { user }
  } catch {
    return { user: null, error: 'Authentication failed' }
  }
}

// Auth middleware for API routes
export async function authMiddleware(request: NextRequest): Promise<AuthMiddlewareResult> {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: 'No token provided',
        status: 401
      }
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Verify token
    const decoded = verifyToken(token)

    // Get user from database
    const user = await User.findById(decoded.userId).select('-password')
    if (!user) {
      return {
        success: false,
        error: 'User not found',
        status: 401
      }
    }

    if (!user.isActive) {
      return {
        success: false,
        error: 'Account is deactivated',
        status: 401
      }
    }

    // Map to AuthenticatedUser interface
    const authenticatedUser: AuthenticatedUser = {
      id: user._id.toString(),
      email: user.email,
      role: user.role as UserRole,
      companyId: user.companyId?.toString(),
      isActive: user.isActive,
      emailVerified: user.isEmailVerified,
      profile: {
        firstName: user.profile?.firstName,
        lastName: user.profile?.lastName,
        avatar: user.profile?.avatar
      }
    }

    return {
      success: true,
      user: authenticatedUser
    }
  } catch {
    return {
      success: false,
      error: 'Authentication failed',
      status: 401
    }
  }
}

// Role-based authorization middleware
export function requireRole(allowedRoles: UserRole[]) {
  return async (_request: NextRequest, user: AuthenticatedUser | null): Promise<AuthorizationResult> => {
    if (!user) {
      return { authorized: false, error: 'Authentication required' }
    }

    if (!allowedRoles.includes(user.role)) {
      return { authorized: false, error: 'Insufficient permissions' }
    }

    return { authorized: true }
  }
}

// Middleware wrapper for API routes (with optional authentication)
export function withAuth<T extends { user?: AuthenticatedUser }>(
  handler: (request: NextRequest, context: T) => Promise<NextResponse>,
  options: {
    requiredRoles?: UserRole[]
    optional?: boolean
  } = {}
) {
  return async (request: NextRequest, context: Record<string, unknown>) => {
    try {
      const { user, error } = await authenticateUser(request)

      // If authentication is optional and no user, continue without user
      if (options.optional && !user && error) {
        return handler(request, { ...context, user: undefined } as T)
      }

      // If authentication failed and not optional, return error
      if (!user && error) {
        return NextResponse.json(
          { error: error || 'Authentication required' },
          { status: 401 }
        )
      }

      // At this point, user should exist if we reach here
      if (!user) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }

      // Check role requirements
      if (options.requiredRoles && options.requiredRoles.length > 0) {
        const roleCheck = await requireRole(options.requiredRoles)(request, user)
        if (!roleCheck.authorized) {
          return NextResponse.json(
            { error: roleCheck.error },
            { status: 403 }
          )
        }
      }

      // Add user to context and call handler
      return handler(request, { ...context, user } as T)
    } catch (error) {
      console.error('Auth middleware error:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Extract user from request (for use in API routes)
export async function getUserFromRequest(request: NextRequest): Promise<AuthenticatedUser | null> {
  const { user } = await authenticateUser(request)
  return user
}

// Check if user owns resource
export function checkResourceOwnership(userId: string, resourceUserId: string): boolean {
  return userId === resourceUserId
}

// Admin check
export function isAdmin(user: AuthenticatedUser | null): boolean {
  return user?.role === 'admin'
}

// Super admin check (for future use)
export function isSuperAdmin(user: AuthenticatedUser | null): boolean {
  return user?.role === 'admin' && user?.adminRole === 'super_admin'
}

// Company admin check
export function isCompanyAdmin(user: AuthenticatedUser | null): boolean {
  return user?.role === 'company_admin' || user?.role === 'admin'
}

// Recruiter check
export function isRecruiter(user: AuthenticatedUser | null): boolean {
  return ['recruiter', 'company_admin', 'admin'].includes(user?.role || '')
}

// Job seeker check
export function isJobSeeker(user: AuthenticatedUser | null): boolean {
  return user?.role === 'job_seeker'
}

// Admin permissions check
export function hasAdminPermission(user: AuthenticatedUser | null, permission: string): boolean {
  if (!isAdmin(user)) return false

  // Super admin has all permissions
  if (isSuperAdmin(user)) return true

  // Check specific permissions (for future granular permissions)
  const adminPermissions = user?.permissions || []
  return adminPermissions.includes(permission) || adminPermissions.includes('*')
}

// Admin middleware wrapper
export function withAdminAuth(
  handler: (request: NextRequest, context: { user: AuthenticatedUser }) => Promise<NextResponse>,
  options: {
    requiredPermissions?: string[]
    allowSuperAdminOnly?: boolean
  } = {}
) {
  return withAuth(handler, {
    requiredRoles: ['admin'],
    ...options
  })
}
