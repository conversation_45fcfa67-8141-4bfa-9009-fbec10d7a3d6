'use client'

import { Button, ButtonProps } from '@/components/ui/button'
import { LoadingSpinner } from './loading-spinner'
import { cn } from '@/lib/utils'

interface ButtonLoadingProps extends ButtonProps {
  loading?: boolean
  loadingText?: string
  spinnerSize?: 'sm' | 'md' | 'lg' | 'xl'
}

export function ButtonLoading({
  loading = false,
  loadingText,
  children,
  disabled,
  className,
  spinnerSize = 'sm',
  ...props
}: ButtonLoadingProps) {
  return (
    <Button
      disabled={disabled || loading}
      className={cn(
        'relative',
        loading && 'cursor-not-allowed',
        className
      )}
      {...props}
    >
      {loading && (
        <LoadingSpinner 
          size={spinnerSize} 
          className="mr-2" 
          color="secondary"
        />
      )}
      {loading ? (loadingText || 'Loading...') : children}
    </Button>
  )
}

// Alternative with overlay loading state
export function ButtonLoadingOverlay({
  loading = false,
  loadingText,
  children,
  disabled,
  className,
  spinnerSize = 'sm',
  ...props
}: ButtonLoadingProps) {
  return (
    <Button
      disabled={disabled || loading}
      className={cn(
        'relative overflow-hidden',
        className
      )}
      {...props}
    >
      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 bg-primary/90 flex items-center justify-center">
          <LoadingSpinner 
            size={spinnerSize} 
            color="secondary"
          />
        </div>
      )}
      
      {/* Button content */}
      <span className={cn(loading && 'opacity-50')}>
        {children}
      </span>
    </Button>
  )
}

// Submit button with loading state
export function SubmitButton({
  loading = false,
  children = 'Submit',
  loadingText = 'Submitting...',
  ...props
}: ButtonLoadingProps) {
  return (
    <ButtonLoading
      type="submit"
      loading={loading}
      loadingText={loadingText}
      {...props}
    >
      {children}
    </ButtonLoading>
  )
}

// Save button with loading state
export function SaveButton({
  loading = false,
  children = 'Save',
  loadingText = 'Saving...',
  ...props
}: ButtonLoadingProps) {
  return (
    <ButtonLoading
      loading={loading}
      loadingText={loadingText}
      {...props}
    >
      {children}
    </ButtonLoading>
  )
}
