// API Types for Job Marketplace Portal

import { NextRequest } from 'next/server'

// ============================================================================
// COMMON TYPES
// ============================================================================

export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: ApiError
  message?: string
  timestamp?: string
}

export interface ApiError {
  code: string
  message: string
  field?: string
  details?: Record<string, unknown>
  statusCode?: number
}

export interface PaginationParams {
  page: number
  limit: number
  skip: number
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface RouteParams {
  params: {
    id: string
    [key: string]: string
  }
}

// ============================================================================
// USER TYPES
// ============================================================================

export interface UserProfile {
  firstName: string
  lastName: string
  fullName?: string
  phone?: string
  location?: {
    city: string
    state?: string
    country: string
    coordinates?: [number, number]
  }
  bio?: string
  avatar?: string
  website?: string
  linkedin?: string
  github?: string
  resume?: string
  skills?: string[]
  experience?: number
  education?: Array<{
    institution: string
    degree: string
    field: string
    startDate: Date
    endDate?: Date
    current: boolean
  }>
  certifications?: Array<{
    name: string
    issuer: string
    date: Date
    expiryDate?: Date
    credentialId?: string
  }>
}

export interface UserPreferences {
  emailNotifications: boolean
  jobAlerts: boolean
  marketingEmails: boolean
  theme: 'light' | 'dark' | 'system'
  language: string
  timezone: string
  jobTypes?: string[]
  salaryRange?: {
    min: number
    max: number
    currency: string
  }
  locations?: string[]
  remoteWork: boolean
}

export interface AuthUser {
  id: string
  email: string
  role: 'job_seeker' | 'company_admin' | 'admin' | 'super_admin'
  profile: UserProfile
  preferences: UserPreferences
  companyId?: string
  isEmailVerified: boolean
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
}

// ============================================================================
// SEARCH QUERY TYPES
// ============================================================================

export interface CompanySearchQuery {
  isActive?: boolean
  $or?: Array<{
    name?: { $regex: string; $options: string }
    description?: { $regex: string; $options: string }
    industry?: { $regex: string; $options: string }
    specialties?: { $in: RegExp[] }
  }>
  industry?: string | { $in: string[] }
  size?: string
  'locations.city'?: { $regex: string; $options: string }
  'locations.state'?: { $regex: string; $options: string }
  'locations.country'?: { $regex: string; $options: string }
  isFeatured?: boolean
  'verification.isVerified'?: boolean
}

export interface JobSearchQuery {
  isActive?: boolean
  status?: string | { $in: string[] }
  $or?: Array<{
    title?: { $regex: string; $options: string }
    description?: { $regex: string; $options: string }
    requirements?: { $regex: string; $options: string }
    skills?: { $in: RegExp[] }
  }>
  company?: string
  'location.city'?: { $regex: string; $options: string }
  'location.state'?: { $regex: string; $options: string }
  'location.country'?: { $regex: string; $options: string }
  type?: string | { $in: string[] }
  'salary.min'?: { $gte?: number; $lte?: number }
  'salary.max'?: { $gte?: number; $lte?: number }
  remote?: boolean
  department?: string
  experienceLevel?: string
  createdAt?: { $gte?: Date; $lte?: Date }
}

export interface ApplicationSearchQuery {
  client?: string
  company?: string
  job?: string
  status?: string | { $in: string[] }
  createdAt?: { $gte?: Date; $lte?: Date }
  updatedAt?: { $gte?: Date; $lte?: Date }
}

// ============================================================================
// REQUEST/RESPONSE TYPES
// ============================================================================

export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role: 'job_seeker' | 'company_admin'
  phone?: string
  location?: {
    city: string
    state?: string
    country: string
  }
}

export interface AuthResponse {
  user: AuthUser
  token: string
  refreshToken?: string
  expiresIn: number
}

// ============================================================================
// ERROR HANDLING
// ============================================================================

export interface DatabaseError extends Error {
  code?: string
  statusCode?: number
}

export interface ValidationError extends Error {
  field: string
  value: unknown
  statusCode: 400
}

export interface AuthenticationError extends Error {
  statusCode: 401
}

export interface AuthorizationError extends Error {
  statusCode: 403
  requiredRole?: string
}

export interface NotFoundError extends Error {
  statusCode: 404
  resource?: string
}

// ============================================================================
// MIDDLEWARE TYPES
// ============================================================================

export interface AuthenticatedRequest extends NextRequest {
  user?: AuthUser
}

export interface AuthMiddlewareResult {
  success: boolean
  user?: {
    id: string
    email: string
    role: string
  }
  error?: string
  status?: number
}

// ============================================================================
// DASHBOARD TYPES
// ============================================================================

export interface DashboardStats {
  activeJobs: number
  totalApplications: number
  pendingReviews: number
  interviewsScheduled: number
  hiredCandidates: number
  profileViews: number
  responseRate: number
  averageTimeToHire: number
  jobFillRate: number
  applicationQuality: number
  activeRecruiters: number
  jobsGrowth: number
  applicationsGrowth: number
  profileViewsGrowth: number
  hiredGrowth: number
}

export interface ActivityItem {
  id: string
  type: 'application_received' | 'interview_scheduled' | 'candidate_hired' | 'job_posted' | 'profile_viewed'
  title: string
  description: string
  timestamp: Date
  status: 'pending' | 'completed' | 'cancelled'
  jobId?: string
  applicationId?: string
  candidateId?: string
}

// ============================================================================
// FILTER TYPES
// ============================================================================

export interface CompanyFilters {
  industry?: string[]
  size?: string[]
  location?: string
  isVerified?: boolean
  isFeatured?: boolean
}

export interface JobFilters {
  type?: string[]
  location?: string
  remote?: boolean
  salaryMin?: number
  salaryMax?: number
  experienceLevel?: string[]
  department?: string[]
  company?: string
}

export interface ApplicationFilters {
  status?: string[]
  dateFrom?: Date
  dateTo?: Date
  job?: string
  company?: string
}
