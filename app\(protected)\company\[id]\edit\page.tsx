'use client'

import React, { useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useCompaniesStore, useAuthStore } from '@/stores'
import { CompanyProfileForm } from '@/components/company/company-profile-form'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { Button } from '@/components/ui/button'

function CompanyEditContent() {
  const params = useParams()
  const router = useRouter()
  const companyId = params.id as string
  
  const { user } = useAuthStore()
  const { 
    currentCompany,
    companyLoading,
    error,
    getCompanyById,
    clearError
  } = useCompaniesStore()

  // Load company details
  useEffect(() => {
    if (companyId) {
      getCompanyById(companyId)
    }
  }, [companyId, getCompanyById])

  // Check if user owns this company
  const canEdit = user && currentCompany && (
    user.role === 'admin' || 
    (user.companyId && user.companyId === currentCompany._id)
  )

  // Handle successful update
  const handleSuccess = (company: any) => {
    router.push(`/company/${company._id}`)
  }

  // Handle cancel
  const handleCancel = () => {
    router.push(`/company/${companyId}`)
  }

  // Loading state
  if (companyLoading) {
    return <PageLoader message="Loading company details..." fullScreen />
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorAlert
            type="error"
            title="Failed to Load Company"
            message={error.message || 'Could not load company details. Please try again.'}
            dismissible
            onDismiss={clearError}
            actions={
              <div className="flex space-x-2 mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/company/${companyId}`)}
                >
                  View Company
                </Button>
                <Button
                  size="sm"
                  onClick={() => getCompanyById(companyId)}
                >
                  Try Again
                </Button>
              </div>
            }
          />
        </div>
      </div>
    )
  }

  // Company not found
  if (!currentCompany) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Company Not Found</h1>
          <p className="text-muted-foreground mb-4">
            The company you're trying to edit doesn't exist or has been removed.
          </p>
          <Button onClick={() => router.push('/companies')}>
            Browse All Companies
          </Button>
        </div>
      </div>
    )
  }

  // Permission check
  if (!canEdit) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don't have permission to edit this company profile.
          </p>
          <Button onClick={() => router.push(`/company/${companyId}`)}>
            View Company Profile
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Edit Company Profile</h1>
              <p className="text-muted-foreground">
                Update your company information to attract top talent
              </p>
            </div>
            
            <Button
              variant="outline"
              onClick={() => router.push(`/company/${companyId}`)}
            >
              Cancel
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <CompanyProfileForm
          company={currentCompany}
          mode="edit"
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </main>
    </div>
  )
}

export default function CompanyEditPage() {
  return (
    <ProtectedRoute requiredRole="company_admin">
      <CompanyEditContent />
    </ProtectedRoute>
  )
}
