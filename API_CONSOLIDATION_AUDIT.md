# API Routes Consolidation Audit

## Current State Analysis

### Duplicate Routes Found

#### Authentication Routes
- ❌ `app/api/auth/login/route.ts` (OLD)
- ✅ `app/api/v1/auth/login/route.ts` (KEEP)
- ❌ `app/api/auth/register/route.ts` (OLD)
- ✅ `app/api/v1/auth/register/route.ts` (KEEP)
- ❌ `app/api/auth/me/route.ts` (OLD)
- ❌ `app/api/auth/refresh/route.ts` (OLD)

#### Companies Routes
- ❌ `app/api/companies/route.ts` (OLD)
- ✅ `app/api/v1/companies/route.ts` (KEEP)
- ❌ `app/api/companies/[id]/route.ts` (OLD)
- ✅ `app/api/v1/companies/[id]/route.ts` (KEEP)

#### Jobs Routes
- ❌ `app/api/jobs/route.ts` (OLD)
- ✅ `app/api/v1/jobs/route.ts` (KEEP)
- ❌ `app/api/jobs/[id]/route.ts` (OLD)
- ✅ `app/api/v1/jobs/[id]/route.ts` (KEEP)

#### Applications Routes
- ❌ `app/api/applications/route.ts` (OLD)
- ✅ `app/api/v1/applications/route.ts` (KEEP)

#### Admin Routes (Need Migration)
- ❌ `app/api/admin/companies/route.ts` (MIGRATE TO V1)
- ❌ `app/api/admin/dashboard/route.ts` (MIGRATE TO V1)
- ❌ `app/api/admin/users/route.ts` (MIGRATE TO V1)

#### Messages Routes (Need Migration)
- ❌ `app/api/messages/conversations/route.ts` (MIGRATE TO V1)

#### Test Routes (Keep Separate)
- ✅ `app/api/test/` (KEEP - Development only)

### Missing V1 Routes (Need Creation)

#### Authentication
- `app/api/v1/auth/me/route.ts`
- `app/api/v1/auth/refresh/route.ts`
- `app/api/v1/auth/logout/route.ts`
- `app/api/v1/auth/forgot-password/route.ts`
- `app/api/v1/auth/reset-password/route.ts`

#### Admin
- `app/api/v1/admin/dashboard/route.ts`
- `app/api/v1/admin/companies/route.ts`
- `app/api/v1/admin/users/route.ts`
- `app/api/v1/admin/jobs/route.ts`

#### Messages
- `app/api/v1/messages/conversations/route.ts`
- `app/api/v1/messages/[id]/route.ts`

## TypeScript Issues Found

### Common 'any' Type Usage
1. **Search Query Objects**: `const searchQuery: any = {}`
2. **Error Handling**: `catch (error: any)`
3. **Request Parameters**: `params: any`
4. **Database Results**: `const result: any`
5. **API Response Types**: `ApiResponse<T = any>`

### Required Type Definitions

#### Search Query Types
```typescript
interface CompanySearchQuery {
  isActive?: boolean
  $or?: Array<Record<string, any>>
  industry?: string
  size?: string
  'location.city'?: { $regex: string; $options: string }
  'location.state'?: { $regex: string; $options: string }
}

interface JobSearchQuery {
  isActive?: boolean
  status?: string
  $or?: Array<Record<string, any>>
  company?: string
  location?: string
  type?: string
  salaryRange?: { $gte?: number; $lte?: number }
}
```

#### Route Parameter Types
```typescript
interface RouteParams {
  params: {
    id: string
    [key: string]: string
  }
}

interface PaginationParams {
  page: number
  limit: number
  skip: number
}
```

#### Error Types
```typescript
interface ApiError extends Error {
  statusCode?: number
  code?: string
  field?: string
}
```

## Migration Plan

### Phase 1: Create Missing V1 Routes
1. Create missing auth routes in v1
2. Migrate admin routes to v1
3. Migrate messages routes to v1

### Phase 2: Fix TypeScript Issues
1. Create comprehensive type definitions
2. Replace all 'any' types with proper interfaces
3. Add proper error handling types

### Phase 3: Update Frontend
1. Update all API calls to use v1 endpoints
2. Update stores and services
3. Update error handling

### Phase 4: Remove Old Routes
1. Remove duplicate routes from app/api/
2. Keep only test routes outside v1
3. Update documentation

### Phase 5: Testing
1. Test all migrated endpoints
2. Verify frontend integration
3. Update API documentation

## Priority Order

1. **HIGH**: Fix authentication routes (critical for app functionality)
2. **HIGH**: Fix companies and jobs routes (core business logic)
3. **MEDIUM**: Migrate admin routes
4. **MEDIUM**: Fix TypeScript types
5. **LOW**: Migrate messages routes
6. **LOW**: Clean up old routes

## Breaking Changes

### Frontend Updates Required
- All API calls must be updated to use `/api/v1/` prefix
- Error handling may need updates due to type changes
- Some response formats may change slightly

### Database Queries
- Search query types will be more strict
- Pagination parameters will be validated
- Error responses will be more consistent

## Next Steps

1. Start with authentication route migration
2. Create comprehensive type definitions file
3. Update one route at a time to avoid breaking changes
4. Test each migration thoroughly
5. Update frontend incrementally
