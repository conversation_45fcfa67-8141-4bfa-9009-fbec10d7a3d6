"use client"

import React, { useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarTrigger,
  useSidebar
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { 
  LayoutDashboard,
  User,
  Briefcase,
  FileText,
  Heart,
  Building2,
  BarChart3,
  Settings,
  Bell,
  Search,
  Calendar,
  MessageSquare,
  Award,
  BookOpen,
  Target,
  TrendingUp,
  ChevronRight,
  ChevronDown,
  Home,
  LogOut
} from "lucide-react"
import { cn } from "@/lib/utils"

interface SidebarItem {
  title: string
  icon: React.ComponentType<{ className?: string }>
  href?: string
  badge?: string | number
  items?: SidebarItem[]
  isActive?: boolean
}

export function ClientDashboardSidebar() {
  const router = useRouter()
  const pathname = usePathname()
  const { state } = useSidebar()
  const [openSections, setOpenSections] = useState<string[]>(['dashboard', 'jobs'])

  const toggleSection = (section: string) => {
    setOpenSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  const isActive = (href: string) => pathname === href || pathname.startsWith(href + '/')

  const sidebarItems: SidebarItem[] = [
    {
      title: "Dashboard",
      icon: LayoutDashboard,
      href: "/client-dashboard"
    },
    {
      title: "Profile",
      icon: User,
      items: [
        { title: "View Profile", icon: User, href: "/client-dashboard/profile" },
        { title: "Edit Profile", icon: Settings, href: "/client-dashboard/profile/edit" },
        { title: "Resume & Documents", icon: FileText, href: "/client-dashboard/profile/documents" },
        { title: "Skills & Experience", icon: Award, href: "/client-dashboard/profile/skills" },
        { title: "Portfolio", icon: BookOpen, href: "/client-dashboard/profile/portfolio" }
      ]
    },
    {
      title: "Job Search",
      icon: Search,
      items: [
        { title: "Browse Jobs", icon: Briefcase, href: "/client-dashboard/jobs" },
        { title: "Job Recommendations", icon: Target, href: "/client-dashboard/jobs/recommendations" },
        { title: "Saved Jobs", icon: Heart, href: "/client-dashboard/jobs/saved", badge: "3" },
        { title: "Job Alerts", icon: Bell, href: "/client-dashboard/jobs/alerts" },
        { title: "Search History", icon: Search, href: "/client-dashboard/jobs/history" }
      ]
    },
    {
      title: "Applications",
      icon: FileText,
      items: [
        { title: "All Applications", icon: FileText, href: "/client-dashboard/applications", badge: "12" },
        { title: "In Progress", icon: TrendingUp, href: "/client-dashboard/applications/in-progress", badge: "5" },
        { title: "Interviews", icon: Calendar, href: "/client-dashboard/applications/interviews", badge: "2" },
        { title: "Offers", icon: Award, href: "/client-dashboard/applications/offers", badge: "1" },
        { title: "Application History", icon: BarChart3, href: "/client-dashboard/applications/history" }
      ]
    },
    {
      title: "Companies",
      icon: Building2,
      items: [
        { title: "Explore Companies", icon: Building2, href: "/client-dashboard/companies" },
        { title: "Following", icon: Heart, href: "/client-dashboard/companies/following", badge: "8" },
        { title: "Company Reviews", icon: MessageSquare, href: "/client-dashboard/companies/reviews" }
      ]
    },
    {
      title: "Career Tools",
      icon: TrendingUp,
      items: [
        { title: "Career Analytics", icon: BarChart3, href: "/client-dashboard/analytics" },
        { title: "Skill Assessment", icon: Award, href: "/client-dashboard/skills/assessment" },
        { title: "Salary Insights", icon: TrendingUp, href: "/client-dashboard/salary" },
        { title: "Career Path", icon: Target, href: "/client-dashboard/career-path" },
        { title: "Learning Resources", icon: BookOpen, href: "/client-dashboard/learning" }
      ]
    },
    {
      title: "Messages",
      icon: MessageSquare,
      href: "/client-dashboard/messages",
      badge: "4"
    },
    {
      title: "Settings",
      icon: Settings,
      items: [
        { title: "Account Settings", icon: Settings, href: "/client-dashboard/settings/account" },
        { title: "Privacy Settings", icon: User, href: "/client-dashboard/settings/privacy" },
        { title: "Notification Settings", icon: Bell, href: "/client-dashboard/settings/notifications" },
        { title: "Job Preferences", icon: Target, href: "/client-dashboard/settings/preferences" }
      ]
    }
  ]

  const renderSidebarItem = (item: SidebarItem, level: number = 0) => {
    const hasSubItems = item.items && item.items.length > 0
    const sectionKey = item.title.toLowerCase().replace(/\s+/g, '-')
    const isOpen = openSections.includes(sectionKey)
    const itemIsActive = item.href ? isActive(item.href) : false

    if (hasSubItems) {
      return (
        <Collapsible key={item.title} open={isOpen} onOpenChange={() => toggleSection(sectionKey)}>
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton
                className={cn(
                  "w-full justify-between hover:bg-primary/10 transition-all duration-200 rounded-lg",
                  level > 0 && "ml-4"
                )}
              >
                <div className="flex items-center">
                  <item.icon className="w-4 h-4 mr-3 text-primary/70" />
                  <span className="font-medium">{item.title}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {item.badge && (
                    <Badge variant="secondary" className="text-xs bg-primary/20 text-primary border-primary/30">
                      {item.badge}
                    </Badge>
                  )}
                  {isOpen ? (
                    <ChevronDown className="w-4 h-4 text-primary/60" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-primary/60" />
                  )}
                </div>
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.items?.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.title}>
                    <SidebarMenuSubButton
                      asChild
                      isActive={subItem.href ? isActive(subItem.href) : false}
                      className="hover:bg-primary/5 transition-all duration-200 rounded-md"
                    >
                      <a href={subItem.href} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <subItem.icon className="w-4 h-4 mr-3 text-primary/60" />
                          <span className="text-sm">{subItem.title}</span>
                        </div>
                        {subItem.badge && (
                          <Badge variant="secondary" className="text-xs bg-primary/15 text-primary border-primary/20">
                            {subItem.badge}
                          </Badge>
                        )}
                      </a>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      )
    }

    return (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton
          asChild
          isActive={itemIsActive}
          className={cn(
            "hover:bg-primary/10 transition-all duration-200 rounded-lg",
            level > 0 && "ml-4",
            itemIsActive && "bg-primary/15 border-r-2 border-primary"
          )}
        >
          <a href={item.href} className="flex items-center justify-between">
            <div className="flex items-center">
              <item.icon className={cn(
                "w-4 h-4 mr-3",
                itemIsActive ? "text-primary" : "text-primary/70"
              )} />
              <span className={cn(
                "font-medium",
                itemIsActive && "text-primary font-semibold"
              )}>{item.title}</span>
            </div>
            {item.badge && (
              <Badge variant="secondary" className={cn(
                "text-xs",
                itemIsActive
                  ? "bg-primary text-primary-foreground"
                  : "bg-primary/20 text-primary border-primary/30"
              )}>
                {item.badge}
              </Badge>
            )}
          </a>
        </SidebarMenuButton>
      </SidebarMenuItem>
    )
  }

  return (
    <Sidebar variant="sidebar" collapsible="icon" className="border-r bg-card/50 backdrop-blur-sm">
      <SidebarHeader className="border-b border-border/50 bg-gradient-to-r from-primary/5 to-primary/10">
        <div className="flex items-center space-x-3 px-4 py-4">
          <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-lg">
            <User className="w-5 h-5 text-primary-foreground" />
          </div>
          {state === "expanded" && (
            <div className="flex-1">
              <h2 className="text-lg font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                Career Hub
              </h2>
              <p className="text-xs text-muted-foreground font-medium">
                Professional Dashboard
              </p>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent className="bg-gradient-to-b from-background/50 to-background">
        <ScrollArea className="flex-1">
          <SidebarMenu className="px-3 py-6 space-y-2">
            {/* Quick Actions */}
            {state === "expanded" && (
              <>
                <div className="px-3 py-2">
                  <h3 className="text-xs font-bold text-muted-foreground uppercase tracking-wider">
                    Quick Actions
                  </h3>
                </div>
                <SidebarMenuItem>
                  <Button
                    variant="default"
                    size="sm"
                    className="w-full justify-start mb-3 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-md"
                    onClick={() => router.push('/client-dashboard/jobs')}
                  >
                    <Search className="w-4 h-4 mr-2" />
                    Find Jobs
                  </Button>
                </SidebarMenuItem>
                <Separator className="my-4 bg-border/50" />
              </>
            )}

            {/* Main Navigation */}
            {sidebarItems.map((item) => renderSidebarItem(item))}
          </SidebarMenu>
        </ScrollArea>
      </SidebarContent>

      <SidebarFooter className="border-t border-border/50 bg-gradient-to-r from-primary/5 to-primary/10 p-4">
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="hover:bg-primary/10 transition-all duration-200 rounded-lg"
            >
              <a href="/" className="flex items-center">
                <Home className="w-4 h-4 mr-3 text-primary/70" />
                <span className="font-medium">Back to Home</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
          {state === "expanded" && (
            <div className="mt-3 px-3">
              <div className="text-xs text-muted-foreground">
                <p className="font-medium">Need help?</p>
                <p className="text-primary/60 hover:text-primary cursor-pointer">
                  Contact Support
                </p>
              </div>
            </div>
          )}
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}
