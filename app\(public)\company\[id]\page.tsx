'use client'

import React, { useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { useCompaniesStore, useAuthStore } from '@/stores'
import { CompanyProfilePage } from '@/components/company/company-profile-page'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { Button } from '@/components/ui/button'

export default function CompanyPage() {
  const params = useParams()
  const router = useRouter()
  const companyId = params.id as string
  
  const { user } = useAuthStore()
  const { 
    currentCompany,
    companyLoading,
    error,
    getCompanyById,
    clearError
  } = useCompaniesStore()

  // Load company details
  useEffect(() => {
    if (companyId) {
      getCompanyById(companyId)
    }
  }, [companyId, getCompanyById])

  // Check if user owns this company
  const isOwner = user && currentCompany && (
    user.role === 'admin' || 
    (user.companyId && user.companyId === currentCompany._id)
  )

  // Loading state
  if (companyLoading) {
    return <PageLoader message="Loading company profile..." fullScreen />
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorAlert
            type="error"
            title="Failed to Load Company"
            message={error.message || 'Could not load company profile. Please try again.'}
            dismissible
            onDismiss={clearError}
            actions={
              <div className="flex space-x-2 mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/companies')}
                >
                  Browse Companies
                </Button>
                <Button
                  size="sm"
                  onClick={() => getCompanyById(companyId)}
                >
                  Try Again
                </Button>
              </div>
            }
          />
        </div>
      </div>
    )
  }

  // Company not found
  if (!currentCompany) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Company Not Found</h1>
          <p className="text-muted-foreground mb-4">
            The company you're looking for doesn't exist or has been removed.
          </p>
          <Button onClick={() => router.push('/companies')}>
            Browse All Companies
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="pt-16">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-16 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="flex items-center space-x-2"
            >
              <span>← Back</span>
            </Button>

            {isOwner && (
              <Button
                onClick={() => router.push(`/company/${companyId}/edit`)}
                variant="outline"
              >
                Edit Profile
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <CompanyProfilePage
          company={currentCompany}
          isOwner={isOwner}
        />
      </main>
    </div>
  )
}
