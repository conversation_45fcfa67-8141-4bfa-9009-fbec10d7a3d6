'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useCompaniesStore, useAuthStore, type Company } from '@/stores'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ButtonLoading } from '@/components/ui/button-loading'
import { ErrorAlert, InlineError } from '@/components/ui/error-alert'
import { Badge } from '@/components/ui/badge'
import { 
  Upload, 
  Building, 
  Globe, 
  MapPin,
  Users,
  X,
  CheckCircle,
  Camera
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface CompanyProfileFormProps {
  company?: Company
  onSuccess?: (company: Company) => void
  onCancel?: () => void
  mode?: 'create' | 'edit'
}

interface CompanyFormData {
  name: string
  description: string
  website: string
  industry: string
  size: string
  location: {
    city: string
    state: string
    country: string
  }
  logo: string
  coverImage: string
  founded: string
  specialties: string[]
  benefits: string[]
  culture: string
  mission: string
}

interface FormErrors {
  name?: string
  description?: string
  website?: string
  industry?: string
  size?: string
  location?: string
  general?: string
}

const INDUSTRIES = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Manufacturing',
  'Retail',
  'Consulting',
  'Media & Entertainment',
  'Real Estate',
  'Transportation',
  'Energy',
  'Government',
  'Non-profit',
  'Other'
]

const COMPANY_SIZES = [
  '1-10 employees',
  '11-50 employees',
  '51-200 employees',
  '201-500 employees',
  '501-1000 employees',
  '1001-5000 employees',
  '5000+ employees'
]

export function CompanyProfileForm({ 
  company, 
  onSuccess, 
  onCancel, 
  mode = 'create' 
}: CompanyProfileFormProps) {
  const router = useRouter()
  const { user } = useAuthStore()
  const { createCompany, updateCompany, createLoading, updateLoading, error, clearError } = useCompaniesStore()
  
  const [formData, setFormData] = useState<CompanyFormData>({
    name: company?.name || '',
    description: company?.description || '',
    website: company?.website || '',
    industry: company?.industry || '',
    size: company?.size || '',
    location: {
      city: company?.location?.city || '',
      state: company?.location?.state || '',
      country: company?.location?.country || 'United States'
    },
    logo: company?.logo || '',
    coverImage: company?.coverImage || '',
    founded: company?.founded ? new Date(company.founded).getFullYear().toString() : '',
    specialties: company?.specialties || [],
    benefits: company?.benefits || [],
    culture: company?.culture || '',
    mission: company?.mission || ''
  })
  
  const [errors, setErrors] = useState<FormErrors>({})
  const [newSpecialty, setNewSpecialty] = useState('')
  const [newBenefit, setNewBenefit] = useState('')

  const isLoading = createLoading || updateLoading

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Company name is required'
    } else if (formData.name.length < 2) {
      newErrors.name = 'Company name must be at least 2 characters'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Company description is required'
    } else if (formData.description.length < 50) {
      newErrors.description = 'Description must be at least 50 characters'
    }

    if (formData.website && !isValidUrl(formData.website)) {
      newErrors.website = 'Please enter a valid website URL'
    }

    if (!formData.industry) {
      newErrors.industry = 'Industry is required'
    }

    if (!formData.size) {
      newErrors.size = 'Company size is required'
    }

    if (!formData.location.city.trim() || !formData.location.state.trim()) {
      newErrors.location = 'City and state are required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // URL validation helper
  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`)
      return true
    } catch {
      return false
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()

    if (!validateForm()) {
      return
    }

    try {
      const companyData = {
        ...formData,
        website: formData.website.startsWith('http') ? formData.website : `https://${formData.website}`,
        founded: formData.founded ? new Date(`${formData.founded}-01-01`) : undefined
      }

      let result: Company
      if (mode === 'edit' && company) {
        result = await updateCompany(company._id, companyData)
      } else {
        result = await createCompany(companyData)
      }

      if (onSuccess) {
        onSuccess(result)
      } else {
        router.push(`/company/${result._id}`)
      }
    } catch (error) {
      console.error('Company form error:', error)
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof CompanyFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }))
    
    // Clear field error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Handle location changes
  const handleLocationChange = (field: keyof CompanyFormData['location']) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      location: { ...prev.location, [field]: e.target.value }
    }))
    
    if (errors.location) {
      setErrors(prev => ({ ...prev, location: undefined }))
    }
  }

  // Handle specialty addition
  const addSpecialty = () => {
    if (newSpecialty.trim() && !formData.specialties.includes(newSpecialty.trim())) {
      setFormData(prev => ({
        ...prev,
        specialties: [...prev.specialties, newSpecialty.trim()]
      }))
      setNewSpecialty('')
    }
  }

  // Handle specialty removal
  const removeSpecialty = (specialty: string) => {
    setFormData(prev => ({
      ...prev,
      specialties: prev.specialties.filter(s => s !== specialty)
    }))
  }

  // Handle benefit addition
  const addBenefit = () => {
    if (newBenefit.trim() && !formData.benefits.includes(newBenefit.trim())) {
      setFormData(prev => ({
        ...prev,
        benefits: [...prev.benefits, newBenefit.trim()]
      }))
      setNewBenefit('')
    }
  }

  // Handle benefit removal
  const removeBenefit = (benefit: string) => {
    setFormData(prev => ({
      ...prev,
      benefits: prev.benefits.filter(b => b !== benefit)
    }))
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">
            {mode === 'edit' ? 'Edit Company Profile' : 'Create Company Profile'}
          </CardTitle>
          <CardDescription>
            {mode === 'edit' 
              ? 'Update your company information to attract top talent'
              : 'Set up your company profile to start posting jobs and attracting candidates'
            }
          </CardDescription>
        </CardHeader>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* General Error */}
        {error && (
          <ErrorAlert
            type="error"
            message={error.message || 'Failed to save company profile. Please try again.'}
            dismissible
            onDismiss={clearError}
          />
        )}

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building className="w-5 h-5" />
              <span>Basic Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="name">Company Name *</Label>
                <Input
                  id="name"
                  placeholder="Enter company name"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  className={cn(errors.name && 'border-red-500')}
                  disabled={isLoading}
                />
                <InlineError message={errors.name} />
              </div>

              <div>
                <Label htmlFor="industry">Industry *</Label>
                <Select
                  value={formData.industry}
                  onValueChange={(value) => {
                    setFormData(prev => ({ ...prev, industry: value }))
                    if (errors.industry) {
                      setErrors(prev => ({ ...prev, industry: undefined }))
                    }
                  }}
                  disabled={isLoading}
                >
                  <SelectTrigger className={cn(errors.industry && 'border-red-500')}>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    {INDUSTRIES.map((industry) => (
                      <SelectItem key={industry} value={industry}>
                        {industry}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InlineError message={errors.industry} />
              </div>

              <div>
                <Label htmlFor="size">Company Size *</Label>
                <Select
                  value={formData.size}
                  onValueChange={(value) => {
                    setFormData(prev => ({ ...prev, size: value }))
                    if (errors.size) {
                      setErrors(prev => ({ ...prev, size: undefined }))
                    }
                  }}
                  disabled={isLoading}
                >
                  <SelectTrigger className={cn(errors.size && 'border-red-500')}>
                    <SelectValue placeholder="Select company size" />
                  </SelectTrigger>
                  <SelectContent>
                    {COMPANY_SIZES.map((size) => (
                      <SelectItem key={size} value={size}>
                        {size}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <InlineError message={errors.size} />
              </div>

              <div>
                <Label htmlFor="website">Website</Label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="website"
                    placeholder="company.com"
                    value={formData.website}
                    onChange={handleInputChange('website')}
                    className={cn('pl-10', errors.website && 'border-red-500')}
                    disabled={isLoading}
                  />
                </div>
                <InlineError message={errors.website} />
              </div>

              <div>
                <Label htmlFor="founded">Founded Year</Label>
                <Input
                  id="founded"
                  type="number"
                  placeholder="2020"
                  value={formData.founded}
                  onChange={handleInputChange('founded')}
                  min="1800"
                  max={new Date().getFullYear()}
                  disabled={isLoading}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Company Description *</Label>
              <Textarea
                id="description"
                placeholder="Tell candidates about your company, mission, and what makes it a great place to work..."
                value={formData.description}
                onChange={handleInputChange('description')}
                className={cn('min-h-32 resize-none', errors.description && 'border-red-500')}
                disabled={isLoading}
              />
              <div className="flex justify-between items-center mt-1">
                <InlineError message={errors.description} />
                <span className="text-xs text-muted-foreground">
                  {formData.description.length}/2000 characters
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Location */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="w-5 h-5" />
              <span>Location</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  placeholder="San Francisco"
                  value={formData.location.city}
                  onChange={handleLocationChange('city')}
                  className={cn(errors.location && 'border-red-500')}
                  disabled={isLoading}
                />
              </div>

              <div>
                <Label htmlFor="state">State *</Label>
                <Input
                  id="state"
                  placeholder="CA"
                  value={formData.location.state}
                  onChange={handleLocationChange('state')}
                  className={cn(errors.location && 'border-red-500')}
                  disabled={isLoading}
                />
              </div>

              <div>
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  value={formData.location.country}
                  onChange={handleLocationChange('country')}
                  disabled={isLoading}
                />
              </div>
            </div>
            <InlineError message={errors.location} />
          </CardContent>
        </Card>

        {/* Branding */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="w-5 h-5" />
              <span>Branding</span>
            </CardTitle>
            <CardDescription>
              Add your company logo and cover image to make your profile stand out
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="logo">Company Logo URL</Label>
                <Input
                  id="logo"
                  placeholder="https://company.com/logo.png"
                  value={formData.logo}
                  onChange={handleInputChange('logo')}
                  disabled={isLoading}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Recommended: Square image, 200x200px minimum
                </p>
              </div>

              <div>
                <Label htmlFor="coverImage">Cover Image URL</Label>
                <Input
                  id="coverImage"
                  placeholder="https://company.com/cover.jpg"
                  value={formData.coverImage}
                  onChange={handleInputChange('coverImage')}
                  disabled={isLoading}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Recommended: 1200x400px, showcases your office or team
                </p>
              </div>
            </div>

            {/* Logo Preview */}
            {formData.logo && (
              <div className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg">
                <img
                  src={formData.logo}
                  alt="Company logo preview"
                  className="w-16 h-16 rounded-lg object-cover border"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
                <div>
                  <p className="text-sm font-medium">Logo Preview</p>
                  <p className="text-xs text-muted-foreground">
                    This is how your logo will appear on job postings
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Company Details */}
        <Card>
          <CardHeader>
            <CardTitle>Company Details</CardTitle>
            <CardDescription>
              Additional information to help candidates understand your company culture
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Mission */}
            <div>
              <Label htmlFor="mission">Mission Statement</Label>
              <Textarea
                id="mission"
                placeholder="Our mission is to..."
                value={formData.mission}
                onChange={handleInputChange('mission')}
                className="min-h-24 resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Culture */}
            <div>
              <Label htmlFor="culture">Company Culture</Label>
              <Textarea
                id="culture"
                placeholder="Describe your company culture, values, and work environment..."
                value={formData.culture}
                onChange={handleInputChange('culture')}
                className="min-h-24 resize-none"
                disabled={isLoading}
              />
            </div>

            {/* Specialties */}
            <div>
              <Label>Specialties</Label>
              <div className="space-y-2">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Add a specialty (e.g., Machine Learning, Cloud Computing)"
                    value={newSpecialty}
                    onChange={(e) => setNewSpecialty(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecialty())}
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addSpecialty}
                    disabled={!newSpecialty.trim() || isLoading}
                  >
                    Add
                  </Button>
                </div>
                {formData.specialties.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.specialties.map((specialty, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center space-x-1"
                      >
                        <span>{specialty}</span>
                        <button
                          type="button"
                          onClick={() => removeSpecialty(specialty)}
                          className="ml-1 hover:text-red-600"
                          disabled={isLoading}
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Benefits */}
            <div>
              <Label>Benefits & Perks</Label>
              <div className="space-y-2">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Add a benefit (e.g., Health Insurance, Remote Work)"
                    value={newBenefit}
                    onChange={(e) => setNewBenefit(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addBenefit())}
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addBenefit}
                    disabled={!newBenefit.trim() || isLoading}
                  >
                    Add
                  </Button>
                </div>
                {formData.benefits.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.benefits.map((benefit, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="flex items-center space-x-1"
                      >
                        <span>{benefit}</span>
                        <button
                          type="button"
                          onClick={() => removeBenefit(benefit)}
                          className="ml-1 hover:text-red-600"
                          disabled={isLoading}
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4 justify-end">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                  className="sm:w-auto w-full"
                >
                  Cancel
                </Button>
              )}

              <ButtonLoading
                type="submit"
                loading={isLoading}
                loadingText={mode === 'edit' ? 'Updating...' : 'Creating...'}
                disabled={isLoading}
                className="sm:w-auto w-full"
                size="lg"
              >
                {mode === 'edit' ? 'Update Company Profile' : 'Create Company Profile'}
              </ButtonLoading>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  )
}
