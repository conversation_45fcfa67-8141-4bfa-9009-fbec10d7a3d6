'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth.store'

export default function DashboardRedirectPage() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()

  useEffect(() => {
    console.log('Dashboard redirect - Auth state:', { isAuthenticated, user })

    // Redirect to appropriate dashboard based on user role
    if (isAuthenticated && user) {
      console.log('User role:', user.role)
      const redirectPath = user.role === 'company_admin' ? '/company-dashboard' :
                          user.role === 'admin' ? '/administration' :
                          '/client-dashboard'
      console.log('Dashboard redirect path:', redirectPath)
      router.replace(redirectPath)
    } else {
      // If not authenticated, redirect to login
      console.log('Not authenticated, redirecting to login')
      router.replace('/login')
    }
  }, [isAuthenticated, user, router])

  // Show loading while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Redirecting to your dashboard...</p>
      </div>
    </div>
  )
}
