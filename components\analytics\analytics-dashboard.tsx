'use client'

import React, { useEffect, useState } from 'react'
import { useJobsStore, useApplicationsStore, useCompaniesStore, useAuthStore } from '@/stores'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  TrendingUp, 
  TrendingDown,
  Users, 
  Briefcase, 
  Target,
  Clock,
  Calendar,
  Award,
  Eye,
  MessageSquare,
  Download,
  BarChart3,
  <PERSON><PERSON>hart,
  LineChart
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface AnalyticsDashboardProps {
  companyId?: string
  className?: string
}

interface AnalyticsData {
  overview: {
    totalJobs: number
    activeJobs: number
    totalApplications: number
    totalViews: number
    averageApplicationsPerJob: number
    responseRate: number
    timeToHire: number
    costPerHire: number
  }
  trends: {
    applicationsThisMonth: number
    applicationsLastMonth: number
    viewsThisMonth: number
    viewsLastMonth: number
    hiresThisMonth: number
    hiresLastMonth: number
  }
  jobPerformance: Array<{
    jobId: string
    title: string
    applications: number
    views: number
    conversionRate: number
    status: string
    postedAt: Date
  }>
  applicationFunnel: {
    submitted: number
    reviewed: number
    interviewed: number
    offered: number
    hired: number
  }
  sourceAnalytics: Array<{
    source: string
    applications: number
    hires: number
    conversionRate: number
  }>
  timeAnalytics: {
    averageTimeToFirstResponse: number
    averageTimeToInterview: number
    averageTimeToOffer: number
    averageTimeToHire: number
  }
}

export function AnalyticsDashboard({ companyId, className }: AnalyticsDashboardProps) {
  const { user } = useAuthStore()
  const { jobs, getJobsByCompany } = useJobsStore()
  const { applications, getApplicationsByCompany } = useApplicationsStore()
  const { currentCompany, getCompanyById } = useCompaniesStore()

  const [dateRange, setDateRange] = useState('30d')
  const [activeTab, setActiveTab] = useState('overview')
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)

  const effectiveCompanyId = companyId || user?.companyId

  // Load data
  useEffect(() => {
    if (effectiveCompanyId) {
      loadAnalyticsData()
    }
  }, [effectiveCompanyId, dateRange])

  const loadAnalyticsData = async () => {
    setLoading(true)
    try {
      // Load basic data
      await Promise.all([
        getJobsByCompany(effectiveCompanyId!),
        getApplicationsByCompany(effectiveCompanyId!),
        getCompanyById(effectiveCompanyId!)
      ])

      // Calculate analytics
      calculateAnalytics()
    } catch (error) {
      console.error('Failed to load analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateAnalytics = () => {
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)

    // Overview calculations
    const totalJobs = jobs.length
    const activeJobs = jobs.filter(job => job.isActive).length
    const totalApplications = applications.length
    const totalViews = jobs.reduce((sum, job) => sum + (job.viewsCount || 0), 0)
    const averageApplicationsPerJob = totalJobs > 0 ? Math.round(totalApplications / totalJobs) : 0
    
    const respondedApplications = applications.filter(app => 
      !['submitted', 'under_review'].includes(app.status)
    ).length
    const responseRate = totalApplications > 0 ? Math.round((respondedApplications / totalApplications) * 100) : 0

    // Trends calculations
    const applicationsThisMonth = applications.filter(app => 
      new Date(app.submittedAt) >= thirtyDaysAgo
    ).length
    const applicationsLastMonth = applications.filter(app => {
      const date = new Date(app.submittedAt)
      return date >= sixtyDaysAgo && date < thirtyDaysAgo
    }).length

    const viewsThisMonth = jobs.reduce((sum, job) => {
      // This would need actual view tracking by date
      return sum + Math.floor((job.viewsCount || 0) * 0.3) // Approximate
    }, 0)
    const viewsLastMonth = jobs.reduce((sum, job) => {
      return sum + Math.floor((job.viewsCount || 0) * 0.2) // Approximate
    }, 0)

    const hiresThisMonth = applications.filter(app => 
      app.status === 'offer_extended' && new Date(app.updatedAt) >= thirtyDaysAgo
    ).length
    const hiresLastMonth = applications.filter(app => {
      const date = new Date(app.updatedAt)
      return app.status === 'offer_extended' && date >= sixtyDaysAgo && date < thirtyDaysAgo
    }).length

    // Job performance
    const jobPerformance = jobs.map(job => {
      const jobApplications = applications.filter(app => app.jobId === job._id)
      const conversionRate = job.viewsCount && job.viewsCount > 0 
        ? Math.round((jobApplications.length / job.viewsCount) * 100)
        : 0

      return {
        jobId: job._id,
        title: job.title,
        applications: jobApplications.length,
        views: job.viewsCount || 0,
        conversionRate,
        status: job.isActive ? 'active' : 'inactive',
        postedAt: job.postedAt
      }
    }).sort((a, b) => b.applications - a.applications)

    // Application funnel
    const applicationFunnel = {
      submitted: applications.filter(app => app.status === 'submitted').length,
      reviewed: applications.filter(app => app.status === 'under_review').length,
      interviewed: applications.filter(app => 
        ['interview_scheduled', 'interviewed'].includes(app.status)
      ).length,
      offered: applications.filter(app => app.status === 'offer_extended').length,
      hired: applications.filter(app => app.status === 'offer_extended').length // Simplified
    }

    // Time analytics (simplified calculations)
    const timeAnalytics = {
      averageTimeToFirstResponse: 24, // hours
      averageTimeToInterview: 72, // hours
      averageTimeToOffer: 168, // hours (1 week)
      averageTimeToHire: 336 // hours (2 weeks)
    }

    // Source analytics (simplified)
    const sourceAnalytics = [
      { source: 'Direct', applications: Math.floor(totalApplications * 0.4), hires: Math.floor(hiresThisMonth * 0.4), conversionRate: 12 },
      { source: 'Job Boards', applications: Math.floor(totalApplications * 0.3), hires: Math.floor(hiresThisMonth * 0.3), conversionRate: 8 },
      { source: 'Social Media', applications: Math.floor(totalApplications * 0.2), hires: Math.floor(hiresThisMonth * 0.2), conversionRate: 15 },
      { source: 'Referrals', applications: Math.floor(totalApplications * 0.1), hires: Math.floor(hiresThisMonth * 0.1), conversionRate: 25 }
    ]

    setAnalyticsData({
      overview: {
        totalJobs,
        activeJobs,
        totalApplications,
        totalViews,
        averageApplicationsPerJob,
        responseRate,
        timeToHire: 14, // days
        costPerHire: 3500 // dollars
      },
      trends: {
        applicationsThisMonth,
        applicationsLastMonth,
        viewsThisMonth,
        viewsLastMonth,
        hiresThisMonth,
        hiresLastMonth
      },
      jobPerformance,
      applicationFunnel,
      sourceAnalytics,
      timeAnalytics
    })
  }

  // Calculate trend percentage
  const getTrendPercentage = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0
    return Math.round(((current - previous) / previous) * 100)
  }

  // Format time duration
  const formatDuration = (hours: number) => {
    if (hours < 24) return `${hours}h`
    const days = Math.floor(hours / 24)
    return `${days}d`
  }

  if (loading || !analyticsData) {
    return (
      <div className={cn('space-y-6', className)}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Track your hiring performance and optimize your recruitment process
          </p>
        </div>
        
        <Select value={dateRange} onValueChange={setDateRange}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="1y">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                <p className="text-2xl font-bold">{analyticsData.overview.totalApplications}</p>
                <div className="flex items-center space-x-1 mt-1">
                  {analyticsData.trends.applicationsThisMonth >= analyticsData.trends.applicationsLastMonth ? (
                    <TrendingUp className="w-4 h-4 text-green-600" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-600" />
                  )}
                  <span className={cn(
                    'text-xs',
                    analyticsData.trends.applicationsThisMonth >= analyticsData.trends.applicationsLastMonth
                      ? 'text-green-600'
                      : 'text-red-600'
                  )}>
                    {getTrendPercentage(analyticsData.trends.applicationsThisMonth, analyticsData.trends.applicationsLastMonth)}%
                  </span>
                </div>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Jobs</p>
                <p className="text-2xl font-bold">{analyticsData.overview.activeJobs}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  {analyticsData.overview.averageApplicationsPerJob} avg applications
                </p>
              </div>
              <Briefcase className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Response Rate</p>
                <p className="text-2xl font-bold">{analyticsData.overview.responseRate}%</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Candidates who received response
                </p>
              </div>
              <MessageSquare className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Time to Hire</p>
                <p className="text-2xl font-bold">{analyticsData.overview.timeToHire}d</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Average hiring time
                </p>
              </div>
              <Clock className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="jobs">Job Performance</TabsTrigger>
          <TabsTrigger value="funnel">Application Funnel</TabsTrigger>
          <TabsTrigger value="sources">Traffic Sources</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Application Funnel */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Application Funnel</span>
                </CardTitle>
                <CardDescription>
                  Track candidates through your hiring process
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(analyticsData.applicationFunnel).map(([stage, count]) => {
                  const percentage = analyticsData.overview.totalApplications > 0 
                    ? Math.round((count / analyticsData.overview.totalApplications) * 100)
                    : 0
                  
                  return (
                    <div key={stage} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium capitalize">
                          {stage.replace('_', ' ')}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          {count} ({percentage}%)
                        </span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  )
                })}
              </CardContent>
            </Card>

            {/* Time Analytics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="w-5 h-5" />
                  <span>Time Analytics</span>
                </CardTitle>
                <CardDescription>
                  Average time for each hiring stage
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-600">
                      {formatDuration(analyticsData.timeAnalytics.averageTimeToFirstResponse)}
                    </p>
                    <p className="text-xs text-muted-foreground">First Response</p>
                  </div>
                  
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">
                      {formatDuration(analyticsData.timeAnalytics.averageTimeToInterview)}
                    </p>
                    <p className="text-xs text-muted-foreground">To Interview</p>
                  </div>
                  
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-2xl font-bold text-purple-600">
                      {formatDuration(analyticsData.timeAnalytics.averageTimeToOffer)}
                    </p>
                    <p className="text-xs text-muted-foreground">To Offer</p>
                  </div>
                  
                  <div className="text-center p-3 bg-muted/50 rounded-lg">
                    <p className="text-2xl font-bold text-orange-600">
                      {formatDuration(analyticsData.timeAnalytics.averageTimeToHire)}
                    </p>
                    <p className="text-xs text-muted-foreground">To Hire</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Job Performance Tab */}
        <TabsContent value="jobs" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Job Performance</CardTitle>
              <CardDescription>
                See how your job postings are performing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.jobPerformance.slice(0, 10).map((job) => (
                  <div key={job.jobId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{job.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        Posted {new Date(job.postedAt).toLocaleDateString()}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <p className="font-semibold">{job.views}</p>
                        <p className="text-muted-foreground">Views</p>
                      </div>
                      
                      <div className="text-center">
                        <p className="font-semibold">{job.applications}</p>
                        <p className="text-muted-foreground">Applications</p>
                      </div>
                      
                      <div className="text-center">
                        <p className="font-semibold">{job.conversionRate}%</p>
                        <p className="text-muted-foreground">Conversion</p>
                      </div>
                      
                      <Badge variant={job.status === 'active' ? 'default' : 'secondary'}>
                        {job.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Application Funnel Tab */}
        <TabsContent value="funnel" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Application Funnel</CardTitle>
              <CardDescription>
                Analyze your hiring funnel performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(analyticsData.applicationFunnel).map(([stage, count], index) => {
                  const percentage = analyticsData.overview.totalApplications > 0 
                    ? Math.round((count / analyticsData.overview.totalApplications) * 100)
                    : 0
                  
                  const prevStage = Object.values(analyticsData.applicationFunnel)[index - 1]
                  const conversionRate = prevStage && prevStage > 0 
                    ? Math.round((count / prevStage) * 100)
                    : 100
                  
                  return (
                    <div key={stage} className="relative">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium capitalize">
                          {stage.replace('_', ' ')}
                        </h4>
                        <div className="flex items-center space-x-4 text-sm">
                          <span>{count} candidates</span>
                          <span className="text-muted-foreground">({percentage}% of total)</span>
                          {index > 0 && (
                            <Badge variant="outline">
                              {conversionRate}% conversion
                            </Badge>
                          )}
                        </div>
                      </div>
                      <Progress value={percentage} className="h-3" />
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Traffic Sources Tab */}
        <TabsContent value="sources" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Traffic Sources</CardTitle>
              <CardDescription>
                See where your best candidates come from
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.sourceAnalytics.map((source) => (
                  <div key={source.source} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h4 className="font-medium">{source.source}</h4>
                      <p className="text-sm text-muted-foreground">
                        {source.applications} applications • {source.hires} hires
                      </p>
                    </div>
                    
                    <div className="text-right">
                      <p className="text-lg font-semibold text-primary">
                        {source.conversionRate}%
                      </p>
                      <p className="text-xs text-muted-foreground">Conversion Rate</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
