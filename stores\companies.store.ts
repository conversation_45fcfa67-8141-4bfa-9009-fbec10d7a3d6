// atores/companies.store.ts
import { create } from 'zustand'
import { errorService, type AppError } from '@/lib/error-service'

// Types
export interface Company {
  _id: string
  name: string
  description: string
  logo?: string
  website?: string
  industry: string
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
  location: {
    headquarters: {
      city: string
      state: string
      country: string
    }
    offices: Array<{
      city: string
      state: string
      country: string
      isPrimary: boolean
    }>
  }
  contact: {
    email: string
    phone?: string
    address?: string
  }
  social: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
  benefits: string[]
  culture: {
    values: string[]
    perks: string[]
    workEnvironment: string
  }
  stats: {
    employeeCount: number
    foundedYear: number
    jobsCount: number
    applicationsCount: number
  }
  verification: {
    isVerified: boolean
    verifiedAt?: Date
    documents: string[]
  }
  subscription: {
    plan: 'free' | 'basic' | 'premium' | 'enterprise'
    status: 'active' | 'inactive' | 'suspended'
    expiresAt?: Date
  }
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CompanySearchQuery {
  q?: string
  industry?: string[]
  size?: string[]
  location?: string
  verified?: boolean
  page?: number
  limit?: number
  sortBy?: 'name' | 'size' | 'jobs' | 'founded'
  sortOrder?: 'asc' | 'desc'
}

export interface CompanyFilters {
  industries: string[]
  sizes: string[]
  locations: string[]
  verified: boolean
  hasJobs: boolean
}

interface CompaniesState {
  companies: Company[]
  currentCompany: Company | null
  searchQuery: CompanySearchQuery
  filters: Partial<CompanyFilters>
  followedCompanies: string[]

  // Loading states
  searchLoading: boolean
  companyLoading: boolean
  followLoading: boolean

  // Error states
  error: AppError | null
  searchError: AppError | null

  // Meta
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface CompaniesActions {
  searchCompanies: (query: CompanySearchQuery) => Promise<void>
  getCompanyById: (companyId: string) => Promise<void>
  getCompanyJobs: (companyId: string) => Promise<void>
  followCompany: (companyId: string) => Promise<void>
  unfollowCompany: (companyId: string) => Promise<void>
  getFollowedCompanies: () => Promise<void>
  updateFilters: (filters: Partial<CompanyFilters>) => void
  updateSearchQuery: (query: Partial<CompanySearchQuery>) => void
  clearSearch: () => void
  clearError: () => void
  clearSearchError: () => void
}

// API Service functions
const CompaniesAPI = {
  async searchCompanies(query: CompanySearchQuery) {
    const params = new URLSearchParams()
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()))
        } else {
          params.append(key, value.toString())
        }
      }
    })

    const response = await fetch(`/api/v1/companies/search?${params}`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Company search failed')
    }
    return response.json()
  },

  async getCompanyById(companyId: string) {
    const response = await fetch(`/api/v1/companies/${companyId}`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch company')
    }
    return response.json()
  },

  async getCompanyJobs(companyId: string) {
    const response = await fetch(`/api/v1/companies/${companyId}/jobs`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch company jobs')
    }
    return response.json()
  },

  async followCompany(companyId: string, token: string) {
    const response = await fetch(`/api/v1/companies/${companyId}/follow`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to follow company')
    }
    return response.json()
  },

  async unfollowCompany(companyId: string, token: string) {
    const response = await fetch(`/api/v1/companies/${companyId}/follow`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to unfollow company')
    }
    return response.json()
  },

  async getFollowedCompanies(token: string) {
    const response = await fetch('/api/v1/companies/followed', {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to get followed companies')
    }
    return response.json()
  }
}

export const useCompaniesStore = create<CompaniesState & CompaniesActions>((set) => ({
  // State
  companies: [],
  currentCompany: null,
  searchQuery: { page: 1, limit: 20 },
  filters: {},
  followedCompanies: [],

  // Loading states
  searchLoading: false,
  companyLoading: false,
  followLoading: false,

  // Error states
  error: null,
  searchError: null,

  // Meta
  pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },

  // Actions
  searchCompanies: async (query) => {
    set({ searchLoading: true, searchError: null })
    
    try {
      const response = await CompaniesAPI.searchCompanies(query)
      
      set({
        companies: response.companies,
        pagination: response.pagination,
        searchQuery: query,
        searchLoading: false
      })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_search')
      set({ 
        searchError: appError, 
        searchLoading: false 
      })
      throw appError
    }
  },

  getCompanyById: async (companyId) => {
    set({ companyLoading: true, error: null })
    try {
      const company = await CompaniesAPI.getCompanyById(companyId)
      set({
        currentCompany: company,
        companyLoading: false
      })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_get_by_id')
      set({
        error: appError,
        companyLoading: false
      })
      throw appError
    }
  },

  getCompanyJobs: async (companyId) => {
    try {
      const jobs = await CompaniesAPI.getCompanyJobs(companyId)
      
      // Update current company with jobs
      set(state => ({
        currentCompany: state.currentCompany ? {
          ...state.currentCompany,
          jobs
        } : null
      }))
      
      return jobs
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_get_jobs')
      set({ error: appError })
      throw appError
    }
  },

  followCompany: async (companyId) => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }

    set({ followLoading: true, error: null })
    try {
      await CompaniesAPI.followCompany(companyId, token)
      
      set(state => ({
        followedCompanies: [...state.followedCompanies, companyId],
        followLoading: false
      }))
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_follow')
      set({
        error: appError,
        followLoading: false
      })
      throw appError
    }
  },

  unfollowCompany: async (companyId) => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }

    set({ followLoading: true, error: null })
    try {
      await CompaniesAPI.unfollowCompany(companyId, token)
      
      set(state => ({
        followedCompanies: state.followedCompanies.filter(id => id !== companyId),
        followLoading: false
      }))
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_unfollow')
      set({
        error: appError,
        followLoading: false
      })
      throw appError
    }
  },

  getFollowedCompanies: async () => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      return // Skip if not authenticated
    }

    try {
      const followedCompanies = await CompaniesAPI.getFollowedCompanies(token)
      set({ followedCompanies: followedCompanies.map((company: Company) => company._id) })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'companies_get_followed')
      set({ error: appError })
    }
  },

  updateFilters: (filters) => {
    set(state => ({
      filters: { ...state.filters, ...filters }
    }))
  },

  updateSearchQuery: (query) => {
    set(state => ({
      searchQuery: { ...state.searchQuery, ...query }
    }))
  },

  clearSearch: () => {
    set({
      companies: [],
      searchQuery: { page: 1, limit: 20 },
      filters: {},
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 }
    })
  },

  clearError: () => set({ error: null }),
  clearSearchError: () => set({ searchError: null })
}))
