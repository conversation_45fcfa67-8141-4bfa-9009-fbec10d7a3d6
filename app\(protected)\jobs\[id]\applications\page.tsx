'use client'

import React, { useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useJobsStore, useAuthStore } from '@/stores'
import { ApplicationManagement } from '@/components/recruiter/application-management'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { PageLoader } from '@/components/ui/page-loader'
import { ErrorAlert } from '@/components/ui/error-alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Briefcase, MapPin, Calendar } from 'lucide-react'

function JobApplicationsContent() {
  const params = useParams()
  const router = useRouter()
  const jobId = params.id as string
  
  const { user } = useAuthStore()
  const { 
    currentJob,
    jobLoading,
    error,
    getJobById,
    clearError
  } = useJobsStore()

  // Load job details
  useEffect(() => {
    if (jobId) {
      getJobById(jobId)
    }
  }, [jobId, getJobById])

  // Check if user owns this job
  const canManage = user && currentJob && (
    user.role === 'admin' || 
    (user.companyId && user.companyId === currentJob.company._id)
  )

  // Loading state
  if (jobLoading) {
    return <PageLoader message="Loading job details..." fullScreen />
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <ErrorAlert
            type="error"
            title="Failed to Load Job"
            message={error.message || 'Could not load job details. Please try again.'}
            dismissible
            onDismiss={clearError}
            actions={
              <div className="flex space-x-2 mt-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push('/recruiter')}
                >
                  Back to Dashboard
                </Button>
                <Button
                  size="sm"
                  onClick={() => getJobById(jobId)}
                >
                  Try Again
                </Button>
              </div>
            }
          />
        </div>
      </div>
    )
  }

  // Job not found
  if (!currentJob) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Job Not Found</h1>
          <p className="text-muted-foreground mb-4">
            The job you're looking for doesn't exist or has been removed.
          </p>
          <Button onClick={() => router.push('/recruiter')}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    )
  }

  // Permission check
  if (!canManage) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don't have permission to manage applications for this job.
          </p>
          <Button onClick={() => router.push(`/jobs/${jobId}`)}>
            View Job Posting
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => router.push('/recruiter')}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Dashboard</span>
            </Button>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => router.push(`/jobs/${jobId}`)}
              >
                View Job Posting
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push(`/jobs/${jobId}/edit`)}
              >
                Edit Job
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Job Info */}
      <div className="container mx-auto px-4 py-6">
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                {/* Company Logo */}
                <div className="flex-shrink-0">
                  {currentJob.company.logo ? (
                    <img
                      src={currentJob.company.logo}
                      alt={`${currentJob.company.name} logo`}
                      className="w-16 h-16 rounded-lg object-cover border"
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-lg bg-muted flex items-center justify-center border">
                      <Briefcase className="w-8 h-8 text-muted-foreground" />
                    </div>
                  )}
                </div>

                {/* Job Info */}
                <div className="flex-1 min-w-0">
                  <CardTitle className="text-2xl mb-2">{currentJob.title}</CardTitle>
                  <CardDescription className="text-lg mb-3">
                    {currentJob.company.name}
                  </CardDescription>
                  
                  <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>
                        {currentJob.location.remote ? 'Remote' : 
                         `${currentJob.location.city}, ${currentJob.location.state}`}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>Posted {new Date(currentJob.postedAt).toLocaleDateString()}</span>
                    </div>
                    
                    <Badge variant="outline">
                      {currentJob.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="text-right">
                <div className="text-2xl font-bold text-primary">
                  {currentJob.applicationsCount || 0}
                </div>
                <div className="text-sm text-muted-foreground">Applications</div>
                
                <div className="text-lg font-semibold text-muted-foreground mt-2">
                  {currentJob.viewsCount || 0}
                </div>
                <div className="text-xs text-muted-foreground">Views</div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Application Management */}
        <ApplicationManagement 
          jobId={jobId} 
          companyId={currentJob.company._id}
        />
      </div>
    </div>
  )
}

export default function JobApplicationsPage() {
  return (
    <ProtectedRoute requiredRole="recruiter">
      <JobApplicationsContent />
    </ProtectedRoute>
  )
}
