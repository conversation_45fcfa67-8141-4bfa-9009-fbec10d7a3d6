import { NextRequest } from 'next/server'
import { with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, createSuccessResponse, validateMethod, validateRequestBody } from '@/lib/api/route-handler'
import { companyService, validationService } from '@/lib/services'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'
import { CompanyFilters } from '@/lib/types/api.types'
import type { CreateCompanyRequest } from '@/lib/services'

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic'

// Validation function for create company request
function validateCreateCompanyRequest(data: any): CreateCompanyRequest {
  const errors: string[] = []
  
  // Required fields
  if (!data.name) errors.push('Company name is required')
  
  // Validate website URL if provided
  if (data.website && !/^https?:\/\/.+/.test(data.website)) {
    errors.push('Website must be a valid URL')
  }
  
  // Validate social links if provided
  if (data.socialLinks) {
    const socialFields = ['linkedin', 'twitter', 'facebook', 'instagram']
    socialFields.forEach(field => {
      if (data.socialLinks[field] && !/^https?:\/\/.+/.test(data.socialLinks[field])) {
        errors.push(`${field} must be a valid URL`)
      }
    })
  }
  
  if (errors.length > 0) {
    throw errorService.createError(
      ErrorCode.VALIDATION_ERROR,
      `Validation failed: ${errors.join(', ')}`,
      undefined,
      { validationErrors: errors }
    )
  }
  
  return {
    name: data.name.trim(),
    description: data.description?.trim(),
    website: data.website?.trim(),
    industry: data.industry?.trim(),
    size: data.size?.trim(),
    location: data.location,
    logo: data.logo?.trim(),
    socialLinks: data.socialLinks
  }
}

// GET /api/v1/companies - Get companies with pagination and filters
export const GET = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['GET'])
  
  const { searchParams } = new URL(request.url)
  
  // Pagination
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '10')
  
  // Search term
  const search = searchParams.get('search')
  
  if (search) {
    // Search companies
    const companies = await companyService.searchCompanies(search, limit)
    return createSuccessResponse({ companies, pagination: null })
  }
  
  // Filters
  const filters: CompanyFilters = {}

  const industry = searchParams.get('industry')
  const size = searchParams.get('size')
  const location = searchParams.get('location')
  const isVerified = searchParams.get('isVerified')
  const isActive = searchParams.get('isActive')

  if (industry) filters.industry = industry.split(',')
  if (size) filters.size = size.split(',')
  if (location) filters.location = location
  if (isVerified) filters.isVerified = isVerified === 'true'
  if (isActive) filters.isFeatured = isActive === 'true'
  
  const result = await companyService.getCompanies(page, limit, filters)
  
  return createSuccessResponse(result)
}, {
  requireDatabase: true
})

// POST /api/v1/companies - Create a new company
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  const companyData = await validateRequestBody(request, validateCreateCompanyRequest)
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }
  
  const result = await companyService.createCompany(companyData, userId)
  
  return createSuccessResponse(result, 201)
}, {
  requireDatabase: true,
  requireAuth: true,
  requiredRoles: ['job_seeker', 'company_admin'] // Job seekers can create companies to become company admins
})

// Method not allowed for other HTTP methods
export async function PUT() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PUT method not allowed for companies collection'
  )
}

export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for companies collection'
  )
}
