'use client'

import Image from 'next/image'

export default function TestImagesPage() {
  const images = [
    '/images/hero/hero-main.jpg',
    '/images/hero/team-collaboration.jpg',
    '/images/hero/office-meeting.jpg',
    '/images/hero/office-background.jpg'
  ]

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Image Test Page</h1>
      
      <div className="grid grid-cols-2 gap-4">
        {images.map((src, index) => (
          <div key={index} className="border rounded-lg overflow-hidden">
            <h3 className="p-2 bg-gray-100 text-sm font-medium">
              Image {index + 1}: {src}
            </h3>
            <div className="relative h-48">
              <Image
                src={src}
                alt={`Test image ${index + 1}`}
                fill
                className="object-cover"
                onLoad={() => console.log(`✅ Loaded: ${src}`)}
                onError={(e) => console.error(`❌ Failed to load: ${src}`, e)}
              />
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-bold mb-4">Direct Image Tags (for comparison)</h2>
        <div className="grid grid-cols-2 gap-4">
          {images.map((src, index) => (
            <div key={index} className="border rounded-lg overflow-hidden">
              <h3 className="p-2 bg-gray-100 text-sm font-medium">
                Direct img tag: {src}
              </h3>
              <div className="h-48">
                <img
                  src={src}
                  alt={`Direct test image ${index + 1}`}
                  className="w-full h-full object-cover"
                  onLoad={() => console.log(`✅ Direct img loaded: ${src}`)}
                  onError={(e) => console.error(`❌ Direct img failed: ${src}`, e)}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
