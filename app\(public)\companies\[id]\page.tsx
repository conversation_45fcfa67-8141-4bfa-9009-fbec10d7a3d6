'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { getCompanyById } from '@/lib/company-data'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft,
  MapPin, 
  Star, 
  Users, 
  Calendar, 
  Heart, 
  MessageCircle,
  Building,
  CheckCircle,
  TrendingUp,
  Award,
  Briefcase,
  Globe,
  Mail,
  Phone,
  ExternalLink,
  Download,
  Share2,
  Flag,
  X,
  Target,
  Zap,
  Shield,
  Coffee,
  Gamepad2,
  GraduationCap,
  HeartHandshake,
  Plane,
  DollarSign,
  Eye,
  ThumbsUp,
  MessageSquare,
  FileText,
  Camera,
  Video,
  Link as LinkIcon
} from 'lucide-react'

export default function CompanyProfilePage() {
  const params = useParams()
  const router = useRouter()
  const companyId = params.id
  const [activeTab, setActiveTab] = useState('overview')
  const [isFollowing, setIsFollowing] = useState(false)

  // Get company data from service - in real app this would come from API based on ID
  const company = getCompanyById(Number(companyId)) || {
    id: companyId,
    name: "TechCorp Inc.",
    industry: "Software Development & Cloud Infrastructure",
    location: "San Francisco, CA",
    headquarters: "San Francisco, CA",
    logo: "/api/placeholder/120/120",
    coverImage: "/api/placeholder/1200/400",
    rating: 4.8,
    size: "large",
    founded: 2015,
    employees: "500-1000",
    description: "Leading software development company specializing in enterprise solutions and cloud infrastructure. We build scalable applications that power businesses worldwide, serving over 10,000 companies across 50+ countries. Our mission is to democratize technology and make powerful software accessible to businesses of all sizes.",
    specialties: ["Cloud Computing", "Enterprise Software", "AI/ML", "DevOps", "Microservices", "Data Analytics", "Cybersecurity", "API Development"],
    openJobs: 24,
    followers: 15420,
    website: "https://techcorp.com",
    benefits: ["Health Insurance", "Remote Work", "Stock Options", "Unlimited PTO", "Learning Budget", "Gym Membership"],
    culture: "Innovation-driven culture with focus on work-life balance, continuous learning, and collaborative problem-solving. We believe in empowering our team members to take ownership and drive meaningful impact.",
    verified: true,
    revenue: "$50M - $100M",
    funding: "Series C",
    investors: ["Sequoia Capital", "Andreessen Horowitz", "Google Ventures"],
    offices: ["San Francisco, CA", "New York, NY", "Austin, TX", "London, UK"],
    techStack: ["React", "Node.js", "Python", "AWS", "Kubernetes", "PostgreSQL", "Redis", "GraphQL"],
    awards: [
      "Best Places to Work 2024",
      "Top 50 Startups to Watch",
      "Innovation Award 2023"
    ],
    socialImpact: "Committed to environmental sustainability and social responsibility through our carbon-neutral operations and community education programs.",
    jobs: [
      {
        id: 1,
        title: "Senior Software Engineer",
        department: "Engineering",
        type: "Full-time",
        location: "San Francisco, CA",
        salary: "$120,000 - $180,000",
        posted: "2 days ago",
        description: "Join our core platform team to build scalable microservices that power our enterprise customers.",
        requirements: ["5+ years experience", "React/Node.js", "AWS", "Microservices"]
      },
      {
        id: 2,
        title: "Product Manager",
        department: "Product",
        type: "Full-time",
        location: "Remote",
        salary: "$130,000 - $160,000",
        posted: "1 week ago",
        description: "Lead product strategy and roadmap for our AI-powered analytics platform.",
        requirements: ["3+ years PM experience", "B2B SaaS", "Analytics", "AI/ML knowledge"]
      },
      {
        id: 3,
        title: "UX Designer",
        department: "Design",
        type: "Full-time",
        location: "New York, NY",
        salary: "$90,000 - $120,000",
        posted: "3 days ago",
        description: "Design intuitive user experiences for our enterprise dashboard and mobile applications.",
        requirements: ["4+ years UX design", "Figma", "Design systems", "Enterprise software"]
      },
      {
        id: 4,
        title: "DevOps Engineer",
        department: "Infrastructure",
        type: "Full-time",
        location: "Austin, TX",
        salary: "$110,000 - $150,000",
        posted: "5 days ago",
        description: "Build and maintain our cloud infrastructure and deployment pipelines.",
        requirements: ["AWS/GCP", "Kubernetes", "Terraform", "CI/CD", "Monitoring"]
      }
    ],
    team: [
      {
        name: "Sarah Johnson",
        role: "CEO & Founder",
        avatar: "/api/placeholder/80/80",
        bio: "Former VP of Engineering at Google with 15+ years in tech leadership. Stanford CS graduate with expertise in distributed systems.",
        linkedin: "https://linkedin.com/in/sarahjohnson"
      },
      {
        name: "Michael Chen",
        role: "CTO",
        avatar: "/api/placeholder/80/80",
        bio: "Ex-Amazon architect specializing in scalable cloud infrastructure. Led engineering teams of 100+ developers.",
        linkedin: "https://linkedin.com/in/michaelchen"
      },
      {
        name: "Emily Rodriguez",
        role: "VP of Product",
        avatar: "/api/placeholder/80/80",
        bio: "Product leader with experience at Airbnb and Stripe. Expert in B2B SaaS and product-market fit.",
        linkedin: "https://linkedin.com/in/emilyrodriguez"
      },
      {
        name: "David Kim",
        role: "VP of Engineering",
        avatar: "/api/placeholder/80/80",
        bio: "Engineering leader with 12+ years building scalable systems. Previously at Netflix and Uber.",
        linkedin: "https://linkedin.com/in/davidkim"
      }
    ],
    benefits: [
      { icon: Shield, title: "Health Insurance", description: "Comprehensive medical, dental, and vision coverage for you and your family" },
      { icon: Coffee, title: "Flexible Work", description: "Remote-first culture with flexible hours and unlimited PTO policy" },
      { icon: DollarSign, title: "Stock Options", description: "Equity participation for all employees with potential for significant upside" },
      { icon: Plane, title: "Unlimited PTO", description: "Take time off when you need it with our trust-based PTO policy" },
      { icon: GraduationCap, title: "Learning Budget", description: "$3,000 annual professional development budget for courses and conferences" },
      { icon: Gamepad2, title: "Team Events", description: "Regular team building, hackathons, and social events" },
      { icon: HeartHandshake, title: "Wellness Program", description: "Mental health support, gym membership, and wellness stipend" },
      { icon: Target, title: "Career Growth", description: "Clear career progression paths with mentorship and leadership opportunities" }
    ],
    reviews: [
      {
        id: 1,
        employee: "Anonymous Software Engineer",
        rating: 5,
        title: "Amazing place to grow your career",
        comment: "TechCorp has been an incredible place to work. The leadership is transparent, the technology is cutting-edge, and the team is brilliant. Great work-life balance and learning opportunities.",
        pros: ["Great leadership", "Cutting-edge tech", "Work-life balance", "Learning opportunities"],
        cons: ["Fast-paced environment", "High expectations"],
        date: "2 weeks ago",
        helpful: 24
      },
      {
        id: 2,
        employee: "Anonymous Product Manager",
        rating: 4,
        title: "Excellent product culture",
        comment: "The product team is very collaborative and data-driven. Lots of autonomy to drive impact. The company is growing fast which creates great opportunities but also some growing pains.",
        pros: ["Product-focused culture", "Data-driven decisions", "Growth opportunities", "Smart colleagues"],
        cons: ["Growing pains", "Sometimes chaotic", "Long hours during launches"],
        date: "1 month ago",
        helpful: 18
      }
    ],
    stats: {
      employeeSatisfaction: 4.8,
      ceoApproval: 92,
      recommendToFriend: 89,
      careerOpportunities: 4.6,
      workLifeBalance: 4.4,
      compensationBenefits: 4.7
    }
  }

  const getCompanySizeLabel = (size: string) => {
    switch (size) {
      case 'startup':
        return '1-10 employees'
      case 'small':
        return '11-50 employees'
      case 'medium':
        return '51-200 employees'
      case 'large':
        return '201-1000 employees'
      case 'enterprise':
        return '1000+ employees'
      default:
        return size
    }
  }

  return (
    <div className="pt-16">
      {/* Hero Section with Cover Image */}
      <section className="relative h-80 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-background to-primary/10" />
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px]" />
        
        {/* Back Button */}
        <div className="absolute top-6 left-8 z-10">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="bg-background/80 backdrop-blur-sm"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Companies
          </Button>
        </div>

        {/* Company Header */}
        <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-background via-background/80 to-transparent">
          <div className="container mx-auto max-w-6xl px-8">
            <div className="flex items-end space-x-6">
              <div className="relative">
                <Avatar className="w-32 h-32 border-4 border-background shadow-xl">
                  <AvatarImage src={company.logo} alt={company.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-3xl">
                    {company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                {company.verified && (
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-background flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>
              
              <div className="flex-1 pb-4">
                <h1 className="text-3xl font-bold mb-2">{company.name}</h1>
                <p className="text-primary font-medium text-xl mb-3">{company.industry}</p>
                <div className="flex items-center space-x-6 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-4 h-4" />
                    <span>{company.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span>{company.rating}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{getCompanySizeLabel(company.size)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>Founded {company.founded}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {company.verified && (
                    <Badge variant="secondary" className="bg-green-500 text-white">
                      Verified Company
                    </Badge>
                  )}
                  <Badge variant="outline">
                    {company.followers.toLocaleString()} followers
                  </Badge>
                  <Badge variant="outline">
                    {company.openJobs} open positions
                  </Badge>
                </div>
              </div>

              <div className="flex items-center space-x-4 pb-4">
                <div className="text-right mr-4">
                  <div className="text-4xl font-bold text-primary">{company.openJobs}</div>
                  <div className="text-sm text-muted-foreground">open jobs</div>
                </div>
                <div className="flex flex-col space-y-2">
                  <Button 
                    className="button-premium"
                    onClick={() => {
                      console.log('View jobs for:', company.name)
                    }}
                  >
                    <Briefcase className="w-4 h-4 mr-2" />
                    View Jobs
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => setIsFollowing(!isFollowing)}
                  >
                    <Heart className={`w-4 h-4 mr-2 ${isFollowing ? 'fill-current text-red-500' : ''}`} />
                    {isFollowing ? 'Following' : 'Follow'}
                  </Button>
                </div>
                <div className="flex flex-col space-y-2">
                  <Button variant="ghost" size="sm" className="p-2">
                    <Share2 className="w-5 h-5" />
                  </Button>
                  <Button variant="ghost" size="sm" className="p-2">
                    <ExternalLink className="w-5 h-5" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto max-w-6xl px-8 py-12">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6 mb-12 max-w-4xl mx-auto">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="jobs">Jobs</TabsTrigger>
            <TabsTrigger value="culture">Culture</TabsTrigger>
            <TabsTrigger value="benefits">Benefits</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-12 max-w-5xl mx-auto">
            {/* About Section */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-2xl">About {company.name}</CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <p className="text-muted-foreground leading-relaxed text-lg">{company.description}</p>
              </CardContent>
            </Card>

            {/* Specialties & Tech Stack */}
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-xl">
                    <Target className="w-5 h-5" />
                    <span>Specialties</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="flex flex-wrap gap-3">
                    {company.specialties.map((specialty) => (
                      <Badge key={specialty} variant="secondary" className="theme-glow text-sm py-2 px-4">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-xl">
                    <Zap className="w-5 h-5" />
                    <span>Tech Stack</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="flex flex-wrap gap-3">
                    {company.techStack.map((tech) => (
                      <Badge key={tech} variant="outline" className="text-sm py-2 px-4">
                        {tech}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Key Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <Card className="card-enhanced text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-primary mb-3">{company.openJobs}</div>
                  <div className="text-sm text-muted-foreground font-medium">Open Positions</div>
                </CardContent>
              </Card>
              <Card className="card-enhanced text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-primary mb-3">{company.rating}</div>
                  <div className="text-sm text-muted-foreground font-medium">Company Rating</div>
                </CardContent>
              </Card>
              <Card className="card-enhanced text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-primary mb-3">{company.followers.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground font-medium">Followers</div>
                </CardContent>
              </Card>
              <Card className="card-enhanced text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-primary mb-3">{company.founded}</div>
                  <div className="text-sm text-muted-foreground font-medium">Founded</div>
                </CardContent>
              </Card>
            </div>

            {/* Company Details */}
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-xl">
                    <Building className="w-5 h-5" />
                    <span>Company Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Industry:</span>
                      <span className="font-medium">{company.industry}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Company Size:</span>
                      <span className="font-medium">{company.employees}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Revenue:</span>
                      <span className="font-medium">{company.revenue}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Funding:</span>
                      <span className="font-medium">{company.funding}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-xl">
                    <Award className="w-5 h-5" />
                    <span>Awards & Recognition</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="space-y-3">
                    {company.awards.map((award, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <Award className="w-5 h-5 text-yellow-500" />
                        <span className="text-muted-foreground text-lg">{award}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="jobs" className="space-y-12 max-w-5xl mx-auto">
            <div className="grid gap-8">
              {company.jobs.map((job) => (
                <Card key={job.id} className="card-premium">
                  <CardContent className="p-10">
                    <div className="flex items-start justify-between mb-6">
                      <div>
                        <h3 className="text-2xl font-bold mb-2">{job.title}</h3>
                        <p className="text-primary font-medium text-lg mb-2">{job.department}</p>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>{job.type}</span>
                          <span>{job.location}</span>
                          <span>Posted {job.posted}</span>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-primary border-primary text-lg px-4 py-2">
                        {job.salary}
                      </Badge>
                    </div>

                    <p className="text-muted-foreground mb-6 text-lg">{job.description}</p>

                    <div className="mb-6">
                      <h4 className="font-medium mb-3">Requirements:</h4>
                      <div className="flex flex-wrap gap-2">
                        {job.requirements.map((req) => (
                          <Badge key={req} variant="secondary">{req}</Badge>
                        ))}
                      </div>
                    </div>

                    <Button className="button-premium">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Apply Now
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="culture" className="space-y-12 max-w-5xl mx-auto">
            {/* Culture Overview */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-2xl">Company Culture</CardTitle>
                <CardDescription className="text-lg">
                  What it's like to work at {company.name}
                </CardDescription>
              </CardHeader>
              <CardContent className="p-10">
                <p className="text-muted-foreground leading-relaxed text-lg mb-8">{company.culture}</p>

                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="font-bold text-lg mb-4">Our Values</h4>
                    <ul className="space-y-3">
                      <li className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span className="text-muted-foreground">Innovation and continuous learning</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span className="text-muted-foreground">Collaboration and teamwork</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span className="text-muted-foreground">Work-life balance</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span className="text-muted-foreground">Diversity and inclusion</span>
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-bold text-lg mb-4">Office Locations</h4>
                    <div className="space-y-2">
                      {company.offices.map((office, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <MapPin className="w-4 h-4 text-primary" />
                          <span className="text-muted-foreground">{office}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Social Impact */}
            <Card className="card-enhanced">
              <CardHeader>
                <CardTitle className="text-2xl">Social Impact</CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <p className="text-muted-foreground leading-relaxed text-lg">{company.socialImpact}</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="benefits" className="space-y-12 max-w-5xl mx-auto">
            <div className="grid gap-8">
              {company.benefits.map((benefit, index) => (
                <Card key={index} className="card-premium">
                  <CardContent className="p-8">
                    <div className="flex items-start space-x-6">
                      <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center">
                        <benefit.icon className="w-8 h-8 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-bold text-xl mb-3">{benefit.title}</h4>
                        <p className="text-muted-foreground text-lg leading-relaxed">{benefit.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="team" className="space-y-12 max-w-5xl mx-auto">
            <div className="grid gap-10">
              {company.team.map((member, index) => (
                <Card key={index} className="card-premium">
                  <CardContent className="p-10">
                    <div className="flex items-start space-x-6">
                      <Avatar className="w-24 h-24 border-2 border-primary/20">
                        <AvatarImage src={member.avatar} alt={member.name} />
                        <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-xl">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h4 className="font-bold text-2xl mb-2">{member.name}</h4>
                        <p className="text-primary font-medium text-lg mb-4">{member.role}</p>
                        <p className="text-muted-foreground text-lg leading-relaxed mb-4">{member.bio}</p>
                        <Button variant="outline" size="sm">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          LinkedIn Profile
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="reviews" className="space-y-12 max-w-5xl mx-auto">
            {/* Overall Stats */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-2xl">Employee Satisfaction</CardTitle>
                <CardDescription className="text-lg">
                  Based on employee reviews and feedback
                </CardDescription>
              </CardHeader>
              <CardContent className="p-10">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{company.stats.employeeSatisfaction}</div>
                    <div className="text-sm text-muted-foreground">Overall Rating</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{company.stats.ceoApproval}%</div>
                    <div className="text-sm text-muted-foreground">CEO Approval</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{company.stats.recommendToFriend}%</div>
                    <div className="text-sm text-muted-foreground">Recommend to Friend</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{company.stats.careerOpportunities}</div>
                    <div className="text-sm text-muted-foreground">Career Opportunities</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{company.stats.workLifeBalance}</div>
                    <div className="text-sm text-muted-foreground">Work-Life Balance</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{company.stats.compensationBenefits}</div>
                    <div className="text-sm text-muted-foreground">Compensation</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Individual Reviews */}
            {company.reviews.map((review) => (
              <Card key={review.id} className="card-premium">
                <CardContent className="p-10">
                  <div className="flex items-start justify-between mb-6">
                    <div>
                      <h4 className="font-bold text-lg mb-1">{review.title}</h4>
                      <p className="text-muted-foreground">{review.employee}</p>
                      <p className="text-sm text-muted-foreground">{review.date}</p>
                    </div>
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-5 h-5 ${
                            i < review.rating
                              ? 'fill-yellow-400 text-yellow-400'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  </div>

                  <blockquote className="text-muted-foreground text-lg leading-relaxed mb-6 italic">
                    "{review.comment}"
                  </blockquote>

                  <div className="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <h5 className="font-medium text-green-600 mb-2">Pros:</h5>
                      <ul className="space-y-1">
                        {review.pros.map((pro, i) => (
                          <li key={i} className="text-sm text-muted-foreground flex items-start space-x-2">
                            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 shrink-0" />
                            <span>{pro}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-medium text-orange-600 mb-2">Cons:</h5>
                      <ul className="space-y-1">
                        {review.cons.map((con, i) => (
                          <li key={i} className="text-sm text-muted-foreground flex items-start space-x-2">
                            <X className="w-4 h-4 text-orange-500 mt-0.5 shrink-0" />
                            <span>{con}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-4">
                      <Button variant="ghost" size="sm">
                        <ThumbsUp className="w-4 h-4 mr-2" />
                        Helpful ({review.helpful})
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Reply
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
