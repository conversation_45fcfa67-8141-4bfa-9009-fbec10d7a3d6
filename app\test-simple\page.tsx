export default function TestSimplePage() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Simple Image Test (No Event Handlers)</h1>
      
      <div className="space-y-4">
        <div>
          <h3 className="font-medium mb-2">Regular img tag:</h3>
          <img 
            src="/images/hero/hero-main.jpg" 
            alt="Test" 
            className="w-64 h-48 object-cover border rounded"
          />
        </div>

        <div>
          <h3 className="font-medium mb-2">All hero images:</h3>
          <div className="grid grid-cols-2 gap-4">
            <img 
              src="/images/hero/hero-main.jpg" 
              alt="Hero Main" 
              className="w-32 h-24 object-cover border rounded"
            />
            <img 
              src="/images/hero/team-collaboration.jpg" 
              alt="Team Collaboration" 
              className="w-32 h-24 object-cover border rounded"
            />
            <img 
              src="/images/hero/office-meeting.jpg" 
              alt="Office Meeting" 
              className="w-32 h-24 object-cover border rounded"
            />
            <img 
              src="/images/hero/office-background.jpg" 
              alt="Office Background" 
              className="w-32 h-24 object-cover border rounded"
            />
          </div>
        </div>

        <div>
          <h3 className="font-medium mb-2">Background image with CSS:</h3>
          <div 
            className="w-64 h-48 border rounded bg-cover bg-center"
            style={{ backgroundImage: 'url(/images/hero/hero-main.jpg)' }}
          />
        </div>

        <div>
          <h3 className="font-medium mb-2">Direct file access test:</h3>
          <a 
            href="/images/hero/hero-main.jpg" 
            target="_blank" 
            className="text-blue-500 underline"
          >
            Click to open image directly
          </a>
        </div>
      </div>
    </div>
  )
}
