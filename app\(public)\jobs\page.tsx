'use client'

import React, { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { She<PERSON>, <PERSON>et<PERSON>ontent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { Badge } from '@/components/ui/badge'
import { PremiumJobFilters } from '@/components/jobs/premium-job-filters'
import { PremiumJobCard } from '@/components/jobs/premium-job-card'
import { PremiumJobDetailModal } from '@/components/jobs/premium-job-detail-modal'
import { JobApplicationModal } from '@/components/jobs/job-application-modal'
import { TalentPagination, TalentSorting, TalentResultsSummary } from '@/components/talent/talent-pagination'
import { jobs } from '@/lib/job-data'
import { useLocationStore } from '@/stores/location-store'
import {
  Search,
  Filter,
  Grid3X3,
  List,
  MapPin,
  Briefcase,
  TrendingUp,
  Award,
  Target,
  Users,
  Clock,
  DollarSign,
  Star,
  Zap
} from 'lucide-react'

export default function JobsPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { currentLocation } = useLocationStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [showDesktopFilters, setShowDesktopFilters] = useState(true)
  const [sortBy, setSortBy] = useState('relevance')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(12)
  const [selectedJob, setSelectedJob] = useState<any>(null)
  const [applicationJob, setApplicationJob] = useState<any>(null)

  // URL parameter states
  const [locationFilter, setLocationFilter] = useState<string>('')
  const [remoteFilter, setRemoteFilter] = useState<boolean>(false)
  const [activeFilterType, setActiveFilterType] = useState<string>('')

  // Filter states
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>([])
  const [selectedExperience, setSelectedExperience] = useState<string[]>([])
  const [salaryRange, setSalaryRange] = useState([0, 300000])
  const [selectedWorkModel, setSelectedWorkModel] = useState<string[]>([])

  // Handle URL parameters on component mount
  useEffect(() => {
    const location = searchParams.get('location')
    const remote = searchParams.get('remote')

    if (location) {
      setLocationFilter(location)
      // Determine filter type based on location value
      if (currentLocation) {
        if (location === currentLocation.city) {
          setActiveFilterType('Local')
        } else if (location === currentLocation.region) {
          setActiveFilterType('Regional')
        } else if (location === currentLocation.country) {
          setActiveFilterType('National')
        } else if (location === currentLocation.continent) {
          setActiveFilterType('Continental')
        } else {
          setActiveFilterType('Location')
        }
      } else {
        setActiveFilterType('Location')
      }
    }

    if (remote === 'true') {
      setRemoteFilter(true)
      setActiveFilterType('Remote/Global')
    }
  }, [searchParams, currentLocation])

  const activeFilterCount = selectedCategories.length +
    selectedJobTypes.length +
    selectedExperience.length +
    selectedWorkModel.length +
    (salaryRange[0] > 0 || salaryRange[1] < 300000 ? 1 : 0)

  // Filter jobs based on URL parameters and other filters
  const filteredJobs = jobs.filter(job => {
    // Location-based filtering
    if (locationFilter) {
      const jobLocation = job.location?.toLowerCase() || ''
      const filterLocation = locationFilter.toLowerCase()

      // Check if job location matches the filter
      if (!jobLocation.includes(filterLocation)) {
        return false
      }
    }

    // Remote filtering
    if (remoteFilter) {
      const isRemote = job.location?.toLowerCase().includes('remote') ||
                      job.workModel?.toLowerCase().includes('remote') ||
                      job.type?.toLowerCase().includes('remote')
      if (!isRemote) {
        return false
      }
    }

    // Apply other existing filters
    if (selectedCategories.length > 0 && !selectedCategories.includes(job.category)) {
      return false
    }

    if (selectedJobTypes.length > 0 && !selectedJobTypes.includes(job.type)) {
      return false
    }

    if (selectedExperience.length > 0 && !selectedExperience.includes(job.experience)) {
      return false
    }

    if (selectedWorkModel.length > 0 && !selectedWorkModel.includes(job.workModel || 'On-site')) {
      return false
    }

    // Salary range filtering
    if (job.salary) {
      const jobMinSalary = job.salary.min || 0
      const jobMaxSalary = job.salary.max || 0
      if (jobMaxSalary < salaryRange[0] || jobMinSalary > salaryRange[1]) {
        return false
      }
    }

    // Search query filtering
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      const searchableText = [
        job.title,
        job.company?.name,
        job.location,
        job.description,
        ...(job.skills || [])
      ].join(' ').toLowerCase()

      if (!searchableText.includes(query)) {
        return false
      }
    }

    return true
  })

  // Sort filtered jobs
  const sortedJobs = [...filteredJobs].sort((a, b) => {
    switch (sortBy) {
      case 'date':
        return sortOrder === 'desc' ?
          new Date(b.posted).getTime() - new Date(a.posted).getTime() :
          new Date(a.posted).getTime() - new Date(b.posted).getTime()
      case 'salary':
        const aSalary = a.salary?.max || 0
        const bSalary = b.salary?.max || 0
        return sortOrder === 'desc' ? bSalary - aSalary : aSalary - bSalary
      case 'company':
        const aCompany = a.company?.name || ''
        const bCompany = b.company?.name || ''
        return sortOrder === 'desc' ?
          bCompany.localeCompare(aCompany) :
          aCompany.localeCompare(bCompany)
      default: // relevance
        return 0
    }
  })

  // Pagination calculations
  const totalJobs = sortedJobs.length
  const totalPages = Math.ceil(totalJobs / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentJobs = sortedJobs.slice(startIndex, endIndex)

  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy)
    setSortOrder(newSortOrder)
    setCurrentPage(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    document.querySelector('#jobs-results')?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage)
    setCurrentPage(1)
  }

  return (
    <div className="pt-16">
      {/* Hero Section - Mobile Optimized */}
      <section className="relative py-12 md:py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-background to-primary/10" />
        {/* Hide pattern on mobile for better performance */}
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px] hidden md:block" />

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-bold mb-4 md:mb-6 bg-gradient-to-r from-foreground via-primary to-foreground bg-clip-text text-transparent"
            >
              Find Your Dream Job
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-base sm:text-lg md:text-xl text-muted-foreground mb-6 md:mb-8 max-w-2xl mx-auto px-4 md:px-0"
            >
              <span className="hidden md:inline">
                Discover amazing career opportunities with top companies. Find jobs that match your skills, experience, and career goals.
              </span>
              <span className="md:hidden">
                Find jobs that match your skills and goals.
              </span>
            </motion.p>

            {/* Stats - Simplified for mobile */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-wrap items-center justify-center gap-4 md:gap-6 text-xs md:text-sm text-muted-foreground"
            >
              <div className="flex items-center space-x-1.5 md:space-x-2">
                <TrendingUp className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                <span>
                  <span className="hidden sm:inline">10,000+ Active Jobs</span>
                  <span className="sm:hidden">10k+ Jobs</span>
                </span>
              </div>
              <div className="flex items-center space-x-1.5 md:space-x-2">
                <Award className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                <span>
                  <span className="hidden sm:inline">500+ Top Companies</span>
                  <span className="sm:hidden">500+ Co.</span>
                </span>
              </div>
              {/* Hide categories stat on mobile */}
              <div className="hidden sm:flex items-center space-x-1.5 md:space-x-2">
                <Target className="w-3 h-3 md:w-4 md:h-4 text-primary" />
                <span>50+ Job Categories</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Search and Filters - Mobile Optimized */}
      <section className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-16 z-40">
        <div className="container mx-auto px-4 py-4 md:py-6">
          <div className="flex flex-col lg:flex-row gap-3 md:gap-4">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search jobs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 input-enhanced h-10 md:h-auto"
              />
            </div>

            {/* Desktop Controls */}
            <div className="hidden lg:flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => setShowDesktopFilters(!showDesktopFilters)}
                className={showDesktopFilters ? 'bg-primary/10 border-primary/50' : ''}
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-2 theme-glow">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>

              <TalentSorting
                sortBy={sortBy}
                sortOrder={sortOrder}
                onSortChange={handleSortChange}
              />

              {/* View Mode Toggle - Hidden on Mobile */}
              <div className="hidden md:flex items-center space-x-2 bg-muted/50 rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Mobile Controls - Optimized */}
            <div className="flex lg:hidden items-center space-x-2">
              <Sheet open={showMobileFilters} onOpenChange={setShowMobileFilters}>
                <SheetTrigger asChild>
                  <Button variant="outline" className="flex-1 h-10">
                    <Filter className="w-4 h-4 mr-2" />
                    <span className="hidden sm:inline">Filters</span>
                    <span className="sm:hidden">Filter</span>
                    {activeFilterCount > 0 && (
                      <Badge variant="secondary" className="ml-2 theme-glow text-xs">
                        {activeFilterCount}
                      </Badge>
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-[90vw] sm:w-80 overflow-y-auto">
                  <SheetHeader>
                    <SheetTitle>Job Filters</SheetTitle>
                  </SheetHeader>
                  <div className="mt-6">
                    <PremiumJobFilters
                      selectedCategories={selectedCategories}
                      setSelectedCategories={setSelectedCategories}
                      selectedJobTypes={selectedJobTypes}
                      setSelectedJobTypes={setSelectedJobTypes}
                      selectedExperience={selectedExperience}
                      setSelectedExperience={setSelectedExperience}
                      salaryRange={salaryRange}
                      setSalaryRange={setSalaryRange}
                      selectedWorkModel={selectedWorkModel}
                      setSelectedWorkModel={setSelectedWorkModel}
                      onFiltersChange={() => setShowMobileFilters(false)}
                    />
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content - Mobile Optimized */}
      <main className="container mx-auto px-4 py-6 md:py-8">
        <div className="flex gap-6 md:gap-8">
          {/* Desktop Filters Sidebar */}
          <aside className={`hidden lg:block transition-all duration-300 ${
            showDesktopFilters ? 'w-80' : 'w-0 overflow-hidden'
          }`}>
            {showDesktopFilters && (
              <div className="sticky top-40">
                <PremiumJobFilters
                  selectedCategories={selectedCategories}
                  setSelectedCategories={setSelectedCategories}
                  selectedJobTypes={selectedJobTypes}
                  setSelectedJobTypes={setSelectedJobTypes}
                  selectedExperience={selectedExperience}
                  setSelectedExperience={setSelectedExperience}
                  salaryRange={salaryRange}
                  setSalaryRange={setSalaryRange}
                  selectedWorkModel={selectedWorkModel}
                  setSelectedWorkModel={setSelectedWorkModel}
                  showApplyButton={false}
                />
              </div>
            )}
          </aside>

          {/* Jobs Grid/List */}
          <div className="flex-1" id="jobs-results">
            <TalentResultsSummary
              totalResults={totalJobs}
              searchQuery={searchQuery}
              activeFilters={activeFilterCount}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />

            {/* Active Filter Indicator */}
            {(locationFilter || remoteFilter || activeFilterType) && (
              <div className="mb-6 p-4 bg-primary/5 border border-primary/20 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-primary" />
                    <div>
                      <h3 className="font-medium text-foreground">
                        Showing {activeFilterType} Jobs
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {remoteFilter
                          ? 'Remote and global opportunities'
                          : `Jobs in ${locationFilter}`
                        }
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      router.push('/jobs')
                      setLocationFilter('')
                      setRemoteFilter(false)
                      setActiveFilterType('')
                    }}
                  >
                    View All Jobs
                  </Button>
                </div>
              </div>
            )}

            {/* Jobs Grid/List - Responsive */}
            <AnimatePresence mode="wait">
              <motion.div
                key={viewMode}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className={`
                  grid grid-cols-1 gap-4 md:gap-6
                  ${viewMode === 'grid' ? 'md:grid-cols-2 xl:grid-cols-3' : 'md:space-y-4 md:block'}
                `}
              >
                {currentJobs.map((job, index) => (
                  <motion.div
                    key={job.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className={viewMode === 'list' ? 'md:mb-4' : ''}
                  >
                    <PremiumJobCard
                      job={job}
                      viewMode={viewMode} // Pass the actual viewMode - component will handle mobile responsiveness
                      onViewJob={setSelectedJob}
                      onSaveJob={(job) => {
                        console.log('Save job:', job.title)
                      }}
                      onApplyJob={(job) => {
                        setApplicationJob(job)
                      }}
                    />
                  </motion.div>
                ))}
              </motion.div>
            </AnimatePresence>

            {/* Pagination */}
            {totalPages > 1 && (
              <TalentPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalJobs}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </div>
      </main>

      {/* Job Detail Modal */}
      <PremiumJobDetailModal
        job={selectedJob}
        isOpen={!!selectedJob}
        onClose={() => setSelectedJob(null)}
        onApply={(job) => {
          setApplicationJob(job)
        }}
        onSave={(job) => {
          console.log('Save job:', job.title)
          // In real app, would save to user's saved jobs
        }}
      />

      {/* Job Application Modal */}
      <JobApplicationModal
        job={applicationJob}
        isOpen={!!applicationJob}
        onClose={() => setApplicationJob(null)}
        onSubmit={(applicationData) => {
          console.log('Application submitted:', applicationData)
          // In real app, would submit to API
          alert(`Application submitted successfully for ${applicationData.jobTitle}!`)
        }}
      />
    </div>
  )
}
