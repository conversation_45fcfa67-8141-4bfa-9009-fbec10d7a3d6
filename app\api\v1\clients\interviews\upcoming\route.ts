import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { Client } from '@/lib/models/client.model'
import { Application } from '@/lib/models/application.model'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client
    const client = await Client.findOne({ user: userId })
    if (!client) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    // Get upcoming interviews (applications with interview scheduled and future interview dates)
    const now = new Date()
    const upcomingInterviews = await Application.find({
      client: client._id,
      status: 'interview_scheduled',
      interviewDate: { $gte: now }
    })
    .populate('job', 'title company location')
    .sort({ interviewDate: 1 })
    .limit(10)
    .lean()

    // Format interviews
    const formattedInterviews = upcomingInterviews.map(interview => ({
      id: interview._id.toString(),
      company: interview.job?.company || 'Unknown Company',
      position: interview.job?.title || 'Unknown Position',
      date: interview.interviewDate?.toISOString().split('T')[0] || '',
      time: interview.interviewDate?.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }) || '',
      type: interview.interviewType || 'phone',
      interviewer: interview.contactPerson?.name || 'TBD',
      notes: interview.interviewNotes || '',
      meetingLink: interview.interviewLocation || ''
    }))

    return NextResponse.json({
      success: true,
      data: formattedInterviews
    })

  } catch (error) {
    console.error('Get upcoming interviews error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch upcoming interviews' },
      { status: 500 }
    )
  }
}
