"use client"

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { useCompanyStore } from '@/stores/company.store'
import { useToast } from '@/hooks/use-toast'
import {
  Building2,
  MapPin,
  Globe,
  Users,
  Calendar,
  Edit,
  Save,
  Upload,
  Eye,
  Star,
  Briefcase,
  Mail,
  Phone,
  ExternalLink,
  Plus,
  Trash2,
  Linkedin,
  Twitter,
  Facebook,
  Instagram,
  Github,
  Youtube
} from 'lucide-react'
import { updateCompanyProfileSchema } from '@/types/company-management.types'
import type { EnhancedCompany } from '@/types/company-management.types'

interface EnhancedCompanyProfileProps {
  companyId?: string
}

export function EnhancedCompanyProfile({ companyId }: EnhancedCompanyProfileProps) {
  const { toast } = useToast()
  const {
    company,
    profileLoading,
    updateLoading,
    error,
    fetchCompanyProfile,
    createCompanyProfile,
    updateCompanyProfile,
    updateCompanyCulture,
    updateSocialLinks,
    addLocation,
    updateLocation,
    removeLocation,
    clearError
  } = useCompanyStore()

  const [activeTab, setActiveTab] = useState("overview")
  const [isEditing, setIsEditing] = useState(false)
  const [editingLocation, setEditingLocation] = useState<string | null>(null)

  // Form for basic profile
  const profileForm = useForm({
    resolver: zodResolver(updateCompanyProfileSchema),
    defaultValues: {
      name: '',
      description: '',
      tagline: '',
      website: '',
      industry: [],
      size: 'startup' as const,
      founded: new Date().getFullYear(),
      contact: {
        email: '',
        phone: '',
        address: '',
        supportEmail: '',
        hrEmail: ''
      }
    }
  })

  // Form for culture
  const [cultureData, setCultureData] = useState({
    values: [] as string[],
    benefits: [] as string[],
    workEnvironment: '',
    diversity: '',
    mission: '',
    vision: '',
    perks: [] as string[]
  })

  // Form for social links
  const [socialData, setSocialData] = useState({
    linkedin: '',
    twitter: '',
    facebook: '',
    instagram: '',
    github: '',
    youtube: '',
    glassdoor: ''
  })

  // Form for new location
  const [newLocation, setNewLocation] = useState({
    city: '',
    state: '',
    country: '',
    isHeadquarters: false,
    address: '',
    postalCode: ''
  })

  useEffect(() => {
    if (companyId || company) {
      fetchCompanyProfile()
    }
  }, [companyId, fetchCompanyProfile])

  useEffect(() => {
    if (company) {
      // Update profile form
      profileForm.reset({
        name: company.name || '',
        description: company.description || '',
        tagline: company.tagline || '',
        website: company.website || '',
        industry: company.industry || [],
        size: company.size || 'startup',
        founded: company.founded || new Date().getFullYear(),
        contact: {
          email: company.contact?.email || '',
          phone: company.contact?.phone || '',
          address: company.contact?.address || '',
          supportEmail: company.contact?.supportEmail || '',
          hrEmail: company.contact?.hrEmail || ''
        }
      })

      // Update culture data
      setCultureData({
        values: company.culture?.values || [],
        benefits: company.culture?.benefits || [],
        workEnvironment: company.culture?.workEnvironment || '',
        diversity: company.culture?.diversity || '',
        mission: company.culture?.mission || '',
        vision: company.culture?.vision || '',
        perks: company.culture?.perks || []
      })

      // Update social data
      setSocialData({
        linkedin: company.socialLinks?.linkedin || '',
        twitter: company.socialLinks?.twitter || '',
        facebook: company.socialLinks?.facebook || '',
        instagram: company.socialLinks?.instagram || '',
        github: company.socialLinks?.github || '',
        youtube: company.socialLinks?.youtube || '',
        glassdoor: company.socialLinks?.glassdoor || ''
      })
    }
  }, [company, profileForm])

  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: error,
        variant: "destructive"
      })
      clearError()
    }
  }, [error, toast, clearError])

  const handleProfileSubmit = async (data: Record<string, unknown>) => {
    try {
      if (company) {
        await updateCompanyProfile(data)
        setIsEditing(false)
        toast({
          title: "Success",
          description: "Company profile updated successfully"
        })
      } else {
        await createCompanyProfile(data)
        toast({
          title: "Success",
          description: "Company profile created successfully"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: company ? "Failed to update company profile" : "Failed to create company profile",
        variant: "destructive"
      })
    }
  }

  const handleCultureSubmit = async () => {
    try {
      await updateCompanyCulture(cultureData)
      toast({
        title: "Success",
        description: "Company culture updated successfully"
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update company culture",
        variant: "destructive"
      })
    }
  }

  const handleSocialSubmit = async () => {
    try {
      await updateSocialLinks(socialData)
      toast({
        title: "Success",
        description: "Social links updated successfully"
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update social links",
        variant: "destructive"
      })
    }
  }

  const handleAddLocation = async () => {
    try {
      await addLocation(newLocation)
      setNewLocation({
        city: '',
        state: '',
        country: '',
        isHeadquarters: false,
        address: '',
        postalCode: ''
      })
      toast({
        title: "Success",
        description: "Location added successfully"
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add location",
        variant: "destructive"
      })
    }
  }

  const handleRemoveLocation = async (locationId: string) => {
    try {
      await removeLocation(locationId)
      toast({
        title: "Success",
        description: "Location removed successfully"
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove location",
        variant: "destructive"
      })
    }
  }

  const addArrayItem = (
    array: string[], 
    setArray: (items: string[]) => void, 
    newItem: string
  ) => {
    if (newItem.trim() && !array.includes(newItem.trim())) {
      setArray([...array, newItem.trim()])
    }
  }

  const removeArrayItem = (
    array: string[], 
    setArray: (items: string[]) => void, 
    index: number
  ) => {
    setArray(array.filter((_, i) => i !== index))
  }

  if (profileLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!company) {
    return (
      <div className="w-full h-full">
        <div className="w-full p-6">
          {/* Header Section */}
          <div className="w-full flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Company Profile Setup</h1>
              <p className="text-muted-foreground mt-2">
                Create your company profile to get started
              </p>
            </div>
          </div>

          {/* Company Creation Form */}
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Create Your Company Profile</CardTitle>
              <CardDescription>
                Let's start by setting up your basic company information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={profileForm.handleSubmit(handleProfileSubmit)} className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Company Name *</Label>
                    <Input
                      id="name"
                      {...profileForm.register('name')}
                      placeholder="Enter your company name"
                      className="mt-1"
                    />
                    {profileForm.formState.errors.name && (
                      <p className="text-sm text-red-500 mt-1">
                        {profileForm.formState.errors.name.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="description">Company Description *</Label>
                    <Textarea
                      id="description"
                      {...profileForm.register('description')}
                      placeholder="Describe your company, its mission, and what makes it unique..."
                      className="mt-1"
                      rows={4}
                    />
                    {profileForm.formState.errors.description && (
                      <p className="text-sm text-red-500 mt-1">
                        {profileForm.formState.errors.description.message}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        {...profileForm.register('website')}
                        placeholder="https://company.com"
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="contact.email">Contact Email *</Label>
                      <Input
                        id="contact.email"
                        type="email"
                        {...profileForm.register('contact.email')}
                        placeholder="<EMAIL>"
                        className="mt-1"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="size">Company Size</Label>
                      <Select
                        value={profileForm.watch('size')}
                        onValueChange={(value) => profileForm.setValue('size', value as any)}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select company size" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="startup">Startup (1-10)</SelectItem>
                          <SelectItem value="small">Small (11-50)</SelectItem>
                          <SelectItem value="medium">Medium (51-200)</SelectItem>
                          <SelectItem value="large">Large (201-1000)</SelectItem>
                          <SelectItem value="enterprise">Enterprise (1000+)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="founded">Founded Year</Label>
                      <Input
                        id="founded"
                        type="number"
                        {...profileForm.register('founded', { valueAsNumber: true })}
                        placeholder="2020"
                        className="mt-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button type="submit" disabled={updateLoading} className="w-full md:w-auto">
                    {updateLoading ? 'Creating...' : 'Create Company Profile'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full">
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Enhanced Company Profile</h1>
            <p className="text-muted-foreground mt-2">
              Comprehensive company management and profile customization
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Eye className="w-4 h-4 mr-2" />
              View Public Profile
            </Button>
            {isEditing ? (
              <>
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={profileForm.handleSubmit(handleProfileSubmit)}
                  disabled={updateLoading}
                >
                  <Save className="w-4 h-4 mr-2" />
                  {updateLoading ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            ) : (
              <Button onClick={() => setIsEditing(true)}>
                <Edit className="w-4 h-4 mr-2" />
                Edit Profile
              </Button>
            )}
          </div>
        </div>

        {/* Enhanced Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Company Details</TabsTrigger>
            <TabsTrigger value="culture">Culture & Values</TabsTrigger>
            <TabsTrigger value="locations">Locations</TabsTrigger>
            <TabsTrigger value="social">Social Links</TabsTrigger>
            <TabsTrigger value="branding">Branding</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-3">
              {/* Company Info Card */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Company Information</CardTitle>
                  <CardDescription>
                    Basic information about your company
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <Avatar className="w-16 h-16">
                      <AvatarImage src={company?.logo} />
                      <AvatarFallback>
                        <Building2 className="w-8 h-8" />
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold">{company?.name || 'Company Name'}</h3>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                        {company?.industry && company.industry.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {company.industry.map((ind, index) => (
                              <Badge key={index} variant="secondary">{ind}</Badge>
                            ))}
                          </div>
                        )}
                        {company?.locations && company.locations.length > 0 && (
                          <div className="flex items-center space-x-1">
                            <MapPin className="w-4 h-4" />
                            <span>
                              {company.locations.find(loc => loc.isHeadquarters)?.city || 
                               company.locations[0]?.city}, {' '}
                              {company.locations.find(loc => loc.isHeadquarters)?.country || 
                               company.locations[0]?.country}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label>Company Description</Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        {company?.description || 'No description provided'}
                      </p>
                    </div>

                    {company?.tagline && (
                      <div>
                        <Label>Tagline</Label>
                        <p className="text-sm text-muted-foreground mt-1 italic">
                          "{company.tagline}"
                        </p>
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Website</Label>
                        <div className="mt-1">
                          {company?.website ? (
                            <a 
                              href={company.website} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-sm text-primary hover:underline flex items-center space-x-1"
                            >
                              <Globe className="w-4 h-4" />
                              <span>{company.website}</span>
                              <ExternalLink className="w-3 h-3" />
                            </a>
                          ) : (
                            <span className="text-sm text-muted-foreground">No website provided</span>
                          )}
                        </div>
                      </div>

                      <div>
                        <Label>Industry</Label>
                        <div className="mt-1">
                          {company?.industry && company.industry.length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {company.industry.map((ind, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {ind}
                                </Badge>
                              ))}
                            </div>
                          ) : (
                            <span className="text-sm text-muted-foreground">Not specified</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Company Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>Company Stats</CardTitle>
                  <CardDescription>
                    Key metrics and information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Company Size</span>
                    </div>
                    <span className="text-sm font-medium capitalize">
                      {company?.size || 'Not specified'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Founded</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.founded || 'Not specified'}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Briefcase className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Active Jobs</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.stats?.activeJobs || 0}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Eye className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Profile Views</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.stats?.profileViews || 0}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">Locations</span>
                    </div>
                    <span className="text-sm font-medium">
                      {company?.locations?.length || 0}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Company Details Tab */}
          <TabsContent value="details" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Detailed Information</CardTitle>
                <CardDescription>
                  Complete company details and contact information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={profileForm.handleSubmit(handleProfileSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="name">Company Name *</Label>
                        <Input
                          id="name"
                          {...profileForm.register('name')}
                          disabled={!isEditing}
                          className="mt-1"
                        />
                        {profileForm.formState.errors.name && (
                          <p className="text-sm text-red-500 mt-1">
                            {profileForm.formState.errors.name.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="tagline">Tagline</Label>
                        <Input
                          id="tagline"
                          {...profileForm.register('tagline')}
                          disabled={!isEditing}
                          placeholder="A brief company tagline"
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor="website">Website</Label>
                        <Input
                          id="website"
                          {...profileForm.register('website')}
                          disabled={!isEditing}
                          placeholder="https://company.com"
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor="size">Company Size</Label>
                        <Select
                          value={profileForm.watch('size')}
                          onValueChange={(value) => profileForm.setValue('size', value as any)}
                          disabled={!isEditing}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Select company size" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="startup">Startup (1-10)</SelectItem>
                            <SelectItem value="small">Small (11-50)</SelectItem>
                            <SelectItem value="medium">Medium (51-200)</SelectItem>
                            <SelectItem value="large">Large (201-1000)</SelectItem>
                            <SelectItem value="enterprise">Enterprise (1000+)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="founded">Founded Year</Label>
                        <Input
                          id="founded"
                          type="number"
                          {...profileForm.register('founded', { valueAsNumber: true })}
                          disabled={!isEditing}
                          placeholder="2020"
                          className="mt-1"
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="contact.email">Contact Email *</Label>
                        <Input
                          id="contact.email"
                          type="email"
                          {...profileForm.register('contact.email')}
                          disabled={!isEditing}
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor="contact.phone">Phone Number</Label>
                        <Input
                          id="contact.phone"
                          {...profileForm.register('contact.phone')}
                          disabled={!isEditing}
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor="contact.supportEmail">Support Email</Label>
                        <Input
                          id="contact.supportEmail"
                          type="email"
                          {...profileForm.register('contact.supportEmail')}
                          disabled={!isEditing}
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor="contact.hrEmail">HR Email</Label>
                        <Input
                          id="contact.hrEmail"
                          type="email"
                          {...profileForm.register('contact.hrEmail')}
                          disabled={!isEditing}
                          className="mt-1"
                        />
                      </div>

                      <div>
                        <Label htmlFor="contact.address">Address</Label>
                        <Textarea
                          id="contact.address"
                          {...profileForm.register('contact.address')}
                          disabled={!isEditing}
                          className="mt-1"
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">Company Description *</Label>
                    <Textarea
                      id="description"
                      {...profileForm.register('description')}
                      disabled={!isEditing}
                      placeholder="Describe your company, its mission, and what makes it unique..."
                      className="mt-1"
                      rows={4}
                    />
                    {profileForm.formState.errors.description && (
                      <p className="text-sm text-red-500 mt-1">
                        {profileForm.formState.errors.description.message}
                      </p>
                    )}
                  </div>

                  {isEditing && (
                    <div className="flex justify-end space-x-2">
                      <Button type="button" variant="outline" onClick={() => setIsEditing(false)}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={updateLoading}>
                        {updateLoading ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </div>
                  )}
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Additional tabs will continue in next part */}
        </Tabs>
      </div>
    </div>
  )
}
