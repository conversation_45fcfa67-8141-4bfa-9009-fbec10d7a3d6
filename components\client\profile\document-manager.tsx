"use client"

import React, { useState, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useClientStore } from "@/stores/client.store"
import { 
  Upload, 
  FileText, 
  Download, 
  Trash2, 
  Eye,
  Star,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react"

interface DocumentManagerProps {
  className?: string
}

export function DocumentManager({ className }: DocumentManagerProps) {
  const { client, updateClientProfile, updateLoading } = useClientStore()
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const documents = client?.documents || {
    resume: [],
    coverLetter: [],
    portfolio: []
  }

  const handleFileUpload = async (file: File, type: 'resume' | 'coverLetter' | 'portfolio') => {
    if (!file) return

    // Validate file type
    const allowedTypes = {
      resume: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      coverLetter: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      portfolio: ['application/pdf', 'image/jpeg', 'image/png', 'image/gif']
    }

    if (!allowedTypes[type].includes(file.type)) {
      alert('Invalid file type. Please upload a PDF or Word document.')
      return
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB')
      return
    }

    setUploading(true)
    setUploadProgress(0)

    try {
      // Create FormData
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', type)

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      // Upload file
      const response = await fetch('/api/v1/clients/documents/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()

      // Update client documents
      const updatedDocuments = { ...documents }
      updatedDocuments[type] = updatedDocuments[type] || []
      updatedDocuments[type].push({
        filename: file.name,
        url: result.data.url,
        uploadDate: new Date(),
        fileSize: file.size,
        isPrimary: updatedDocuments[type].length === 0 // First document is primary
      })

      await updateClientProfile({ documents: updatedDocuments })

      setTimeout(() => {
        setUploading(false)
        setUploadProgress(0)
      }, 1000)

    } catch (error) {
      console.error('Upload error:', error)
      alert('Upload failed. Please try again.')
      setUploading(false)
      setUploadProgress(0)
    }
  }

  const handleFileSelect = (type: 'resume' | 'coverLetter' | 'portfolio') => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = type === 'portfolio' ? '.pdf,.jpg,.jpeg,.png,.gif' : '.pdf,.doc,.docx'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        handleFileUpload(file, type)
      }
    }
    input.click()
  }

  const deleteDocument = async (type: 'resume' | 'coverLetter' | 'portfolio', index: number) => {
    const updatedDocuments = { ...documents }
    updatedDocuments[type].splice(index, 1)
    await updateClientProfile({ documents: updatedDocuments })
  }

  const setPrimaryDocument = async (type: 'resume' | 'coverLetter' | 'portfolio', index: number) => {
    const updatedDocuments = { ...documents }
    updatedDocuments[type].forEach((doc, i) => {
      doc.isPrimary = i === index
    })
    await updateClientProfile({ documents: updatedDocuments })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase()
    return <FileText className="w-5 h-5" />
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Progress */}
      {uploading && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Uploading...</span>
                <span className="text-sm text-muted-foreground">{uploadProgress}%</span>
              </div>
              <Progress value={uploadProgress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Resume Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Resume
              </CardTitle>
              <CardDescription>
                Upload your resume in PDF or Word format
              </CardDescription>
            </div>
            <Button 
              onClick={() => handleFileSelect('resume')}
              disabled={uploading}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Resume
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {documents.resume && documents.resume.length > 0 ? (
            <div className="space-y-3">
              {documents.resume.map((doc, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getFileIcon(doc.filename)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{doc.filename}</p>
                        {doc.isPrimary && (
                          <Badge variant="default" className="text-xs">
                            <Star className="w-3 h-3 mr-1" />
                            Primary
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {formatFileSize(doc.fileSize)} • Uploaded {new Date(doc.uploadDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                    {!doc.isPrimary && (
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setPrimaryDocument('resume', index)}
                      >
                        <Star className="w-4 h-4" />
                      </Button>
                    )}
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => deleteDocument('resume', index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No resume uploaded</p>
              <p className="text-sm">Upload your resume to get started</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cover Letter Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Cover Letters
              </CardTitle>
              <CardDescription>
                Upload cover letter templates
              </CardDescription>
            </div>
            <Button 
              onClick={() => handleFileSelect('coverLetter')}
              disabled={uploading}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Cover Letter
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {documents.coverLetter && documents.coverLetter.length > 0 ? (
            <div className="space-y-3">
              {documents.coverLetter.map((doc, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getFileIcon(doc.filename)}
                    <div>
                      <p className="font-medium">{doc.filename}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatFileSize(doc.fileSize)} • Uploaded {new Date(doc.uploadDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => deleteDocument('coverLetter', index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No cover letters uploaded</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Portfolio Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Portfolio
              </CardTitle>
              <CardDescription>
                Upload portfolio documents and images
              </CardDescription>
            </div>
            <Button 
              onClick={() => handleFileSelect('portfolio')}
              disabled={uploading}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Portfolio
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {documents.portfolio && documents.portfolio.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {documents.portfolio.map((doc, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getFileIcon(doc.filename)}
                    <div>
                      <p className="font-medium text-sm">{doc.filename}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(doc.fileSize)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button variant="ghost" size="sm">
                      <Eye className="w-3 h-3" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => deleteDocument('portfolio', index)}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No portfolio items uploaded</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Document Tips */}
      <Card className="bg-muted/50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5" />
            <div className="space-y-1">
              <h4 className="font-medium">Document Tips</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Keep file sizes under 5MB for faster uploads</li>
                <li>• Use PDF format for best compatibility</li>
                <li>• Name your files descriptively (e.g., "John_Doe_Resume_2024.pdf")</li>
                <li>• Your primary resume will be used for quick applications</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
