'use client'

import React from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { useAuthStore } from '@/stores'
import { JobPostingForm } from '@/components/jobs/job-posting-form'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Briefcase, Users, Target, TrendingUp } from 'lucide-react'

function JobCreateContent() {
  const router = useRouter()
  const { user } = useAuthStore()

  // Handle successful creation
  const handleSuccess = (job: any) => {
    router.push(`/jobs/${job._id}`)
  }

  // Handle cancel
  const handleCancel = () => {
    router.push('/recruiter')
  }

  // Check if user has a company
  if (!user?.companyId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <CardTitle>Company Profile Required</CardTitle>
                <CardDescription>
                  You need to create a company profile before posting jobs.
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <p className="text-muted-foreground">
                  To post jobs and attract candidates, you'll need to set up your company profile first. 
                  This helps candidates learn about your company and makes your job postings more attractive.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button onClick={() => router.push('/company/create')}>
                    Create Company Profile
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/company-dashboard')}
                  >
                    Go to Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Post a New Job</h1>
              <p className="text-muted-foreground">
                Create a compelling job posting to attract top talent
              </p>
            </div>
            
            <Button
              variant="outline"
              onClick={() => router.push('/recruiter')}
            >
              Cancel
            </Button>
          </div>
        </div>
      </header>

      {/* Welcome Section */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card className="mb-8">
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Briefcase className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <CardTitle>Create Your Job Posting</CardTitle>
                  <CardDescription>
                    Attract the right candidates with a detailed and compelling job description
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-blue-600 font-bold text-sm">1</span>
                  </div>
                  <h4 className="font-medium mb-1">Job Details</h4>
                  <p className="text-sm text-muted-foreground">
                    Add title, description, and requirements
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-green-600 font-bold text-sm">2</span>
                  </div>
                  <h4 className="font-medium mb-1">Attract Candidates</h4>
                  <p className="text-sm text-muted-foreground">
                    Your job will be visible to thousands of job seekers
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <span className="text-purple-600 font-bold text-sm">3</span>
                  </div>
                  <h4 className="font-medium mb-1">Review Applications</h4>
                  <p className="text-sm text-muted-foreground">
                    Manage applications from your recruiter dashboard
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tips Card */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5" />
                <span>Tips for a Great Job Posting</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-sm">Clear Job Title</p>
                      <p className="text-xs text-muted-foreground">Use specific, searchable job titles</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-sm">Detailed Description</p>
                      <p className="text-xs text-muted-foreground">Include responsibilities and company culture</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-sm">Competitive Salary</p>
                      <p className="text-xs text-muted-foreground">Include salary range to attract more candidates</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-sm">Benefits & Perks</p>
                      <p className="text-xs text-muted-foreground">Highlight what makes your company special</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Job Form */}
          <JobPostingForm
            mode="create"
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </div>
      </div>
    </div>
  )
}

export default function JobCreatePage() {
  return (
    <ProtectedRoute requiredRole="recruiter">
      <JobCreateContent />
    </ProtectedRoute>
  )
}
