import { NextRequest } from 'next/server'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { errorService, ErrorCode } from '@/lib/error-service'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  validate<PERSON><PERSON><PERSON>, 
  validateRequestBody,
  createSuccessResponse 
} from '@/lib/middleware/api-middleware'
import { updateCompanyProfileSchema } from '@/types/company-management.types'
import { connectToDatabase } from '@/lib/database/connection'

// GET /api/v1/companies/me - Get current user's company
export const GET = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['GET'])
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  await connectToDatabase()

  // Find user and their company
  const user = await User.findById(userId).populate('companyId')

  if (!user) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'User not found',
      'userId'
    )
  }

  // If user has no company, return null
  if (!user.companyId) {
    return createSuccessResponse(null, 200, 'No company associated with user')
  }

  // Get full company details
  const company = await Company.findById(user.companyId)
    .populate('admins', 'name email avatar')
    .populate('recruiters', 'name email avatar')
    .populate('teamMembers.user', 'name email avatar')
    .lean()

  if (!company) {
    return createSuccessResponse(null, 200, 'Company not found')
  }

  return createSuccessResponse(company, 200, 'Company profile retrieved successfully')
}, {
  requireDatabase: true,
  requireAuth: true
})

// POST /api/v1/companies/me - Create company for current user
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  await connectToDatabase()

  // Check if user already has a company
  const user = await User.findById(userId)
  
  if (!user) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'User not found',
      'userId'
    )
  }

  if (user.companyId) {
    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'User already has a company associated',
      'company'
    )
  }

  // Validate request body
  const companyData = await validateRequestBody(request, updateCompanyProfileSchema)
  
  // Create company slug from name
  const slug = companyData.name
    ?.toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '') || 'company'

  // Create new company
  const company = new Company({
    name: companyData.name,
    slug: slug,
    description: companyData.description || '',
    tagline: companyData.tagline || '',
    website: companyData.website || '',
    industry: companyData.industry || [],
    size: companyData.size || 'startup',
    founded: companyData.founded || new Date().getFullYear(),
    locations: [],
    contact: {
      email: companyData.contact?.email || user.email,
      phone: companyData.contact?.phone || '',
      address: companyData.contact?.address || '',
      supportEmail: companyData.contact?.supportEmail || '',
      hrEmail: companyData.contact?.hrEmail || ''
    },
    culture: {
      values: [],
      benefits: [],
      workEnvironment: '',
      diversity: '',
      mission: '',
      vision: '',
      perks: []
    },
    socialLinks: {
      linkedin: '',
      twitter: '',
      facebook: '',
      instagram: '',
      github: '',
      youtube: '',
      glassdoor: ''
    },
    subscription: {
      plan: 'starter',
      status: 'trial',
      jobPostingLimit: 5,
      jobPostingsUsed: 0,
      featuredJobsLimit: 1,
      featuredJobsUsed: 0
    },
    admins: [userId],
    recruiters: [],
    teamMembers: [{
      user: userId,
      role: 'owner',
      department: 'Management',
      joinedAt: new Date(),
      isActive: true
    }],
    stats: {
      totalJobs: 0,
      activeJobs: 0,
      totalApplications: 0,
      totalHires: 0,
      profileViews: 0,
      followerCount: 0
    },
    verification: {
      isVerified: false,
      status: 'pending'
    },
    settings: {
      allowPublicProfile: true,
      showSalaryRanges: true,
      allowDirectContact: true,
      showCompanyStats: false,
      autoRejectAfterDays: 30,
      requireCoverLetter: false,
      enableApplicationTracking: true,
      emailNotifications: {
        newApplications: true,
        applicationUpdates: true,
        interviewReminders: true,
        jobExpiring: true,
        weeklyReports: false,
        marketingEmails: false
      },
      pushNotifications: {
        newApplications: true,
        applicationUpdates: false,
        interviewReminders: true,
        systemUpdates: true
      },
      branding: {
        primaryColor: '#000000',
        secondaryColor: '#ffffff',
        logoUrl: '',
        coverImageUrl: '',
        customCss: ''
      },
      team: {
        allowMemberInvites: true,
        requireApprovalForInvites: true,
        defaultMemberRole: 'recruiter',
        maxTeamMembers: 10
      },
      integrations: {
        atsEnabled: false,
        atsProvider: '',
        atsApiKey: '',
        slackWebhookUrl: '',
        googleAnalyticsId: '',
        linkedinCompanyId: ''
      },
      advanced: {
        customApplicationFields: [],
        autoResponseEnabled: true,
        autoResponseMessage: 'Thank you for your application. We will review it and get back to you soon.',
        applicationDeadlineReminder: true,
        bulkActionsEnabled: true
      }
    },
    isActive: true,
    isFeatured: false
  })

  await company.save()

  // Update user with company reference
  user.companyId = company._id
  user.role = 'company_admin'
  await user.save()

  // Return the created company
  const populatedCompany = await Company.findById(company._id)
    .populate('admins', 'name email avatar')
    .populate('recruiters', 'name email avatar')
    .populate('teamMembers.user', 'name email avatar')
    .lean()

  return createSuccessResponse(populatedCompany, 201, 'Company created successfully')
}, {
  requireDatabase: true,
  requireAuth: true
})

// PUT /api/v1/companies/me - Update current user's company
export const PUT = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['PUT'])
  
  // Get user ID from auth context
  const userId = request.headers.get('x-user-id')
  if (!userId) {
    throw errorService.createError(
      ErrorCode.UNAUTHORIZED,
      'Authentication required',
      'auth'
    )
  }

  await connectToDatabase()

  // Find user and their company
  const user = await User.findById(userId)

  if (!user || !user.companyId) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'No company associated with user',
      'company'
    )
  }

  const company = await Company.findById(user.companyId)
  
  if (!company) {
    throw errorService.createError(
      ErrorCode.NOT_FOUND,
      'Company not found',
      'company'
    )
  }

  // Check if user is admin
  const isAdmin = company.admins.some((adminId: unknown) => {
    if (adminId && typeof adminId === 'object' && 'equals' in adminId) {
      return (adminId as { equals: (id: unknown) => boolean }).equals(userId)
    }
    return false
  })

  if (!isAdmin) {
    throw errorService.createError(
      ErrorCode.FORBIDDEN,
      'Insufficient permissions to update company',
      'permissions'
    )
  }

  // Validate request body
  const updateData = await validateRequestBody(request, updateCompanyProfileSchema.partial())
  
  // Update company
  Object.assign(company, updateData)
  company.updatedAt = new Date()
  await company.save()

  // Return updated company
  const updatedCompany = await Company.findById(company._id)
    .populate('admins', 'name email avatar')
    .populate('recruiters', 'name email avatar')
    .populate('teamMembers.user', 'name email avatar')
    .lean()

  return createSuccessResponse(updatedCompany, 200, 'Company updated successfully')
}, {
  requireDatabase: true,
  requireAuth: true
})

// Method not allowed for other HTTP methods
export async function DELETE() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'DELETE method not allowed for company profiles.'
  )
}

export async function PATCH() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'PATCH method not allowed. Use PUT for company updates.'
  )
}

export async function HEAD() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'HEAD method not allowed.'
  )
}

export async function OPTIONS() {
  throw errorService.createError(
    ErrorCode.METHOD_NOT_ALLOWED,
    'OPTIONS method not allowed.'
  )
}
