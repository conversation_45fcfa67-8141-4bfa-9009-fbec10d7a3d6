import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { connectDB } from '@/lib/database/connection'
import { User } from '@/lib/models/user.model'
import { generateTokens } from '@/lib/auth/middleware'
import { ApiResponse, AuthResponse, AuthUser, DatabaseError } from '@/lib/types/api.types'

interface RefreshTokenRequest {
  refreshToken: string
}

interface RefreshTokenPayload {
  userId: string
  type: 'refresh'
  iat: number
  exp: number
}

export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<AuthResponse>>> {
  try {
    await connectDB()

    let body: RefreshTokenRequest
    try {
      body = await request.json()
    } catch {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_REQUEST_BODY',
            message: 'Invalid JSON in request body',
            statusCode: 400
          },
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    const { refreshToken } = body

    if (!refreshToken) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'MISSING_REFRESH_TOKEN',
            message: 'Refresh token is required',
            field: 'refreshToken',
            statusCode: 400
          },
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      )
    }

    // Verify refresh token
    let decoded: RefreshTokenPayload
    try {
      decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET!) as RefreshTokenPayload
    } catch (jwtError) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_REFRESH_TOKEN',
            message: 'Invalid or expired refresh token',
            statusCode: 401
          },
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      )
    }

    // Get user from database
    const user = await User.findById(decoded.userId)
      .populate('companyId', 'name slug')
      .select('-password')
      .lean()

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            statusCode: 404
          },
          timestamp: new Date().toISOString()
        },
        { status: 404 }
      )
    }

    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ACCOUNT_DEACTIVATED',
            message: 'Account is deactivated',
            statusCode: 401
          },
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      )
    }

    // Generate new tokens
    const tokens = generateTokens(user)

    // Format user data
    const userData: AuthUser = {
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      profile: user.profile || {
        firstName: '',
        lastName: '',
        fullName: ''
      },
      preferences: user.preferences || {
        emailNotifications: true,
        jobAlerts: true,
        marketingEmails: false,
        theme: 'system',
        language: 'en',
        timezone: 'UTC',
        remoteWork: false
      },
      companyId: user.companyId?._id?.toString(),
      isEmailVerified: user.isEmailVerified || false,
      isActive: user.isActive || true,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }

    const authResponse: AuthResponse = {
      user: userData,
      token: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      expiresIn: 3600 // 1 hour
    }

    return NextResponse.json(
      {
        success: true,
        data: authResponse,
        message: 'Token refreshed successfully',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Token refresh error:', error)
    
    const dbError = error as DatabaseError
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to refresh token',
          statusCode: 500,
          details: process.env.NODE_ENV === 'development' ? { originalError: dbError.message } : undefined
        },
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Method not allowed for other HTTP methods
export async function GET(): Promise<NextResponse<ApiResponse>> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: 'GET method not allowed. Use POST to refresh tokens.',
        statusCode: 405
      },
      timestamp: new Date().toISOString()
    },
    { status: 405 }
  )
}
