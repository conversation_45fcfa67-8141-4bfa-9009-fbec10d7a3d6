// lib/models/notification.model.ts
import mongoose, { Schema, Document } from 'mongoose'

export interface INotification extends Document {
  userId: mongoose.Types.ObjectId
  type: 'application_status' | 'new_application' | 'job_posted' | 'job_expired' | 'message' | 'system'
  title: string
  message: string
  data?: {
    jobId?: string
    applicationId?: string
    companyId?: string
    url?: string
    [key: string]: unknown
  }
  isRead: boolean
  readAt?: Date
  priority: 'low' | 'medium' | 'high'
  expiresAt?: Date
  createdAt: Date
  updatedAt: Date
}

const notificationSchema = new Schema<INotification>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  type: {
    type: String,
    enum: ['application_status', 'new_application', 'job_posted', 'job_expired', 'message', 'system'],
    required: [true, 'Notification type is required']
  },
  title: {
    type: String,
    required: [true, 'Notification title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  message: {
    type: String,
    required: [true, 'Notification message is required'],
    trim: true,
    maxlength: [1000, 'Message cannot exceed 1000 characters']
  },
  data: {
    type: Schema.Types.Mixed,
    default: {}
  },
  isRead: {
    type: Boolean,
    default: false
  },
  readAt: {
    type: Date
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  expiresAt: {
    type: Date,
    validate: {
      validator: function(value: Date) {
        return !value || value > new Date()
      },
      message: 'Expiration date must be in the future'
    }
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(_, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for better query performance
notificationSchema.index({ userId: 1, isRead: 1 })
notificationSchema.index({ userId: 1, createdAt: -1 })
notificationSchema.index({ type: 1, createdAt: -1 })
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }) // Auto-delete expired notifications
notificationSchema.index({ priority: 1, isRead: 1 })

// Virtual for checking if notification is expired
notificationSchema.virtual('isExpired').get(function(this: INotification) {
  return this.expiresAt ? this.expiresAt < new Date() : false
})

// Pre-save middleware to set readAt when isRead changes to true
notificationSchema.pre('save', function(this: INotification) {
  if (this.isModified('isRead') && this.isRead && !this.readAt) {
    this.readAt = new Date()
  }
})

// Static methods
notificationSchema.statics.findUnreadByUser = function(userId: string) {
  return this.find({ userId, isRead: false }).sort({ createdAt: -1 })
}

notificationSchema.statics.findByUserAndType = function(userId: string, type: string) {
  return this.find({ userId, type }).sort({ createdAt: -1 })
}

notificationSchema.statics.markAllAsRead = function(userId: string) {
  return this.updateMany(
    { userId, isRead: false },
    { isRead: true, readAt: new Date() }
  )
}

notificationSchema.statics.deleteExpired = function() {
  return this.deleteMany({
    expiresAt: { $lt: new Date() }
  })
}

// Instance methods
notificationSchema.methods.markAsRead = function() {
  this.isRead = true
  this.readAt = new Date()
  return this.save()
}

notificationSchema.methods.markAsUnread = function() {
  this.isRead = false
  this.readAt = undefined
  return this.save()
}

export const Notification = mongoose.models.Notification || mongoose.model<INotification>('Notification', notificationSchema)
