import { create } from 'zustand'
import { errorService, type AppError } from '@/lib/error-service'

// Types
export interface Job {
  _id: string
  title: string
  description: string
  company: {
    _id: string
    name: string
    logo?: string
    location: string
  }
  location: {
    city: string
    state: string
    country: string
    remote: boolean
    hybrid: boolean
  }
  salary: {
    min?: number
    max?: number
    currency: string
    period: 'hourly' | 'monthly' | 'yearly'
  }
  requirements: {
    experience: string
    education: string
    skills: string[]
  }
  benefits: string[]
  type: 'full-time' | 'part-time' | 'contract' | 'internship'
  status: 'active' | 'paused' | 'closed'
  applicationDeadline?: Date
  postedAt: Date
  updatedAt: Date
  applicationsCount: number
  viewsCount: number
}

export interface JobSearchQuery {
  q?: string
  location?: string
  jobType?: string[]
  salaryMin?: number
  salaryMax?: number
  experience?: string
  remote?: boolean
  page?: number
  limit?: number
  sortBy?: 'relevance' | 'date' | 'salary'
  sortOrder?: 'asc' | 'desc'
}

export interface JobFilters {
  jobTypes: string[]
  experienceLevels: string[]
  salaryRange: [number, number]
  locations: string[]
  companies: string[]
  remote: boolean
  datePosted: 'any' | '24h' | '7d' | '30d'
}

export interface PaginationData {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface SearchMeta {
  searchTime: number
  totalResults: number
  query: string
}

export interface ApplicationData {
  coverLetter?: string
  resumeId?: string
  customFields?: Record<string, unknown>
}

interface JobsState {
  jobs: Job[]
  currentJob: Job | null
  searchQuery: JobSearchQuery
  filters: Partial<JobFilters>
  savedJobs: string[]
  appliedJobs: string[]
  recommendations: Job[]

  // Loading states
  searchLoading: boolean
  jobLoading: boolean
  applyLoading: boolean
  saveLoading: boolean
  recommendationsLoading: boolean

  // Error states
  error: AppError | null
  searchError: AppError | null

  // Meta
  pagination: PaginationData
  searchMeta: SearchMeta
}

interface JobsActions {
  searchJobs: (query: JobSearchQuery) => Promise<void>
  getJobById: (jobId: string) => Promise<void>
  applyToJob: (jobId: string, applicationData: ApplicationData) => Promise<void>
  saveJob: (jobId: string) => Promise<void>
  unsaveJob: (jobId: string) => Promise<void>
  getRecommendations: () => Promise<void>
  getSavedJobs: () => Promise<void>
  getAppliedJobs: () => Promise<void>
  updateFilters: (filters: Partial<JobFilters>) => void
  updateSearchQuery: (query: Partial<JobSearchQuery>) => void
  clearSearch: () => void
  clearError: () => void
  clearSearchError: () => void
}

// API Service functions
const JobsAPI = {
  async searchJobs(query: JobSearchQuery) {
    const params = new URLSearchParams()
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()))
        } else {
          params.append(key, value.toString())
        }
      }
    })

    const response = await fetch(`/api/v1/jobs/search?${params}`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Search failed')
    }
    return response.json()
  },

  async getJobById(jobId: string) {
    const response = await fetch(`/api/v1/jobs/${jobId}`)
    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to fetch job')
    }
    return response.json()
  },

  async applyToJob(jobId: string, applicationData: ApplicationData, token: string) {
    const response = await fetch(`/api/v1/jobs/${jobId}/apply`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(applicationData)
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Application failed')
    }
    return response.json()
  },

  async saveJob(jobId: string, token: string) {
    const response = await fetch(`/api/v1/jobs/${jobId}/save`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to save job')
    }
    return response.json()
  },

  async unsaveJob(jobId: string, token: string) {
    const response = await fetch(`/api/v1/jobs/${jobId}/save`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to unsave job')
    }
    return response.json()
  },

  async getRecommendations(token: string) {
    const response = await fetch('/api/v1/jobs/recommendations', {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to get recommendations')
    }
    return response.json()
  },

  async getSavedJobs(token: string) {
    const response = await fetch('/api/v1/jobs/saved', {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to get saved jobs')
    }
    return response.json()
  },

  async getAppliedJobs(token: string) {
    const response = await fetch('/api/v1/jobs/applied', {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.message || 'Failed to get applied jobs')
    }
    return response.json()
  }
}

export const useJobsStore = create<JobsState & JobsActions>((set) => ({
  // State
  jobs: [],
  currentJob: null,
  searchQuery: { page: 1, limit: 20 },
  filters: {},
  savedJobs: [],
  appliedJobs: [],
  recommendations: [],

  // Loading states
  searchLoading: false,
  jobLoading: false,
  applyLoading: false,
  saveLoading: false,
  recommendationsLoading: false,

  // Error states
  error: null,
  searchError: null,

  // Meta
  pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
  searchMeta: { searchTime: 0, totalResults: 0, query: '' },

  // Actions
  searchJobs: async (query) => {
    set({ searchLoading: true, searchError: null })
    const startTime = Date.now()
    
    try {
      const response = await JobsAPI.searchJobs(query)
      const searchTime = Date.now() - startTime
      
      set({
        jobs: response.jobs,
        pagination: response.pagination,
        searchQuery: query,
        searchMeta: {
          searchTime,
          totalResults: response.pagination.total,
          query: query.q || ''
        },
        searchLoading: false
      })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'jobs_search')
      set({ 
        searchError: appError, 
        searchLoading: false 
      })
      throw appError
    }
  },

  getJobById: async (jobId) => {
    set({ jobLoading: true, error: null })
    try {
      const job = await JobsAPI.getJobById(jobId)
      set({
        currentJob: job,
        jobLoading: false
      })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'jobs_get_by_id')
      set({
        error: appError,
        jobLoading: false
      })
      throw appError
    }
  },

  applyToJob: async (jobId, applicationData) => {
    // This would need access to auth token - in a real app you'd get this from auth store
    const token = localStorage.getItem('auth-token') // Simplified for now
    if (!token) {
      throw new Error('Authentication required')
    }

    set({ applyLoading: true, error: null })
    try {
      await JobsAPI.applyToJob(jobId, applicationData, token)
      
      // Add to applied jobs
      set(state => ({
        appliedJobs: [...state.appliedJobs, jobId],
        applyLoading: false
      }))
    } catch (error) {
      const appError = errorService.logError(error as Error, 'jobs_apply')
      set({
        error: appError,
        applyLoading: false
      })
      throw appError
    }
  },

  saveJob: async (jobId) => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }

    set({ saveLoading: true, error: null })
    try {
      await JobsAPI.saveJob(jobId, token)
      
      set(state => ({
        savedJobs: [...state.savedJobs, jobId],
        saveLoading: false
      }))
    } catch (error) {
      const appError = errorService.logError(error as Error, 'jobs_save')
      set({
        error: appError,
        saveLoading: false
      })
      throw appError
    }
  },

  unsaveJob: async (jobId) => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }

    set({ saveLoading: true, error: null })
    try {
      await JobsAPI.unsaveJob(jobId, token)
      
      set(state => ({
        savedJobs: state.savedJobs.filter(id => id !== jobId),
        saveLoading: false
      }))
    } catch (error) {
      const appError = errorService.logError(error as Error, 'jobs_unsave')
      set({
        error: appError,
        saveLoading: false
      })
      throw appError
    }
  },

  getRecommendations: async () => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      return // Skip if not authenticated
    }

    set({ recommendationsLoading: true, error: null })
    try {
      const recommendations = await JobsAPI.getRecommendations(token)
      set({
        recommendations,
        recommendationsLoading: false
      })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'jobs_recommendations')
      set({
        error: appError,
        recommendationsLoading: false
      })
    }
  },

  getSavedJobs: async () => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }

    try {
      const savedJobs = await JobsAPI.getSavedJobs(token)
      set({ savedJobs: savedJobs.map((job: Job) => job._id) })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'jobs_get_saved')
      set({ error: appError })
    }
  },

  getAppliedJobs: async () => {
    const token = localStorage.getItem('auth-token')
    if (!token) {
      throw new Error('Authentication required')
    }

    try {
      const appliedJobs = await JobsAPI.getAppliedJobs(token)
      set({ appliedJobs: appliedJobs.map((job: Job) => job._id) })
    } catch (error) {
      const appError = errorService.logError(error as Error, 'jobs_get_applied')
      set({ error: appError })
    }
  },

  updateFilters: (filters) => {
    set(state => ({
      filters: { ...state.filters, ...filters }
    }))
  },

  updateSearchQuery: (query) => {
    set(state => ({
      searchQuery: { ...state.searchQuery, ...query }
    }))
  },

  clearSearch: () => {
    set({
      jobs: [],
      searchQuery: { page: 1, limit: 20 },
      filters: {},
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      searchMeta: { searchTime: 0, totalResults: 0, query: '' }
    })
  },

  clearError: () => set({ error: null }),
  clearSearchError: () => set({ searchError: null })
}))
