import { NextRequest, NextResponse } from 'next/server'
import { ZodSchema } from 'zod'
import { errorService, ErrorCode } from '@/lib/error-service'
import { connectToDatabase } from '@/lib/database/connection'
import { verifyToken } from './auth.middleware'

// Types for middleware options
interface ErrorHandlerOptions {
  requireDatabase?: boolean
  requireAuth?: boolean
  requiredRoles?: string[]
}

// Type for API handler function
type <PERSON><PERSON><PERSON><PERSON><PERSON> = (
  request: NextRequest,
  context: { params: Record<string, string> }
) => Promise<NextResponse>

/**
 * Higher-order function to wrap API handlers with error handling
 */
export function with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
  handler: <PERSON><PERSON><PERSON><PERSON><PERSON>,
  options: ErrorHandlerOptions = {}
): A<PERSON><PERSON><PERSON><PERSON> {
  return async (request: NextRequest, context: { params: Record<string, string> }) => {
    try {
      // Connect to database if required
      if (options.requireDatabase) {
        await connectToDatabase()
      }

      // Check authentication if required
      if (options.requireAuth) {
        // Development mode bypass for testing
        if (process.env.NODE_ENV === 'development' && process.env.BYPASS_AUTH === 'true') {
          const headers = new Headers(request.headers)
          headers.set('x-user-id', process.env.DEV_USER_ID || '507f1f77bcf86cd799439011')
          headers.set('x-user-role', 'company_admin')
          headers.set('x-user-email', '<EMAIL>')

          const authenticatedRequest = new NextRequest(request.url, {
            method: request.method,
            headers: headers,
            body: request.body
          })

          Object.assign(request, authenticatedRequest)
        } else {
          const authHeader = request.headers.get('authorization')
          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw errorService.createError(
              ErrorCode.UNAUTHORIZED,
              'Authentication required',
              'auth'
            )
          }

          // Extract and verify JWT token
          const token = authHeader.substring(7) // Remove 'Bearer ' prefix
          try {
            const payload = verifyToken(token)

            // Create a new request with user information in headers
            const headers = new Headers(request.headers)
            headers.set('x-user-id', payload.userId || payload.id || '')
            headers.set('x-user-role', payload.role || 'user')
            headers.set('x-user-email', payload.email || '')

            // Create new request with updated headers
            const authenticatedRequest = new NextRequest(request.url, {
              method: request.method,
              headers: headers,
              body: request.body
            })

            // Replace the original request
            Object.assign(request, authenticatedRequest)
          } catch (error) {
            throw errorService.createError(
              ErrorCode.UNAUTHORIZED,
              'Invalid or expired token',
              'auth'
            )
          }
        }
      }

      // Check roles if required
      if (options.requiredRoles && options.requiredRoles.length > 0) {
        const userRole = request.headers.get('x-user-role')
        if (!userRole || !options.requiredRoles.includes(userRole)) {
          throw errorService.createError(
            ErrorCode.FORBIDDEN,
            `Access denied. Required roles: ${options.requiredRoles.join(', ')}`,
            'authorization'
          )
        }
      }

      // Execute the handler
      return await handler(request, context)

    } catch (error) {
      console.error('API Error:', error)

      // Handle known application errors
      if (typeof error === 'object' && error !== null && 'code' in error) {
        const appError = error as {
          code: string
          message: string
          field?: string
          statusCode?: number
        }

        return NextResponse.json(
          {
            success: false,
            error: {
              code: appError.code,
              message: appError.message,
              field: appError.field
            }
          },
          { status: appError.statusCode || 500 }
        )
      }

      // Handle unknown errors
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INTERNAL_SERVER_ERROR',
            message: 'An unexpected error occurred'
          }
        },
        { status: 500 }
      )
    }
  }
}

/**
 * Validate HTTP method
 */
export function validateMethod(request: NextRequest, allowedMethods: string[]): void {
  if (!allowedMethods.includes(request.method)) {
    throw errorService.createError(
      ErrorCode.METHOD_NOT_ALLOWED,
      `Method ${request.method} not allowed. Allowed methods: ${allowedMethods.join(', ')}`,
      'method'
    )
  }
}

/**
 * Validate request body against a Zod schema
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json()
    return schema.parse(body)
  } catch (error) {
    if (error instanceof Error) {
      // Handle Zod validation errors
      if (error.name === 'ZodError') {
        const zodError = error as unknown as { 
          errors: Array<{ path: string[], message: string }> 
        }
        const firstError = zodError.errors[0]
        const path = firstError.path.join('.')
        
        throw errorService.createError(
          ErrorCode.VALIDATION_ERROR,
          firstError.message,
          path
        )
      }

      // Handle JSON parsing errors
      if (error.message.includes('JSON')) {
        throw errorService.createError(
          ErrorCode.BAD_REQUEST,
          'Invalid JSON in request body',
          'body'
        )
      }
    }

    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Invalid request body',
      'body'
    )
  }
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T>(
  data: T,
  status: number = 200,
  message?: string
): NextResponse {
  return NextResponse.json(
    {
      success: true,
      data,
      message
    },
    { status }
  )
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  code: string,
  message: string,
  status: number = 400,
  field?: string
): NextResponse {
  return NextResponse.json(
    {
      success: false,
      error: {
        code,
        message,
        field
      }
    },
    { status }
  )
}

/**
 * Extract pagination parameters from request
 */
export function extractPaginationParams(request: NextRequest): {
  page: number
  limit: number
  offset: number
} {
  const { searchParams } = new URL(request.url)
  
  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10))
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10', 10)))
  const offset = (page - 1) * limit

  return { page, limit, offset }
}

/**
 * Extract sorting parameters from request
 */
export function extractSortParams(request: NextRequest): {
  sortBy: string
  sortOrder: 'asc' | 'desc'
} {
  const { searchParams } = new URL(request.url)
  
  const sortBy = searchParams.get('sortBy') || 'createdAt'
  const sortOrder = (searchParams.get('sortOrder') === 'desc') ? 'desc' : 'asc'

  return { sortBy, sortOrder }
}

/**
 * Extract filter parameters from request
 */
export function extractFilterParams(request: NextRequest): Record<string, string> {
  const { searchParams } = new URL(request.url)
  const filters: Record<string, string> = {}

  // Extract all query parameters except pagination and sorting
  const excludeParams = ['page', 'limit', 'sortBy', 'sortOrder']
  
  searchParams.forEach((value, key) => {
    if (!excludeParams.includes(key)) {
      filters[key] = value
    }
  })

  return filters
}

/**
 * Validate query parameters against a schema
 */
export function validateQueryParams<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): T {
  try {
    const { searchParams } = new URL(request.url)
    const params: Record<string, string> = {}
    
    searchParams.forEach((value, key) => {
      params[key] = value
    })

    return schema.parse(params)
  } catch (error) {
    if (error instanceof Error && error.name === 'ZodError') {
      const zodError = error as unknown as { 
        errors: Array<{ path: string[], message: string }> 
      }
      const firstError = zodError.errors[0]
      const path = firstError.path.join('.')
      
      throw errorService.createError(
        ErrorCode.VALIDATION_ERROR,
        `Invalid query parameter: ${firstError.message}`,
        path
      )
    }

    throw errorService.createError(
      ErrorCode.BAD_REQUEST,
      'Invalid query parameters'
    )
  }
}

/**
 * Rate limiting middleware (basic implementation)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function rateLimit(
  request: NextRequest,
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): void {
  const clientIp = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
  const now = Date.now()
  
  const clientData = rateLimitMap.get(clientIp)
  
  if (!clientData || now > clientData.resetTime) {
    // Reset or initialize
    rateLimitMap.set(clientIp, {
      count: 1,
      resetTime: now + windowMs
    })
    return
  }
  
  if (clientData.count >= maxRequests) {
    throw errorService.createError(
      ErrorCode.TOO_MANY_REQUESTS,
      'Rate limit exceeded. Please try again later.',
      'rate_limit'
    )
  }
  
  clientData.count++
}

/**
 * CORS middleware
 */
export function setCorsHeaders(response: NextResponse): NextResponse {
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-user-id, x-user-role')
  response.headers.set('Access-Control-Max-Age', '86400')
  
  return response
}
