'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft,
  MapPin, 
  Star, 
  DollarSign, 
  Clock, 
  Heart, 
  MessageCircle,
  Briefcase,
  CheckCircle,
  TrendingUp,
  Award,
  Users,
  Calendar,
  Globe,
  Mail,
  Phone,
  ExternalLink,
  Download,
  Share2,
  Flag,
  Building,
  GraduationCap,
  Code,
  Palette,
  Database,
  Shield,
  Zap,
  Eye,
  ThumbsUp,
  MessageSquare,
  FileText,
  Camera,
  Video,
  Link as LinkIcon
} from 'lucide-react'

export default function TalentProfilePage() {
  const params = useParams()
  const router = useRouter()
  const talentId = params.id
  const [activeTab, setActiveTab] = useState('overview')
  const [isSaved, setIsSaved] = useState(false)
  const [isFollowing, setIsFollowing] = useState(false)

  // Mock talent data - in real app this would come from API based on ID
  const talent = {
    id: talentId,
    name: "Sarah Chen",
    title: "Senior Full Stack Developer & Technical Architect",
    location: "San Francisco, CA",
    timezone: "PST (UTC-8)",
    avatar: "/api/placeholder/120/120",
    coverImage: "/api/placeholder/1200/400",
    rating: 4.9,
    experience: 8,
    hourlyRate: 120,
    skills: ["React", "Node.js", "TypeScript", "AWS", "GraphQL", "PostgreSQL", "Docker", "Kubernetes"],
    bio: "Passionate full-stack developer with 8+ years of experience building scalable web applications. I specialize in modern JavaScript frameworks, cloud architecture, and leading development teams. My expertise spans from frontend user experiences to backend infrastructure, with a focus on performance, security, and maintainability.",
    availability: "Available",
    projects: 47,
    reviews: 23,
    responseTime: "2 hours",
    successRate: 98,
    totalEarnings: "$2.4M",
    languages: ["English (Native)", "Mandarin (Fluent)", "Spanish (Conversational)"],
    certifications: [
      "AWS Solutions Architect Professional",
      "Google Cloud Professional Developer",
      "Certified Kubernetes Administrator"
    ],
    portfolio: [
      {
        id: 1,
        title: "E-commerce Platform Redesign",
        description: "Complete redesign and rebuild of a major e-commerce platform serving 2M+ users with 99.9% uptime",
        image: "/api/placeholder/400/300",
        technologies: ["React", "Node.js", "PostgreSQL", "AWS", "Redis"],
        link: "https://example.com",
        client: "TechCorp Inc.",
        duration: "6 months",
        budget: "$150,000"
      },
      {
        id: 2,
        title: "Real-time Analytics Dashboard",
        description: "Built a comprehensive analytics dashboard with real-time data processing and visualization",
        image: "/api/placeholder/400/300",
        technologies: ["Vue.js", "Python", "InfluxDB", "Grafana", "Docker"],
        link: "https://example.com",
        client: "DataFlow Solutions",
        duration: "4 months",
        budget: "$85,000"
      },
      {
        id: 3,
        title: "Mobile Banking Application",
        description: "Secure mobile banking app with biometric authentication and real-time transactions",
        image: "/api/placeholder/400/300",
        technologies: ["React Native", "Node.js", "MongoDB", "AWS", "Stripe"],
        link: "https://example.com",
        client: "FinTech Innovations",
        duration: "8 months",
        budget: "$200,000"
      }
    ],
    workHistory: [
      {
        company: "TechCorp Inc.",
        position: "Senior Full Stack Developer",
        duration: "2021 - Present",
        location: "San Francisco, CA",
        description: "Led development of microservices architecture serving 1M+ users. Implemented CI/CD pipelines reducing deployment time by 70%. Mentored junior developers and established coding standards.",
        achievements: [
          "Reduced page load times by 40% through optimization",
          "Led team of 5 developers on major platform redesign",
          "Implemented automated testing increasing code coverage to 95%"
        ]
      },
      {
        company: "StartupXYZ",
        position: "Full Stack Developer",
        duration: "2019 - 2021",
        location: "San Francisco, CA",
        description: "Built MVP from scratch and scaled to 100k users. Implemented real-time features and payment processing. Worked directly with founders on product strategy.",
        achievements: [
          "Built entire platform from concept to 100k users",
          "Implemented real-time chat and notifications",
          "Integrated multiple payment gateways"
        ]
      },
      {
        company: "Digital Agency Pro",
        position: "Frontend Developer",
        duration: "2017 - 2019",
        location: "San Francisco, CA",
        description: "Developed responsive websites and web applications for various clients. Specialized in React and modern CSS frameworks.",
        achievements: [
          "Delivered 20+ client projects on time and budget",
          "Improved client satisfaction scores by 25%",
          "Established component library used across projects"
        ]
      }
    ],
    education: [
      {
        institution: "Stanford University",
        degree: "Master of Computer Science",
        year: "2017",
        gpa: "3.8/4.0",
        focus: "Distributed Systems and Machine Learning"
      },
      {
        institution: "UC Berkeley",
        degree: "Bachelor of Computer Science",
        year: "2015",
        gpa: "3.6/4.0",
        focus: "Software Engineering and Algorithms"
      }
    ],
    reviews: [
      {
        id: 1,
        client: "John Smith",
        company: "TechStartup Inc.",
        rating: 5,
        comment: "Sarah exceeded all expectations. Her technical expertise and communication skills are outstanding. She delivered a complex e-commerce platform ahead of schedule and under budget. Highly recommended!",
        project: "E-commerce Platform Development",
        date: "2 weeks ago",
        projectBudget: "$150,000"
      },
      {
        id: 2,
        client: "Maria Garcia",
        company: "Digital Solutions LLC",
        rating: 5,
        comment: "Exceptional developer with deep knowledge of modern web technologies. Sarah's attention to detail and problem-solving abilities are remarkable. Will definitely work with her again.",
        project: "Real-time Analytics Dashboard",
        date: "1 month ago",
        projectBudget: "$85,000"
      },
      {
        id: 3,
        client: "David Johnson",
        company: "FinTech Innovations",
        rating: 5,
        comment: "Sarah built our mobile banking app with the highest security standards. Her expertise in financial technology and regulatory compliance was invaluable. Outstanding work!",
        project: "Mobile Banking Application",
        date: "2 months ago",
        projectBudget: "$200,000"
      }
    ],
    stats: {
      totalProjects: 47,
      successRate: 98,
      onTimeDelivery: 96,
      clientSatisfaction: 4.9,
      repeatClients: 85,
      avgProjectValue: "$75,000"
    }
  }

  const getAvailabilityColor = (availability: string) => {
    switch (availability.toLowerCase()) {
      case 'available':
        return 'bg-green-500'
      case 'busy':
        return 'bg-yellow-500'
      case 'unavailable':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getExperienceLevel = (years: number) => {
    if (years < 2) return 'Junior'
    if (years < 5) return 'Mid-level'
    if (years < 8) return 'Senior'
    return 'Expert'
  }

  return (
    <div className="pt-16">
      {/* Hero Section with Cover Image */}
      <section className="relative h-80 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-background to-primary/10" />
        <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_20%_30%,_hsl(var(--primary))_3px,_transparent_3px),_radial-gradient(circle_at_80%_70%,_hsl(var(--primary))_2px,_transparent_2px)] bg-[length:80px_80px,_120px_120px]" />
        
        {/* Back Button */}
        <div className="absolute top-6 left-8 z-10">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="bg-background/80 backdrop-blur-sm"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Talent
          </Button>
        </div>

        {/* Profile Header */}
        <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-background via-background/80 to-transparent">
          <div className="container mx-auto max-w-6xl px-8">
            <div className="flex items-end space-x-6">
              <div className="relative">
                <Avatar className="w-32 h-32 border-4 border-background shadow-xl">
                  <AvatarImage src={talent.avatar} alt={talent.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-3xl">
                    {talent.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className={`absolute -bottom-2 -right-2 w-8 h-8 ${getAvailabilityColor(talent.availability)} rounded-full border-4 border-background flex items-center justify-center`}>
                  <div className="w-3 h-3 bg-white rounded-full" />
                </div>
              </div>
              
              <div className="flex-1 pb-4">
                <h1 className="text-3xl font-bold mb-2">{talent.name}</h1>
                <p className="text-primary font-medium text-xl mb-3">{talent.title}</p>
                <div className="flex items-center space-x-6 text-sm text-muted-foreground mb-4">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-4 h-4" />
                    <span>{talent.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span>{talent.rating}</span>
                    <span>({talent.reviews.length} reviews)</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Briefcase className="w-4 h-4" />
                    <span>{talent.experience} years • {getExperienceLevel(talent.experience)}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{talent.timezone}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Badge variant="secondary" className={`${getAvailabilityColor(talent.availability)} text-white`}>
                    {talent.availability}
                  </Badge>
                  <Badge variant="outline">
                    Responds in {talent.responseTime}
                  </Badge>
                  <Badge variant="outline">
                    {talent.successRate}% Success Rate
                  </Badge>
                </div>
              </div>

              <div className="flex items-center space-x-4 pb-4">
                <div className="text-right mr-4">
                  <div className="text-4xl font-bold text-primary">${talent.hourlyRate}</div>
                  <div className="text-sm text-muted-foreground">per hour</div>
                </div>
                <div className="flex flex-col space-y-2">
                  <Button 
                    className="button-premium"
                    onClick={() => {
                      // Handle hire logic
                      console.log('Hire talent:', talent.name)
                    }}
                  >
                    <Briefcase className="w-4 h-4 mr-2" />
                    Hire Now
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => {
                      // Handle contact logic
                      console.log('Contact talent:', talent.name)
                    }}
                  >
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Contact
                  </Button>
                </div>
                <div className="flex flex-col space-y-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsSaved(!isSaved)}
                    className={`p-2 ${isSaved ? 'text-red-500' : 'text-muted-foreground'}`}
                  >
                    <Heart className={`w-5 h-5 ${isSaved ? 'fill-current' : ''}`} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsFollowing(!isFollowing)}
                    className={`p-2 ${isFollowing ? 'text-primary' : 'text-muted-foreground'}`}
                  >
                    <Users className="w-5 h-5" />
                  </Button>
                  <Button variant="ghost" size="sm" className="p-2">
                    <Share2 className="w-5 h-5" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto max-w-6xl px-8 py-12">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6 mb-12 max-w-4xl mx-auto">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
            <TabsTrigger value="experience">Experience</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-12 max-w-5xl mx-auto">
            {/* About Section */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-2xl">About {talent.name}</CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <p className="text-muted-foreground leading-relaxed text-lg">{talent.bio}</p>
              </CardContent>
            </Card>

            {/* Skills & Expertise */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-2xl">Skills & Expertise</CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="flex flex-wrap gap-3">
                  {talent.skills.map((skill) => (
                    <Badge key={skill} variant="secondary" className="theme-glow text-sm py-2 px-4">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Key Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <Card className="card-enhanced text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-primary mb-3">{talent.projects}</div>
                  <div className="text-sm text-muted-foreground font-medium">Projects Completed</div>
                </CardContent>
              </Card>
              <Card className="card-enhanced text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-primary mb-3">{talent.successRate}%</div>
                  <div className="text-sm text-muted-foreground font-medium">Success Rate</div>
                </CardContent>
              </Card>
              <Card className="card-enhanced text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-primary mb-3">{talent.reviews.length}</div>
                  <div className="text-sm text-muted-foreground font-medium">Client Reviews</div>
                </CardContent>
              </Card>
              <Card className="card-enhanced text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-primary mb-3">{talent.responseTime}</div>
                  <div className="text-sm text-muted-foreground font-medium">Response Time</div>
                </CardContent>
              </Card>
            </div>

            {/* Languages & Certifications */}
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-xl">
                    <Globe className="w-5 h-5" />
                    <span>Languages</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="space-y-3">
                    {talent.languages.map((language, index) => (
                      <div key={index} className="text-muted-foreground text-lg">
                        {language}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 text-xl">
                    <Award className="w-5 h-5" />
                    <span>Certifications</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-8">
                  <div className="space-y-3">
                    {talent.certifications.map((cert, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                        <span className="text-muted-foreground text-lg">{cert}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="portfolio" className="space-y-12 max-w-5xl mx-auto">
            <div className="grid gap-12">
              {talent.portfolio.map((project) => (
                <Card key={project.id} className="card-premium">
                  <CardContent className="p-10">
                    <div className="grid md:grid-cols-3 gap-8">
                      <div className="md:col-span-1">
                        <div className="w-full h-56 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                          <Code className="w-20 h-20 text-primary" />
                        </div>
                      </div>
                      <div className="md:col-span-2">
                        <div className="flex items-start justify-between mb-4">
                          <h3 className="text-2xl font-bold">{project.title}</h3>
                          <Badge variant="outline" className="text-primary border-primary">
                            {project.budget}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground mb-4 text-lg">{project.description}</p>
                        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                          <div>
                            <span className="font-medium">Client:</span> {project.client}
                          </div>
                          <div>
                            <span className="font-medium">Duration:</span> {project.duration}
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {project.technologies.map((tech) => (
                            <Badge key={tech} variant="secondary">{tech}</Badge>
                          ))}
                        </div>
                        <Button variant="outline" className="button-enhanced">
                          <ExternalLink className="w-4 h-4 mr-2" />
                          View Project
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="experience" className="space-y-12 max-w-5xl mx-auto">
            {/* Work History */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-2xl">
                  <Briefcase className="w-6 h-6" />
                  <span>Work Experience</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-10 p-10">
                {talent.workHistory.map((job, index) => (
                  <div key={index} className="relative">
                    {index !== talent.workHistory.length - 1 && (
                      <div className="absolute left-6 top-12 bottom-0 w-px bg-border" />
                    )}
                    <div className="flex space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                        <Building className="w-6 h-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h4 className="text-xl font-bold">{job.position}</h4>
                            <p className="text-primary font-medium text-lg">{job.company}</p>
                          </div>
                          <Badge variant="outline">{job.duration}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{job.location}</p>
                        <p className="text-muted-foreground mb-4">{job.description}</p>
                        <div className="space-y-2">
                          <h5 className="font-medium">Key Achievements:</h5>
                          <ul className="space-y-1">
                            {job.achievements.map((achievement, i) => (
                              <li key={i} className="flex items-start space-x-2 text-sm text-muted-foreground">
                                <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 shrink-0" />
                                <span>{achievement}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Education */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-2xl">
                  <GraduationCap className="w-6 h-6" />
                  <span>Education</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-8 p-10">
                {talent.education.map((edu, index) => (
                  <div key={index} className="flex space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                      <GraduationCap className="w-6 h-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-bold">{edu.degree}</h4>
                      <p className="text-primary font-medium">{edu.institution}</p>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>{edu.year}</span>
                        <span>GPA: {edu.gpa}</span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">Focus: {edu.focus}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reviews" className="space-y-10 max-w-5xl mx-auto">
            {talent.reviews.map((review) => (
              <Card key={review.id} className="card-premium">
                <CardContent className="p-10">
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-start space-x-4">
                      <Avatar className="w-12 h-12">
                        <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold">
                          {review.client.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h4 className="font-bold text-lg">{review.client}</h4>
                        <p className="text-muted-foreground">{review.company}</p>
                        <p className="text-sm text-muted-foreground">{review.date}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-1 mb-2">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-5 h-5 ${
                              i < review.rating
                                ? 'fill-yellow-400 text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <Badge variant="outline" className="text-primary border-primary">
                        {review.projectBudget}
                      </Badge>
                    </div>
                  </div>
                  <blockquote className="text-muted-foreground text-lg leading-relaxed mb-4 italic">
                    "{review.comment}"
                  </blockquote>
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span className="font-medium">Project: {review.project}</span>
                    <div className="flex items-center space-x-4">
                      <Button variant="ghost" size="sm">
                        <ThumbsUp className="w-4 h-4 mr-2" />
                        Helpful
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Reply
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="stats" className="space-y-12 max-w-5xl mx-auto">
            {/* Performance Metrics */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-2xl">Performance Metrics</CardTitle>
                <CardDescription className="text-lg">
                  Comprehensive statistics and performance indicators
                </CardDescription>
              </CardHeader>
              <CardContent className="p-10">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-10">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{talent.stats.totalProjects}</div>
                    <div className="text-sm text-muted-foreground">Total Projects</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{talent.stats.successRate}%</div>
                    <div className="text-sm text-muted-foreground">Success Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{talent.stats.onTimeDelivery}%</div>
                    <div className="text-sm text-muted-foreground">On-Time Delivery</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{talent.stats.clientSatisfaction}</div>
                    <div className="text-sm text-muted-foreground">Client Satisfaction</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{talent.stats.repeatClients}%</div>
                    <div className="text-sm text-muted-foreground">Repeat Clients</div>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-primary mb-2">{talent.stats.avgProjectValue}</div>
                    <div className="text-sm text-muted-foreground">Avg Project Value</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Earnings Overview */}
            <Card className="card-premium">
              <CardHeader>
                <CardTitle className="text-2xl">Earnings Overview</CardTitle>
              </CardHeader>
              <CardContent className="p-10">
                <div className="text-center">
                  <div className="text-6xl font-bold text-primary mb-4">{talent.totalEarnings}</div>
                  <div className="text-muted-foreground text-lg">Total Earnings on Platform</div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contact" className="space-y-12 max-w-5xl mx-auto">
            <div className="grid md:grid-cols-2 gap-12">
              {/* Contact Information */}
              <Card className="card-premium">
                <CardHeader>
                  <CardTitle className="text-2xl">Get in Touch</CardTitle>
                  <CardDescription className="text-lg">
                    Ready to start your project with {talent.name}?
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 p-8">
                  <Button className="button-premium w-full">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Send Direct Message
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule a Call
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Briefcase className="w-4 h-4 mr-2" />
                    Invite to Project
                  </Button>
                  <Button variant="outline" className="w-full">
                    <FileText className="w-4 h-4 mr-2" />
                    Request Proposal
                  </Button>
                </CardContent>
              </Card>

              {/* Additional Info */}
              <Card className="card-premium">
                <CardHeader>
                  <CardTitle className="text-2xl">Professional Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6 p-8">
                  <div className="flex items-center space-x-3 text-sm">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span>Timezone: {talent.timezone}</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <Globe className="w-4 h-4 text-muted-foreground" />
                    <span>Languages: {talent.languages.join(', ')}</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Verified Profile</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <Shield className="w-4 h-4 text-green-500" />
                    <span>Background Checked</span>
                  </div>
                  <div className="flex items-center space-x-3 text-sm">
                    <Award className="w-4 h-4 text-primary" />
                    <span>Top Rated Professional</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
