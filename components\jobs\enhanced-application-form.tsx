'use client'

import React, { useState, useRef } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useJobsStore, useAuthStore } from '@/stores'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ButtonLoading } from '@/components/ui/button-loading'
import { ErrorAlert } from '@/components/ui/error-alert'
import {
  Upload,
  FileText,
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  X,
  CheckCircle,
  AlertCircle,
  Calendar,
  DollarSign,
  Globe,
  Linkedin,
  Github,
  ExternalLink,
  Clock,
  Target,
  Award,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Job {
  _id: string
  title: string
  company: {
    _id: string
    name: string
    logo?: string
  }
  location: {
    city: string
    state: string
    remote: boolean
  }
  type: string
  salary?: {
    min?: number
    max?: number
    currency: string
    period: string
  }
  requirements?: string[]
  customQuestions?: Array<{
    id: string
    question: string
    type: 'text' | 'textarea' | 'select' | 'multiselect'
    required: boolean
    options?: string[]
  }>
}

interface EnhancedApplicationFormProps {
  job: Job
  onSuccess?: () => void
  onCancel?: () => void
}

interface ApplicationFormData {
  // Personal Information
  firstName: string
  lastName: string
  email: string
  phone: string
  location: string

  // Professional Information
  currentTitle: string
  experience: string
  education: string

  // Application Details
  coverLetter: string
  resumeFile: File | null
  portfolioUrl: string
  linkedinUrl: string
  githubUrl: string
  websiteUrl: string

  // Preferences
  availableStartDate: string
  salaryExpectation: string
  willingToRelocate: boolean
  remoteWork: boolean

  // Custom Questions
  customAnswers: Record<string, string | string[]>

  // Legal
  workAuthorization: boolean
  backgroundCheck: boolean
  privacyConsent: boolean
}

interface FormErrors {
  [key: string]: string
}

const FORM_STEPS = [
  { id: 'personal', title: 'Personal Info', icon: User },
  { id: 'professional', title: 'Professional', icon: Briefcase },
  { id: 'application', title: 'Application', icon: FileText },
  { id: 'preferences', title: 'Preferences', icon: Target },
  { id: 'review', title: 'Review', icon: CheckCircle }
]

export function EnhancedApplicationForm({ job, onSuccess, onCancel }: EnhancedApplicationFormProps) {
  const router = useRouter()
  const { user } = useAuthStore()
  const { applyToJob, applyLoading, error, clearError } = useJobsStore()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<ApplicationFormData>({
    // Pre-fill with user data
    firstName: user?.profile?.firstName || '',
    lastName: user?.profile?.lastName || '',
    email: user?.email || '',
    phone: user?.profile?.phone || '',
    location: user?.profile?.location ? `${user.profile.location.city}, ${user.profile.location.state}` : '',

    currentTitle: '',
    experience: '',
    education: '',

    coverLetter: '',
    resumeFile: null,
    portfolioUrl: '',
    linkedinUrl: '',
    githubUrl: '',
    websiteUrl: '',

    availableStartDate: '',
    salaryExpectation: '',
    willingToRelocate: false,
    remoteWork: false,

    customAnswers: {},

    workAuthorization: false,
    backgroundCheck: false,
    privacyConsent: false
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [uploadProgress, setUploadProgress] = useState(0)

  // Calculate form completion percentage
  const getFormProgress = () => {
    const totalSteps = FORM_STEPS.length
    const currentProgress = (currentStep / (totalSteps - 1)) * 100
    return Math.min(currentProgress, 100)
  }

  // Validate current step
  const validateStep = (step: number): boolean => {
    const newErrors: FormErrors = {}

    switch (step) {
      case 0: // Personal Info
        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required'
        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required'
        if (!formData.email.trim()) newErrors.email = 'Email is required'
        else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Invalid email format'
        if (!formData.phone.trim()) newErrors.phone = 'Phone number is required'
        break

      case 1: // Professional
        if (!formData.currentTitle.trim()) newErrors.currentTitle = 'Current title is required'
        if (!formData.experience.trim()) newErrors.experience = 'Experience level is required'
        break

      case 2: // Application
        if (!formData.coverLetter.trim()) newErrors.coverLetter = 'Cover letter is required'
        if (!formData.resumeFile) newErrors.resumeFile = 'Resume is required'
        break

      case 3: // Preferences
        if (!formData.availableStartDate) newErrors.availableStartDate = 'Available start date is required'
        break

      case 4: // Review
        if (!formData.workAuthorization) newErrors.workAuthorization = 'Work authorization confirmation is required'
        if (!formData.privacyConsent) newErrors.privacyConsent = 'Privacy consent is required'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Handle input changes
  const handleInputChange = (field: keyof ApplicationFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const value = e.target.type === 'checkbox' ? (e.target as HTMLInputElement).checked : e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type and size
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      const maxSize = 5 * 1024 * 1024 // 5MB

      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({ ...prev, resumeFile: 'Please upload a PDF or Word document' }))
        return
      }

      if (file.size > maxSize) {
        setErrors(prev => ({ ...prev, resumeFile: 'File size must be less than 5MB' }))
        return
      }

      setFormData(prev => ({ ...prev, resumeFile: file }))
      setErrors(prev => ({ ...prev, resumeFile: undefined }))

      // Simulate upload progress
      setUploadProgress(0)
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval)
            return 100
          }
          return prev + 10
        })
      }, 100)
    }
  }

  // Handle next step
  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, FORM_STEPS.length - 1))
    }
  }

  // Handle previous step
  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0))
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      return
    }

    try {
      clearError()

      // Prepare application data
      const applicationData = {
        coverLetter: formData.coverLetter,
        resumeId: 'temp-resume-id', // Would be actual resume ID after upload
        customFields: {
          currentTitle: formData.currentTitle,
          experience: formData.experience,
          education: formData.education,
          portfolioUrl: formData.portfolioUrl,
          linkedinUrl: formData.linkedinUrl,
          githubUrl: formData.githubUrl,
          websiteUrl: formData.websiteUrl,
          availableStartDate: formData.availableStartDate,
          salaryExpectation: formData.salaryExpectation,
          willingToRelocate: formData.willingToRelocate,
          remoteWork: formData.remoteWork,
          workAuthorization: formData.workAuthorization,
          backgroundCheck: formData.backgroundCheck,
          ...formData.customAnswers
        }
      }

      await applyToJob(job._id, applicationData)

      if (onSuccess) {
        onSuccess()
      } else {
        router.push(`/applications?applied=${job._id}`)
      }
    } catch (error) {
      console.error('Application failed:', error)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="text-2xl">Apply for {job.title}</CardTitle>
              <CardDescription className="mt-2">
                at {job.company.name} • {job.location.remote ? 'Remote' : `${job.location.city}, ${job.location.state}`}
              </CardDescription>
            </div>
            <Badge variant="outline" className="ml-4">
              {job.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Progress Indicator */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>Application Progress</span>
              <span>{Math.round(getFormProgress())}% Complete</span>
            </div>
            <Progress value={getFormProgress()} className="h-2" />

            {/* Step Indicators */}
            <div className="flex justify-between">
              {FORM_STEPS.map((step, index) => {
                const Icon = step.icon
                const isActive = index === currentStep
                const isCompleted = index < currentStep

                return (
                  <div
                    key={step.id}
                    className={cn(
                      'flex flex-col items-center space-y-2 text-xs',
                      isActive && 'text-primary',
                      isCompleted && 'text-green-600',
                      !isActive && !isCompleted && 'text-muted-foreground'
                    )}
                  >
                    <div className={cn(
                      'w-8 h-8 rounded-full flex items-center justify-center border-2',
                      isActive && 'border-primary bg-primary/10',
                      isCompleted && 'border-green-600 bg-green-50',
                      !isActive && !isCompleted && 'border-muted-foreground/30'
                    )}>
                      {isCompleted ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <Icon className="w-4 h-4" />
                      )}
                    </div>
                    <span className="hidden sm:block">{step.title}</span>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form Content */}
      <div className="space-y-6">
        {/* General Error */}
        {error && (
          <ErrorAlert
            type="error"
            message={error.message || 'Application failed. Please try again.'}
            dismissible
            onDismiss={clearError}
          />
        )}

        {/* Step 0: Personal Information */}
        {currentStep === 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>Personal Information</span>
              </CardTitle>
              <CardDescription>
                Tell us about yourself
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange('firstName')}
                    className={errors.firstName ? 'border-red-500' : ''}
                  />
                  {errors.firstName && (
                    <p className="text-sm text-red-500 mt-1">{errors.firstName}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange('lastName')}
                    className={errors.lastName ? 'border-red-500' : ''}
                  />
                  {errors.lastName && (
                    <p className="text-sm text-red-500 mt-1">{errors.lastName}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    className={errors.email ? 'border-red-500' : ''}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500 mt-1">{errors.email}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange('phone')}
                    className={errors.phone ? 'border-red-500' : ''}
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-500 mt-1">{errors.phone}</p>
                  )}
                </div>
              </div>
              <div>
                <Label htmlFor="location">Current Location</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={handleInputChange('location')}
                  placeholder="City, State"
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 1: Professional Information */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Briefcase className="w-5 h-5" />
                <span>Professional Information</span>
              </CardTitle>
              <CardDescription>
                Share your professional background
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="currentTitle">Current Job Title *</Label>
                <Input
                  id="currentTitle"
                  value={formData.currentTitle}
                  onChange={handleInputChange('currentTitle')}
                  placeholder="e.g. Senior Software Engineer"
                  className={errors.currentTitle ? 'border-red-500' : ''}
                />
                {errors.currentTitle && (
                  <p className="text-sm text-red-500 mt-1">{errors.currentTitle}</p>
                )}
              </div>
              <div>
                <Label htmlFor="experience">Years of Experience *</Label>
                <Select
                  value={formData.experience}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, experience: value }))}
                >
                  <SelectTrigger className={errors.experience ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select experience level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0-1">0-1 years</SelectItem>
                    <SelectItem value="2-3">2-3 years</SelectItem>
                    <SelectItem value="4-6">4-6 years</SelectItem>
                    <SelectItem value="7-10">7-10 years</SelectItem>
                    <SelectItem value="10+">10+ years</SelectItem>
                  </SelectContent>
                </Select>
                {errors.experience && (
                  <p className="text-sm text-red-500 mt-1">{errors.experience}</p>
                )}
              </div>
              <div>
                <Label htmlFor="education">Highest Education</Label>
                <Select
                  value={formData.education}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, education: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select education level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high-school">High School</SelectItem>
                    <SelectItem value="associate">Associate Degree</SelectItem>
                    <SelectItem value="bachelor">Bachelor's Degree</SelectItem>
                    <SelectItem value="master">Master's Degree</SelectItem>
                    <SelectItem value="phd">PhD</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Application Details */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>Application Details</span>
              </CardTitle>
              <CardDescription>
                Upload your documents and write your cover letter
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Resume Upload */}
              <div>
                <Label>Resume/CV *</Label>
                <div className="mt-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                  {!formData.resumeFile ? (
                    <div
                      onClick={() => fileInputRef.current?.click()}
                      className={cn(
                        'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer hover:border-primary/50 transition-colors',
                        errors.resumeFile ? 'border-red-500' : 'border-muted-foreground/25'
                      )}
                    >
                      <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">
                        Click to upload your resume (PDF, DOC, DOCX - Max 5MB)
                      </p>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <FileText className="w-8 h-8 text-green-600" />
                        <div>
                          <p className="font-medium">{formData.resumeFile.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {(formData.resumeFile.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setFormData(prev => ({ ...prev, resumeFile: null }))
                          setUploadProgress(0)
                        }}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  )}
                  {uploadProgress > 0 && uploadProgress < 100 && (
                    <div className="mt-2">
                      <Progress value={uploadProgress} className="h-2" />
                    </div>
                  )}
                  {errors.resumeFile && (
                    <p className="text-sm text-red-500 mt-1">{errors.resumeFile}</p>
                  )}
                </div>
              </div>

              {/* Cover Letter */}
              <div>
                <Label htmlFor="coverLetter">Cover Letter *</Label>
                <Textarea
                  id="coverLetter"
                  value={formData.coverLetter}
                  onChange={handleInputChange('coverLetter')}
                  placeholder="Tell us why you're interested in this position and what makes you a great fit..."
                  className={cn('min-h-[150px]', errors.coverLetter ? 'border-red-500' : '')}
                />
                <div className="flex justify-between mt-1">
                  {errors.coverLetter && (
                    <p className="text-sm text-red-500">{errors.coverLetter}</p>
                  )}
                  <p className="text-sm text-muted-foreground ml-auto">
                    {formData.coverLetter.length}/2000 characters
                  </p>
                </div>
              </div>

              {/* Portfolio & Social Links */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="portfolioUrl">Portfolio URL</Label>
                  <div className="relative">
                    <Globe className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="portfolioUrl"
                      value={formData.portfolioUrl}
                      onChange={handleInputChange('portfolioUrl')}
                      placeholder="https://yourportfolio.com"
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="linkedinUrl">LinkedIn Profile</Label>
                  <div className="relative">
                    <Linkedin className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="linkedinUrl"
                      value={formData.linkedinUrl}
                      onChange={handleInputChange('linkedinUrl')}
                      placeholder="https://linkedin.com/in/yourprofile"
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="githubUrl">GitHub Profile</Label>
                  <div className="relative">
                    <Github className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="githubUrl"
                      value={formData.githubUrl}
                      onChange={handleInputChange('githubUrl')}
                      placeholder="https://github.com/yourusername"
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="websiteUrl">Personal Website</Label>
                  <div className="relative">
                    <ExternalLink className="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                    <Input
                      id="websiteUrl"
                      value={formData.websiteUrl}
                      onChange={handleInputChange('websiteUrl')}
                      placeholder="https://yourwebsite.com"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Preferences */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5" />
                <span>Job Preferences</span>
              </CardTitle>
              <CardDescription>
                Let us know your preferences and availability
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="availableStartDate">Available Start Date *</Label>
                  <Input
                    id="availableStartDate"
                    type="date"
                    value={formData.availableStartDate}
                    onChange={handleInputChange('availableStartDate')}
                    className={errors.availableStartDate ? 'border-red-500' : ''}
                  />
                  {errors.availableStartDate && (
                    <p className="text-sm text-red-500 mt-1">{errors.availableStartDate}</p>
                  )}
                </div>
                <div>
                  <Label htmlFor="salaryExpectation">Salary Expectation</Label>
                  <Input
                    id="salaryExpectation"
                    value={formData.salaryExpectation}
                    onChange={handleInputChange('salaryExpectation')}
                    placeholder="e.g. $80,000 - $100,000"
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Work Preferences</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="remoteWork"
                      checked={formData.remoteWork}
                      onCheckedChange={(checked) =>
                        setFormData(prev => ({ ...prev, remoteWork: checked as boolean }))
                      }
                    />
                    <Label htmlFor="remoteWork" className="text-sm">
                      I'm interested in remote work opportunities
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="willingToRelocate"
                      checked={formData.willingToRelocate}
                      onCheckedChange={(checked) =>
                        setFormData(prev => ({ ...prev, willingToRelocate: checked as boolean }))
                      }
                    />
                    <Label htmlFor="willingToRelocate" className="text-sm">
                      I'm willing to relocate for the right opportunity
                    </Label>
                  </div>
                </div>
              </div>

              {/* Salary Information */}
              {job.salary && (
                <div className="p-4 bg-muted/50 rounded-lg">
                  <h4 className="font-medium mb-2">Position Salary Range</h4>
                  <div className="flex items-center space-x-2 text-green-600">
                    <DollarSign className="w-4 h-4" />
                    <span>
                      {job.salary.min && job.salary.max
                        ? `$${job.salary.min.toLocaleString()} - $${job.salary.max.toLocaleString()}`
                        : job.salary.min
                        ? `From $${job.salary.min.toLocaleString()}`
                        : job.salary.max
                        ? `Up to $${job.salary.max.toLocaleString()}`
                        : 'Competitive salary'
                      } per {job.salary.period?.replace('ly', '') || 'year'}
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Step 4: Review & Submit */}
        {currentStep === 4 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5" />
                <span>Review & Submit</span>
              </CardTitle>
              <CardDescription>
                Review your application before submitting
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Application Summary */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Applicant</h4>
                    <p className="font-medium">{formData.firstName} {formData.lastName}</p>
                    <p className="text-sm text-muted-foreground">{formData.email}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Position</h4>
                    <p className="font-medium">{job.title}</p>
                    <p className="text-sm text-muted-foreground">{job.company.name}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Experience</h4>
                    <p className="font-medium">{formData.currentTitle}</p>
                    <p className="text-sm text-muted-foreground">{formData.experience} years</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">Documents</h4>
                    <p className="font-medium">
                      {formData.resumeFile ? formData.resumeFile.name : 'No resume uploaded'}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Cover letter: {formData.coverLetter.length} characters
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Legal Requirements */}
              <div className="space-y-4">
                <h4 className="font-medium">Legal Requirements</h4>
                <div className="space-y-3">
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="workAuthorization"
                      checked={formData.workAuthorization}
                      onCheckedChange={(checked) =>
                        setFormData(prev => ({ ...prev, workAuthorization: checked as boolean }))
                      }
                      className={errors.workAuthorization ? 'border-red-500' : ''}
                    />
                    <div className="space-y-1">
                      <Label htmlFor="workAuthorization" className="text-sm">
                        I confirm that I am authorized to work in this country *
                      </Label>
                      {errors.workAuthorization && (
                        <p className="text-sm text-red-500">{errors.workAuthorization}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="backgroundCheck"
                      checked={formData.backgroundCheck}
                      onCheckedChange={(checked) =>
                        setFormData(prev => ({ ...prev, backgroundCheck: checked as boolean }))
                      }
                    />
                    <Label htmlFor="backgroundCheck" className="text-sm">
                      I consent to a background check if required for this position
                    </Label>
                  </div>
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="privacyConsent"
                      checked={formData.privacyConsent}
                      onCheckedChange={(checked) =>
                        setFormData(prev => ({ ...prev, privacyConsent: checked as boolean }))
                      }
                      className={errors.privacyConsent ? 'border-red-500' : ''}
                    />
                    <div className="space-y-1">
                      <Label htmlFor="privacyConsent" className="text-sm">
                        I agree to the privacy policy and terms of service *
                      </Label>
                      {errors.privacyConsent && (
                        <p className="text-sm text-red-500">{errors.privacyConsent}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Final Notice */}
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-900 mb-1">Before you submit:</p>
                    <ul className="text-blue-800 space-y-1">
                      <li>• Double-check all information for accuracy</li>
                      <li>• Ensure your resume is up-to-date</li>
                      <li>• Review your cover letter for any typos</li>
                      <li>• You'll receive a confirmation email once submitted</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={currentStep === 0 ? onCancel : handlePrevious}
            disabled={applyLoading}
          >
            {currentStep === 0 ? 'Cancel' : 'Previous'}
          </Button>

          {currentStep < FORM_STEPS.length - 1 ? (
            <Button onClick={handleNext} disabled={applyLoading}>
              Next
            </Button>
          ) : (
            <ButtonLoading
              onClick={handleSubmit}
              loading={applyLoading}
              loadingText="Submitting Application..."
              disabled={applyLoading}
            >
              Submit Application
            </ButtonLoading>
          )}
        </div>
      </div>
    </div>
  )
}