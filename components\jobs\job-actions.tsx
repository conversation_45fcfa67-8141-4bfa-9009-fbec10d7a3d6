'use client'

import React, { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useJobsStore, useAuthStore } from '@/stores'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { ButtonLoading } from '@/components/ui/button-loading'
import { 
  Bookmark, 
  BookmarkCheck, 
  Share2, 
  Send,
  Copy,
  Mail,
  MessageCircle,
  Linkedin,
  Twitter,
  Facebook,
  Link,
  CheckCircle,
  Heart,
  Flag,
  ExternalLink,
  Users,
  TrendingUp
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface Job {
  _id: string
  title: string
  company: {
    _id: string
    name: string
    logo?: string
  }
  location: {
    city: string
    state: string
    remote: boolean
  }
  type: string
  salary?: {
    min?: number
    max?: number
    currency: string
  }
  description: string
  postedAt: Date
  viewsCount: number
  applicationsCount: number
}

interface JobActionsProps {
  job: Job
  isSaved?: boolean
  isApplied?: boolean
  onApply?: () => void
  onSave?: () => void
  onUnsave?: () => void
  className?: string
}

interface ShareDialogProps {
  job: Job
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface ReportDialogProps {
  job: Job
  open: boolean
  onOpenChange: (open: boolean) => void
}

function ShareDialog({ job, open, onOpenChange }: ShareDialogProps) {
  const [copied, setCopied] = useState(false)
  const [emailData, setEmailData] = useState({
    to: '',
    subject: `Check out this job: ${job.title}`,
    message: `I thought you might be interested in this job opportunity:\n\n${job.title} at ${job.company.name}\n\nLocation: ${job.location.remote ? 'Remote' : `${job.location.city}, ${job.location.state}`}\n\nCheck it out here: ${window.location.origin}/jobs/${job._id}`
  })

  const jobUrl = `${window.location.origin}/jobs/${job._id}`
  const shareText = `Check out this job opportunity: ${job.title} at ${job.company.name}`

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(jobUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy link:', error)
    }
  }

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: job.title,
          text: shareText,
          url: jobUrl,
        })
      } catch (error) {
        console.error('Share failed:', error)
      }
    }
  }

  const handleSocialShare = (platform: string) => {
    const encodedUrl = encodeURIComponent(jobUrl)
    const encodedText = encodeURIComponent(shareText)
    
    let shareUrl = ''
    
    switch (platform) {
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`
        break
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`
        break
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
        break
    }
    
    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400')
    }
  }

  const handleEmailShare = () => {
    const subject = encodeURIComponent(emailData.subject)
    const body = encodeURIComponent(emailData.message)
    const mailtoUrl = `mailto:${emailData.to}?subject=${subject}&body=${body}`
    window.location.href = mailtoUrl
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Share Job</DialogTitle>
          <DialogDescription>
            Share this job opportunity with others
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Quick Share */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Quick Share</h4>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyLink}
                className="justify-start"
              >
                {copied ? <CheckCircle className="w-4 h-4 mr-2" /> : <Copy className="w-4 h-4 mr-2" />}
                {copied ? 'Copied!' : 'Copy Link'}
              </Button>
              
              {navigator.share && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNativeShare}
                  className="justify-start"
                >
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
              )}
            </div>
          </div>

          {/* Social Media */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Social Media</h4>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSocialShare('linkedin')}
                className="justify-center"
              >
                <Linkedin className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSocialShare('twitter')}
                className="justify-center"
              >
                <Twitter className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSocialShare('facebook')}
                className="justify-center"
              >
                <Facebook className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Email Share */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Email to a Friend</h4>
            <div className="space-y-2">
              <div>
                <Label htmlFor="email-to" className="text-xs">To</Label>
                <Input
                  id="email-to"
                  type="email"
                  placeholder="<EMAIL>"
                  value={emailData.to}
                  onChange={(e) => setEmailData(prev => ({ ...prev, to: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="email-subject" className="text-xs">Subject</Label>
                <Input
                  id="email-subject"
                  value={emailData.subject}
                  onChange={(e) => setEmailData(prev => ({ ...prev, subject: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="email-message" className="text-xs">Message</Label>
                <Textarea
                  id="email-message"
                  rows={3}
                  value={emailData.message}
                  onChange={(e) => setEmailData(prev => ({ ...prev, message: e.target.value }))}
                />
              </div>
              <Button
                onClick={handleEmailShare}
                disabled={!emailData.to}
                className="w-full"
                size="sm"
              >
                <Mail className="w-4 h-4 mr-2" />
                Send Email
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

function ReportDialog({ job, open, onOpenChange }: ReportDialogProps) {
  const [reportData, setReportData] = useState({
    reason: '',
    description: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const reportReasons = [
    'Inappropriate content',
    'Misleading information',
    'Spam or duplicate posting',
    'Discriminatory language',
    'Fraudulent job posting',
    'Other'
  ]

  const handleSubmitReport = async () => {
    if (!reportData.reason) return

    setIsSubmitting(true)
    try {
      // TODO: Implement report submission API
      console.log('Submitting report:', { jobId: job._id, ...reportData })
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      onOpenChange(false)
      setReportData({ reason: '', description: '' })
      
      // Show success message
    } catch (error) {
      console.error('Failed to submit report:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Report Job</DialogTitle>
          <DialogDescription>
            Help us maintain quality by reporting inappropriate content
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label className="text-sm font-medium">Reason for reporting</Label>
            <div className="mt-2 space-y-2">
              {reportReasons.map((reason) => (
                <label key={reason} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="reason"
                    value={reason}
                    checked={reportData.reason === reason}
                    onChange={(e) => setReportData(prev => ({ ...prev, reason: e.target.value }))}
                    className="w-4 h-4"
                  />
                  <span className="text-sm">{reason}</span>
                </label>
              ))}
            </div>
          </div>
          
          <div>
            <Label htmlFor="description" className="text-sm font-medium">
              Additional details (optional)
            </Label>
            <Textarea
              id="description"
              placeholder="Please provide more details about the issue..."
              value={reportData.description}
              onChange={(e) => setReportData(prev => ({ ...prev, description: e.target.value }))}
              className="mt-1"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <ButtonLoading
            onClick={handleSubmitReport}
            loading={isSubmitting}
            disabled={!reportData.reason}
          >
            Submit Report
          </ButtonLoading>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export function JobActions({ 
  job, 
  isSaved = false, 
  isApplied = false, 
  onApply, 
  onSave, 
  onUnsave,
  className 
}: JobActionsProps) {
  const router = useRouter()
  const { isAuthenticated, user } = useAuthStore()
  const { saveLoading } = useJobsStore()
  
  const [shareDialogOpen, setShareDialogOpen] = useState(false)
  const [reportDialogOpen, setReportDialogOpen] = useState(false)

  const handleSaveToggle = () => {
    if (!isAuthenticated) {
      router.push('/signin')
      return
    }

    if (isSaved && onUnsave) {
      onUnsave()
    } else if (!isSaved && onSave) {
      onSave()
    }
  }

  const handleApply = () => {
    if (!isAuthenticated) {
      router.push('/signin')
      return
    }

    if (user?.role !== 'job_seeker') {
      // Show error or redirect
      return
    }

    if (onApply) {
      onApply()
    } else {
      router.push(`/jobs/${job._id}/apply`)
    }
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Primary Actions */}
      <div className="flex flex-col sm:flex-row gap-3">
        {!isApplied ? (
          <Button onClick={handleApply} size="lg" className="flex-1">
            <Send className="w-4 h-4 mr-2" />
            Apply Now
          </Button>
        ) : (
          <Button 
            variant="outline" 
            size="lg" 
            className="flex-1"
            onClick={() => router.push('/applications')}
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            Applied
          </Button>
        )}

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="lg"
            onClick={handleSaveToggle}
            disabled={saveLoading}
            className="flex-1 sm:flex-none"
          >
            {isSaved ? (
              <BookmarkCheck className="w-4 h-4 mr-2" />
            ) : (
              <Bookmark className="w-4 h-4 mr-2" />
            )}
            {isSaved ? 'Saved' : 'Save'}
          </Button>

          <Button
            variant="outline"
            size="lg"
            onClick={() => setShareDialogOpen(true)}
            className="flex-1 sm:flex-none"
          >
            <Share2 className="w-4 h-4 mr-2" />
            Share
          </Button>
        </div>
      </div>

      {/* Secondary Actions */}
      <div className="flex justify-between items-center text-sm text-muted-foreground">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            <Users className="w-4 h-4" />
            <span>{job.applicationsCount} applicants</span>
          </div>
          <div className="flex items-center space-x-1">
            <TrendingUp className="w-4 h-4" />
            <span>{job.viewsCount} views</span>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setReportDialogOpen(true)}
          className="text-muted-foreground hover:text-red-600"
        >
          <Flag className="w-4 h-4 mr-1" />
          Report
        </Button>
      </div>

      {/* Job Engagement Stats */}
      <Card className="bg-muted/30">
        <CardContent className="pt-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold">{job.applicationsCount}</div>
              <div className="text-xs text-muted-foreground">Applications</div>
            </div>
            <div>
              <div className="text-lg font-semibold">{job.viewsCount}</div>
              <div className="text-xs text-muted-foreground">Views</div>
            </div>
            <div>
              <div className="text-lg font-semibold">
                {Math.round((job.applicationsCount / Math.max(job.viewsCount, 1)) * 100)}%
              </div>
              <div className="text-xs text-muted-foreground">Apply Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dialogs */}
      <ShareDialog 
        job={job} 
        open={shareDialogOpen} 
        onOpenChange={setShareDialogOpen} 
      />
      <ReportDialog 
        job={job} 
        open={reportDialogOpen} 
        onOpenChange={setReportDialogOpen} 
      />
    </div>
  )
}
