'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useAuthStore } from '@/stores'
import { useLocationStore } from '@/stores/location-store'
import { type JobSeekerRegistrationData, type CompanyRegistrationData, type UserRole } from '@/types/user'
import { 
  Eye, EyeOff, User, Building, Mail, Lock, Phone, MapPin, 
  Briefcase, GraduationCap, Calendar, DollarSign, Globe,
  Users, Target, Award, CheckCircle, ArrowRight, ArrowLeft,
  Zap, Shield, Star, TrendingUp, Building2, UserCheck
} from 'lucide-react'
import Link from 'next/link'

interface EnhancedRegisterFormProps {
  onSuccess?: () => void
  defaultRole?: UserRole
}

export function EnhancedRegisterForm({ onSuccess, defaultRole }: EnhancedRegisterFormProps) {
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(defaultRole || null)
  const [currentStep, setCurrentStep] = useState(1)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  const { register, registerLoading, error, clearError, getRoleBasedRedirectPath } = useAuthStore()
  const { currentLocation } = useLocationStore()

  // Job Seeker form data
  const [jobSeekerData, setJobSeekerData] = useState<Partial<JobSeekerRegistrationData>>({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    city: currentLocation?.city || '',
    region: currentLocation?.region || '',
    country: currentLocation?.country || '',
    experienceLevel: 'entry',
    yearsOfExperience: 0,
    industry: '',
    educationLevel: 'bachelor',
    fieldOfStudy: '',
    jobTypes: ['Full-time'],
    workModels: ['On-site'],
    salaryMin: 0,
    salaryMax: 100000,
    salaryCurrency: 'USD',
    availabilityDate: '',
    willingToRelocate: false,
    workAuthorizationStatus: 'citizen',
    skills: [],
    termsAccepted: false,
    privacyAccepted: false,
    marketingEmails: false
  })

  // Company form data
  const [companyData, setCompanyData] = useState<Partial<CompanyRegistrationData>>({
    email: '',
    password: '',
    confirmPassword: '',
    companyName: '',
    website: '',
    industry: '',
    companySize: '1-10',
    companyType: 'startup',
    foundedYear: new Date().getFullYear(),
    city: currentLocation?.city || '',
    region: currentLocation?.region || '',
    country: currentLocation?.country || '',
    address: '',
    contactPersonName: '',
    contactPersonTitle: '',
    contactPersonPhone: '',
    description: '',
    currentOpenings: 0,
    hiringVolume: 'low',
    remoteHiring: false,
    internationalHiring: false,
    visaSponsorship: false,
    businessLicense: '',
    taxId: '',
    linkedinCompanyPage: '',
    termsAccepted: false,
    privacyAccepted: false,
    marketingEmails: false
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // Update location when detected
  useEffect(() => {
    if (currentLocation) {
      if (selectedRole === 'job_seeker') {
        setJobSeekerData(prev => ({
          ...prev,
          city: currentLocation.city,
          region: currentLocation.region,
          country: currentLocation.country
        }))
      } else if (selectedRole === 'company') {
        setCompanyData(prev => ({
          ...prev,
          city: currentLocation.city,
          region: currentLocation.region,
          country: currentLocation.country
        }))
      }
    }
  }, [currentLocation, selectedRole])

  const totalSteps = selectedRole === 'job_seeker' ? 4 : selectedRole === 'company' ? 4 : 1
  const progress = (currentStep / totalSteps) * 100

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {}
    
    if (selectedRole === 'job_seeker') {
      switch (step) {
        case 1:
          if (!jobSeekerData.email) newErrors.email = 'Email is required'
          if (!jobSeekerData.password) newErrors.password = 'Password is required'
          if (jobSeekerData.password !== jobSeekerData.confirmPassword) {
            newErrors.confirmPassword = 'Passwords do not match'
          }
          if (!jobSeekerData.firstName) newErrors.firstName = 'First name is required'
          if (!jobSeekerData.lastName) newErrors.lastName = 'Last name is required'
          break
        case 2:
          if (!jobSeekerData.city) newErrors.city = 'City is required'
          if (!jobSeekerData.country) newErrors.country = 'Country is required'
          if (!jobSeekerData.industry) newErrors.industry = 'Industry is required'
          break
        case 3:
          if (!jobSeekerData.fieldOfStudy) newErrors.fieldOfStudy = 'Field of study is required'
          if (!jobSeekerData.availabilityDate) newErrors.availabilityDate = 'Availability date is required'
          break
        case 4:
          if (!jobSeekerData.termsAccepted) newErrors.terms = 'You must accept the terms'
          if (!jobSeekerData.privacyAccepted) newErrors.privacy = 'You must accept the privacy policy'
          break
      }
    } else if (selectedRole === 'company') {
      switch (step) {
        case 1:
          if (!companyData.email) newErrors.email = 'Email is required'
          if (!companyData.password) newErrors.password = 'Password is required'
          if (companyData.password !== companyData.confirmPassword) {
            newErrors.confirmPassword = 'Passwords do not match'
          }
          if (!companyData.companyName) newErrors.companyName = 'Company name is required'
          break
        case 2:
          if (!companyData.industry) newErrors.industry = 'Industry is required'
          if (!companyData.city) newErrors.city = 'City is required'
          if (!companyData.country) newErrors.country = 'Country is required'
          break
        case 3:
          if (!companyData.contactPersonName) newErrors.contactPersonName = 'Contact person name is required'
          if (!companyData.contactPersonTitle) newErrors.contactPersonTitle = 'Contact person title is required'
          if (!companyData.description) newErrors.description = 'Company description is required'
          break
        case 4:
          if (!companyData.termsAccepted) newErrors.terms = 'You must accept the terms'
          if (!companyData.privacyAccepted) newErrors.privacy = 'You must accept the privacy policy'
          break
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps))
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return

    try {
      const registrationData = selectedRole === 'job_seeker' ? jobSeekerData : companyData
      const result = await register(registrationData as any)

      // If onSuccess is provided, call it (for modal usage)
      if (onSuccess) {
        onSuccess()
      } else {
        // Otherwise, redirect based on role
        const redirectPath = getRoleBasedRedirectPath(selectedRole || 'job_seeker')
        window.location.href = redirectPath
      }
    } catch (error) {
      console.error('Registration failed:', error)
    }
  }

  const renderRoleSelection = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-6"
    >
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold mb-3">Join Our Platform</h2>
        <p className="text-muted-foreground text-lg">
          Choose your role to get started with a personalized experience
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Job Seeker Option */}
        <Card 
          className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
            selectedRole === 'job_seeker' ? 'ring-2 ring-primary border-primary' : ''
          }`}
          onClick={() => {
            setSelectedRole('job_seeker')
            setCurrentStep(2)
          }}
        >
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-bold mb-3">I'm Looking for Jobs</h3>
            <p className="text-muted-foreground mb-4">
              Find your dream job with personalized recommendations and powerful search tools
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Personalized job recommendations</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Direct applications to companies</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Career development resources</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Company Option */}
        <Card 
          className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
            selectedRole === 'company' ? 'ring-2 ring-primary border-primary' : ''
          }`}
          onClick={() => {
            setSelectedRole('company')
            setCurrentStep(2)
          }}
        >
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Building className="w-8 h-8 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="text-xl font-bold mb-3">I'm Hiring Talent</h3>
            <p className="text-muted-foreground mb-4">
              Find the best candidates with advanced recruiting tools and talent analytics
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Advanced candidate search</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Applicant tracking system</span>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span>Employer branding tools</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          Already have an account?{' '}
          <Link href="/auth/login" className="text-primary hover:underline font-medium">
            Sign in here
          </Link>
        </p>
      </div>
    </motion.div>
  )

  if (!selectedRole) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-8">
          {renderRoleSelection()}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {selectedRole === 'job_seeker' ? (
              <User className="w-6 h-6 text-primary" />
            ) : (
              <Building className="w-6 h-6 text-primary" />
            )}
            <div>
              <CardTitle className="text-2xl">
                {selectedRole === 'job_seeker' ? 'Job Seeker Registration' : 'Company Registration'}
              </CardTitle>
              <CardDescription>
                Step {currentStep} of {totalSteps} - {
                  selectedRole === 'job_seeker' 
                    ? ['Account Info', 'Location & Experience', 'Education & Preferences', 'Final Details'][currentStep - 1]
                    : ['Account Info', 'Company Details', 'Contact & Description', 'Final Details'][currentStep - 1]
                }
              </CardDescription>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setSelectedRole(null)
              setCurrentStep(1)
            }}
          >
            Change Role
          </Button>
        </div>

        <Progress value={progress} className="h-2" />
      </CardHeader>

      <CardContent className="p-8">
        <AnimatePresence mode="wait">
          <motion.div
            key={`${selectedRole}-${currentStep}`}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {selectedRole === 'job_seeker' ? renderJobSeekerStep() : renderCompanyStep()}
          </motion.div>
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex items-center justify-between mt-8 pt-6 border-t">
          <div className="flex items-center space-x-2">
            {currentStep > 1 && (
              <Button variant="outline" onClick={handlePrevious}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {currentStep < totalSteps ? (
              <Button onClick={handleNext} className="button-premium">
                Next Step
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button 
                onClick={handleSubmit} 
                disabled={registerLoading}
                className="button-premium"
              >
                {registerLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Creating Account...
                  </>
                ) : (
                  <>
                    <UserCheck className="w-4 h-4 mr-2" />
                    Create Account
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

  const renderJobSeekerStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Mail className="w-12 h-12 text-primary mx-auto mb-3" />
              <h3 className="text-xl font-semibold">Account Information</h3>
              <p className="text-muted-foreground">Create your secure account</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={jobSeekerData.firstName}
                  onChange={(e) => setJobSeekerData(prev => ({ ...prev, firstName: e.target.value }))}
                  className={errors.firstName ? 'border-red-500' : ''}
                />
                {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={jobSeekerData.lastName}
                  onChange={(e) => setJobSeekerData(prev => ({ ...prev, lastName: e.target.value }))}
                  className={errors.lastName ? 'border-red-500' : ''}
                />
                {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                value={jobSeekerData.email}
                onChange={(e) => setJobSeekerData(prev => ({ ...prev, email: e.target.value }))}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="password">Password *</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={jobSeekerData.password}
                    onChange={(e) => setJobSeekerData(prev => ({ ...prev, password: e.target.value }))}
                    className={errors.password ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
                {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
              </div>
              <div>
                <Label htmlFor="confirmPassword">Confirm Password *</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={jobSeekerData.confirmPassword}
                    onChange={(e) => setJobSeekerData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className={errors.confirmPassword ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
                {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="phone">Phone Number (Optional)</Label>
              <Input
                id="phone"
                type="tel"
                value={jobSeekerData.phone}
                onChange={(e) => setJobSeekerData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <MapPin className="w-12 h-12 text-primary mx-auto mb-3" />
              <h3 className="text-xl font-semibold">Location & Experience</h3>
              <p className="text-muted-foreground">Tell us about your location and professional background</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={jobSeekerData.city}
                  onChange={(e) => setJobSeekerData(prev => ({ ...prev, city: e.target.value }))}
                  className={errors.city ? 'border-red-500' : ''}
                />
                {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
              </div>
              <div>
                <Label htmlFor="region">Region/State</Label>
                <Input
                  id="region"
                  value={jobSeekerData.region}
                  onChange={(e) => setJobSeekerData(prev => ({ ...prev, region: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="country">Country *</Label>
                <Input
                  id="country"
                  value={jobSeekerData.country}
                  onChange={(e) => setJobSeekerData(prev => ({ ...prev, country: e.target.value }))}
                  className={errors.country ? 'border-red-500' : ''}
                />
                {errors.country && <p className="text-red-500 text-sm mt-1">{errors.country}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="currentTitle">Current Job Title (Optional)</Label>
              <Input
                id="currentTitle"
                value={jobSeekerData.currentTitle}
                onChange={(e) => setJobSeekerData(prev => ({ ...prev, currentTitle: e.target.value }))}
                placeholder="e.g., Software Engineer, Marketing Manager"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="experienceLevel">Experience Level *</Label>
                <Select
                  value={jobSeekerData.experienceLevel}
                  onValueChange={(value) => setJobSeekerData(prev => ({ ...prev, experienceLevel: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select experience level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="entry">Entry Level (0-2 years)</SelectItem>
                    <SelectItem value="mid">Mid Level (3-5 years)</SelectItem>
                    <SelectItem value="senior">Senior Level (6-10 years)</SelectItem>
                    <SelectItem value="executive">Executive (10+ years)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="yearsOfExperience">Years of Experience</Label>
                <Input
                  id="yearsOfExperience"
                  type="number"
                  min="0"
                  max="50"
                  value={jobSeekerData.yearsOfExperience}
                  onChange={(e) => setJobSeekerData(prev => ({ ...prev, yearsOfExperience: parseInt(e.target.value) || 0 }))}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="industry">Industry *</Label>
              <Select
                value={jobSeekerData.industry}
                onValueChange={(value) => setJobSeekerData(prev => ({ ...prev, industry: value }))}
              >
                <SelectTrigger className={errors.industry ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select your industry" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="sales">Sales</SelectItem>
                  <SelectItem value="design">Design</SelectItem>
                  <SelectItem value="engineering">Engineering</SelectItem>
                  <SelectItem value="consulting">Consulting</SelectItem>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              {errors.industry && <p className="text-red-500 text-sm mt-1">{errors.industry}</p>}
            </div>
          </div>
        )

      default:
        return <div>Step {currentStep} content...</div>
    }
  }

  const renderCompanyStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Building className="w-12 h-12 text-primary mx-auto mb-3" />
              <h3 className="text-xl font-semibold">Company Account</h3>
              <p className="text-muted-foreground">Create your company's hiring account</p>
            </div>

            <div>
              <Label htmlFor="companyName">Company Name *</Label>
              <Input
                id="companyName"
                value={companyData.companyName}
                onChange={(e) => setCompanyData(prev => ({ ...prev, companyName: e.target.value }))}
                className={errors.companyName ? 'border-red-500' : ''}
              />
              {errors.companyName && <p className="text-red-500 text-sm mt-1">{errors.companyName}</p>}
            </div>

            <div>
              <Label htmlFor="website">Company Website (Optional)</Label>
              <Input
                id="website"
                type="url"
                value={companyData.website}
                onChange={(e) => setCompanyData(prev => ({ ...prev, website: e.target.value }))}
                placeholder="https://yourcompany.com"
              />
            </div>

            <div>
              <Label htmlFor="email">Company Email *</Label>
              <Input
                id="email"
                type="email"
                value={companyData.email}
                onChange={(e) => setCompanyData(prev => ({ ...prev, email: e.target.value }))}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="password">Password *</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={companyData.password}
                    onChange={(e) => setCompanyData(prev => ({ ...prev, password: e.target.value }))}
                    className={errors.password ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
                {errors.password && <p className="text-red-500 text-sm mt-1">{errors.password}</p>}
              </div>
              <div>
                <Label htmlFor="confirmPassword">Confirm Password *</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={companyData.confirmPassword}
                    onChange={(e) => setCompanyData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                    className={errors.confirmPassword ? 'border-red-500' : ''}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </Button>
                </div>
                {errors.confirmPassword && <p className="text-red-500 text-sm mt-1">{errors.confirmPassword}</p>}
              </div>
            </div>
          </div>
        )

      default:
        return <div>Company Step {currentStep} content...</div>
    }
  }
