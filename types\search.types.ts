import { ObjectId } from './base.types'
import { Job, EmploymentType, ExperienceLevel } from './job.types'
import { Company, CompanySize } from './company.types'
import { CandidateProfile } from './user.types'

export interface SearchFilters {
  q?: string
  location?: string
  remote?: boolean
  datePosted?: 'today' | 'week' | 'month' | 'all'
  page?: number
  limit?: number
  sort?: string
  order?: 'asc' | 'desc'
}

export interface JobSearchFilters extends SearchFilters {
  title?: string
  company?: string
  employment?: EmploymentType[]
  experience?: ExperienceLevel[]
  category?: string[]
  skills?: string[]
  salaryMin?: number
  salaryMax?: number
  currency?: string
  featured?: boolean
  urgent?: boolean
  benefits?: string[]
  workArrangement?: ('onsite' | 'remote' | 'hybrid')[]
}

export interface CompanySearchFilters extends SearchFilters {
  industry?: string[]
  size?: CompanySize[]
  founded?: {
    min?: number
    max?: number
  }
  rating?: {
    min?: number
    max?: number
  }
  isVerified?: boolean
  isFeatured?: boolean
  hasJobs?: boolean
}

export interface CandidateSearchFilters extends SearchFilters {
  skills?: string[]
  experience?: {
    min?: number
    max?: number
  }
  education?: string[]
  availability?: string[]
  salaryRange?: {
    min?: number
    max?: number
    currency?: string
  }
  languages?: string[]
  certifications?: string[]
  industries?: string[]
  jobTitles?: string[]
}

export interface SearchAggregation {
  field: string
  buckets: {
    key: string
    count: number
    selected?: boolean
  }[]
}

export interface SearchResult<T> {
  results: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  aggregations: SearchAggregation[]
  searchTime: number
  query: string
  filters: Record<string, any>
}

export interface JobSearchResult extends SearchResult<Job> {
  aggregations: SearchAggregation[]
  recommendations?: Job[]
  savedJobs?: ObjectId[]
  appliedJobs?: ObjectId[]
}

export interface CompanySearchResult extends SearchResult<Company> {
  aggregations: SearchAggregation[]
  followedCompanies?: ObjectId[]
}

export interface CandidateSearchResult extends SearchResult<CandidateProfile> {
  aggregations: SearchAggregation[]
  contactedCandidates?: ObjectId[]
  shortlistedCandidates?: ObjectId[]
}

export interface SearchSuggestion {
  type: 'job_title' | 'company' | 'skill' | 'location' | 'category'
  value: string
  count: number
  highlighted?: string
}

export interface SearchAutocomplete {
  query: string
  suggestions: SearchSuggestion[]
  popularSearches: string[]
  recentSearches: string[]
}

export interface SavedSearch {
  id: ObjectId
  user: ObjectId
  name: string
  type: 'job' | 'company' | 'candidate'
  filters: JobSearchFilters | CompanySearchFilters | CandidateSearchFilters
  alertEnabled: boolean
  alertFrequency?: 'immediate' | 'daily' | 'weekly'
  lastRun?: Date
  resultCount?: number
  createdAt: Date
  updatedAt: Date
}

export interface SearchAlert {
  id: ObjectId
  user: ObjectId
  savedSearch: ObjectId
  frequency: 'immediate' | 'daily' | 'weekly'
  isActive: boolean
  lastSent?: Date
  nextSend?: Date
  emailEnabled: boolean
  pushEnabled: boolean
  createdAt: Date
  updatedAt: Date
}

export interface SearchAnalytics {
  period: 'day' | 'week' | 'month'
  startDate: Date
  endDate: Date
  metrics: {
    totalSearches: number
    uniqueUsers: number
    averageResultsPerSearch: number
    clickThroughRate: number
    zeroResultSearches: number
    topQueries: { query: string; count: number }[]
    topFilters: { filter: string; count: number }[]
    searchToApplicationRate: number
    searchToContactRate: number
  }
}

export interface SearchIndex {
  id: ObjectId
  type: 'job' | 'company' | 'candidate'
  entityId: ObjectId
  title: string
  description: string
  keywords: string[]
  location: {
    city?: string
    state?: string
    country?: string
    coordinates?: [number, number]
  }
  metadata: Record<string, any>
  boost: number
  isActive: boolean
  lastIndexed: Date
  createdAt: Date
  updatedAt: Date
}

export interface SearchConfiguration {
  weights: {
    title: number
    description: number
    skills: number
    location: number
    company: number
    keywords: number
  }
  boosts: {
    featured: number
    urgent: number
    verified: number
    premium: number
    recent: number
  }
  filters: {
    enabled: string[]
    defaultSort: string
    maxResults: number
    facetLimits: Record<string, number>
  }
  autocomplete: {
    enabled: boolean
    minQueryLength: number
    maxSuggestions: number
    includePopular: boolean
    includeRecent: boolean
  }
}

export interface SearchQuery {
  q: string
  filters: Record<string, any>
  sort: string
  order: 'asc' | 'desc'
  page: number
  limit: number
  facets: string[]
  highlight: boolean
  explain: boolean
}

export interface SearchResponse<T> {
  hits: {
    total: number
    maxScore: number
    hits: Array<{
      _id: string
      _score: number
      _source: T
      highlight?: Record<string, string[]>
      explanation?: any
    }>
  }
  aggregations: Record<string, {
    buckets: Array<{
      key: string
      doc_count: number
    }>
  }>
  took: number
  timedOut: boolean
}

export interface GeoSearchQuery {
  location: {
    lat: number
    lon: number
  }
  radius: string // e.g., "10km", "50mi"
  unit?: 'km' | 'mi'
}

export interface FacetedSearch {
  facets: {
    [key: string]: {
      type: 'terms' | 'range' | 'date_range' | 'geo_distance'
      field: string
      size?: number
      ranges?: Array<{
        from?: number
        to?: number
        key: string
      }>
    }
  }
  filters: {
    [key: string]: any
  }
}
