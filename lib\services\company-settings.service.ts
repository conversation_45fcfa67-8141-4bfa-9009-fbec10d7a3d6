import { Company } from '@/lib/models/company.model'
import { errorService, ErrorCode } from '@/lib/error-service'
import { cacheService } from './cache.service'
import BaseService from './base.service'
import type {
  CompanySettings,
  CompanySettingsUpdate,
  GetCompanySettingsResponse,
  UpdateCompanySettingsResponse,
  companySettingsSchema,
  updateCompanySettingsSchema
} from '@/types/company-settings.types'

export class CompanySettingsService extends BaseService {
  private readonly CACHE_PREFIX = 'company_settings'
  private readonly CACHE_TTL = 300 // 5 minutes

  /**
   * Get company settings by company ID
   */
  async getCompanySettings(companyId: string): Promise<GetCompanySettingsResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')

      // Check cache first
      const cacheKey = `${this.CACHE_PREFIX}:${companyId}`
      const cachedSettings = await cacheService.get<CompanySettings>(cacheKey)
      
      if (cachedSettings) {
        return {
          success: true,
          data: cachedSettings,
          message: 'Company settings retrieved from cache'
        }
      }

      // Fetch from database
      const company = await Company.findById(companyId).select('settings')
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      // Ensure settings exist with defaults
      const settings = this.ensureDefaultSettings(company.settings || {})

      // Cache the settings
      await cacheService.set(cacheKey, settings, this.CACHE_TTL)

      return {
        success: true,
        data: settings,
        message: 'Company settings retrieved successfully'
      }

    } catch (error) {
      this.handleDatabaseError(error, 'getCompanySettings')
    }
  }

  /**
   * Update company settings
   */
  async updateCompanySettings(
    companyId: string, 
    settingsUpdate: CompanySettingsUpdate,
    userId: string
  ): Promise<UpdateCompanySettingsResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      // Validate settings update
      const validatedUpdate = updateCompanySettingsSchema.parse(settingsUpdate)

      // Check if company exists and user has permission
      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      // Check if user is authorized to update settings
      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Merge with existing settings
      const currentSettings = this.ensureDefaultSettings(company.settings || {})
      const updatedSettings = this.mergeSettings(currentSettings, validatedUpdate)

      // Validate the complete settings object
      const validatedSettings = companySettingsSchema.parse(updatedSettings)

      // Update in database
      company.settings = validatedSettings
      company.updatedAt = new Date()
      await company.save()

      // Update cache
      const cacheKey = `${this.CACHE_PREFIX}:${companyId}`
      await cacheService.set(cacheKey, validatedSettings, this.CACHE_TTL)

      // Invalidate related caches
      await this.invalidateRelatedCaches(companyId)

      this.logOperation('updateCompanySettings', { 
        companyId, 
        userId, 
        updatedFields: Object.keys(validatedUpdate) 
      })

      return {
        success: true,
        data: validatedSettings,
        message: 'Company settings updated successfully'
      }

    } catch (error) {
      this.handleDatabaseError(error, 'updateCompanySettings')
    }
  }

  /**
   * Reset company settings to defaults
   */
  async resetCompanySettings(
    companyId: string,
    userId: string
  ): Promise<UpdateCompanySettingsResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      // Check permissions
      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Get default settings
      const defaultSettings = this.getDefaultSettings()

      // Update in database
      company.settings = defaultSettings
      company.updatedAt = new Date()
      await company.save()

      // Update cache
      const cacheKey = `${this.CACHE_PREFIX}:${companyId}`
      await cacheService.set(cacheKey, defaultSettings, this.CACHE_TTL)

      // Invalidate related caches
      await this.invalidateRelatedCaches(companyId)

      this.logOperation('resetCompanySettings', { companyId, userId })

      return {
        success: true,
        data: defaultSettings,
        message: 'Company settings reset to defaults successfully'
      }

    } catch (error) {
      this.handleDatabaseError(error, 'resetCompanySettings')
    }
  }

  /**
   * Get settings for multiple companies (for admin use)
   */
  async getMultipleCompanySettings(
    companyIds: string[]
  ): Promise<Record<string, CompanySettings>> {
    try {
      const results: Record<string, CompanySettings> = {}

      // Validate all company IDs
      companyIds.forEach(id => this.validateObjectId(id, 'companyId'))

      // Fetch companies with settings
      const companies = await Company.find({
        _id: { $in: companyIds }
      }).select('_id settings')

      companies.forEach(company => {
        results[company._id.toString()] = this.ensureDefaultSettings(company.settings || {})
      })

      return results

    } catch (error) {
      this.handleDatabaseError(error, 'getMultipleCompanySettings')
    }
  }

  /**
   * Ensure settings have all required default values
   */
  private ensureDefaultSettings(settings: Partial<CompanySettings>): CompanySettings {
    return companySettingsSchema.parse(settings)
  }

  /**
   * Get default settings
   */
  private getDefaultSettings(): CompanySettings {
    return companySettingsSchema.parse({})
  }

  /**
   * Deep merge settings objects
   */
  private mergeSettings(
    current: CompanySettings, 
    update: CompanySettingsUpdate
  ): CompanySettings {
    const merged = { ...current }

    Object.keys(update).forEach(key => {
      const updateValue = update[key as keyof CompanySettingsUpdate]
      
      if (updateValue !== undefined) {
        if (typeof updateValue === 'object' && updateValue !== null && !Array.isArray(updateValue)) {
          // Deep merge for nested objects
          merged[key as keyof CompanySettings] = {
            ...merged[key as keyof CompanySettings] as Record<string, unknown>,
            ...updateValue
          } as CompanySettings[keyof CompanySettings]
        } else {
          // Direct assignment for primitives and arrays
          merged[key as keyof CompanySettings] = updateValue as CompanySettings[keyof CompanySettings]
        }
      }
    })

    return merged
  }

  /**
   * Check if user has permission to modify company settings
   */
  private async checkCompanyPermission(
    company: InstanceType<typeof Company>,
    userId: string,
    requiredRoles: string[]
  ): Promise<void> {
    const userObjectId = this.toObjectId(userId)
    
    // Check if user is company admin
    const isAdmin = company.admins.some(adminId => adminId.equals(userObjectId))
    
    if (!isAdmin) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'Insufficient permissions to modify company settings',
        'permissions'
      )
    }
  }

  /**
   * Invalidate related caches
   */
  private async invalidateRelatedCaches(companyId: string): Promise<void> {
    const cacheKeys = [
      `company:${companyId}`,
      `company_profile:${companyId}`,
      `company_public:${companyId}`
    ]

    await Promise.all(
      cacheKeys.map(key => cacheService.delete(key))
    )
  }
}

// Export singleton instance
export const companySettingsService = new CompanySettingsService()
