// lib/services/company-settings.service.ts
import { Company } from '@/lib/models/company.model'
import { errorService, ErrorCode } from '@/lib/error-service'
import { cacheService } from './cache.service'
import BaseService from './base.service'
import type {
  CompanySettings,
  CompanySettingsUpdate,
  GetCompanySettingsResponse,
  UpdateCompanySettingsResponse
} from '@/types/company-settings.types'
import {
  companySettingsSchema,
  updateCompanySettingsSchema
} from '@/types/company-settings.types'

export class CompanySettingsService extends BaseService {
  private readonly CACHE_PREFIX = 'company_settings'
  private readonly CACHE_TTL = 300 // 5 minutes

  /**
   * Get company settings by company ID
   */
  async getCompanySettings(companyId: string): Promise<GetCompanySettingsResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')

      // Check cache first
      const cacheKey = `${this.CACHE_PREFIX}:${companyId}`
      const cachedSettings = await cacheService.get<CompanySettings>(cacheKey)
      
      if (cachedSettings) {
        return {
          success: true,
          data: cachedSettings,
          message: 'Company settings retrieved from cache'
        }
      }

      // Fetch from database
      const company = await Company.findById(companyId).select('settings')
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      // Ensure settings exist with defaults
      const settings = this.ensureDefaultSettings(company.settings || {})

      // Cache the settings
      await cacheService.set(cacheKey, settings, this.CACHE_TTL)

      return {
        success: true,
        data: settings,
        message: 'Company settings retrieved successfully'
      }

    } catch (error) {
      this.handleDatabaseError(error, 'getCompanySettings')
    }
  }

  /**
   * Update company settings
   */
  async updateCompanySettings(
    companyId: string,
    settingsUpdate: CompanySettingsUpdate,
    userId: string
  ): Promise<UpdateCompanySettingsResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      // Validate settings update with custom validation
      const validatedUpdate = await this.validateSettingsUpdate(settingsUpdate)

      // Check if company exists and user has permission
      const company = await Company.findById(companyId)

      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      // Check if user is authorized to update settings
      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Merge with existing settings
      const currentSettings = this.ensureDefaultSettings(company.settings || {})
      const updatedSettings = this.mergeSettings(currentSettings, validatedUpdate)

      // Validate the complete settings object
      const validatedSettings = await this.validateCompleteSettings(updatedSettings)

      // Perform business logic validation
      await this.validateBusinessRules(validatedSettings, company)

      // Update in database
      company.settings = validatedSettings
      company.updatedAt = new Date()
      await company.save()

      // Update cache
      const cacheKey = `${this.CACHE_PREFIX}:${companyId}`
      await cacheService.set(cacheKey, validatedSettings, this.CACHE_TTL)

      // Invalidate related caches
      await this.invalidateRelatedCaches(companyId)

      this.logOperation('updateCompanySettings', {
        companyId,
        userId,
        updatedFields: Object.keys(validatedUpdate)
      })

      return {
        success: true,
        data: validatedSettings,
        message: 'Company settings updated successfully'
      }

    } catch (error) {
      this.handleSettingsError(error, 'updateCompanySettings')
    }
  }

  /**
   * Reset company settings to defaults
   */
  async resetCompanySettings(
    companyId: string,
    userId: string
  ): Promise<UpdateCompanySettingsResponse> {
    try {
      this.validateObjectId(companyId, 'companyId')
      this.validateObjectId(userId, 'userId')

      const company = await Company.findById(companyId)
      
      if (!company) {
        throw errorService.createError(
          ErrorCode.NOT_FOUND,
          'Company not found',
          'companyId'
        )
      }

      // Check permissions
      await this.checkCompanyPermission(company, userId, ['admin', 'owner'])

      // Get default settings
      const defaultSettings = this.getDefaultSettings()

      // Update in database
      company.settings = defaultSettings
      company.updatedAt = new Date()
      await company.save()

      // Update cache
      const cacheKey = `${this.CACHE_PREFIX}:${companyId}`
      await cacheService.set(cacheKey, defaultSettings, this.CACHE_TTL)

      // Invalidate related caches
      await this.invalidateRelatedCaches(companyId)

      this.logOperation('resetCompanySettings', { companyId, userId })

      return {
        success: true,
        data: defaultSettings,
        message: 'Company settings reset to defaults successfully'
      }

    } catch (error) {
      this.handleDatabaseError(error, 'resetCompanySettings')
    }
  }

  /**
   * Get settings for multiple companies (for admin use)
   */
  async getMultipleCompanySettings(
    companyIds: string[]
  ): Promise<Record<string, CompanySettings>> {
    try {
      const results: Record<string, CompanySettings> = {}

      // Validate all company IDs
      companyIds.forEach(id => this.validateObjectId(id, 'companyId'))

      // Fetch companies with settings
      const companies = await Company.find({
        _id: { $in: companyIds }
      }).select('_id settings')

      companies.forEach(company => {
        results[company._id.toString()] = this.ensureDefaultSettings(company.settings || {})
      })

      return results

    } catch (error) {
      this.handleDatabaseError(error, 'getMultipleCompanySettings')
    }
  }

  /**
   * Ensure settings have all required default values
   */
  private ensureDefaultSettings(settings: Partial<CompanySettings>): CompanySettings {
    return companySettingsSchema.parse(settings)
  }

  /**
   * Get default settings
   */
  private getDefaultSettings(): CompanySettings {
    return companySettingsSchema.parse({})
  }

  /**
   * Deep merge settings objects
   */
  private mergeSettings(
    current: CompanySettings,
    update: CompanySettingsUpdate
  ): CompanySettings {
    const merged = { ...current }

    Object.keys(update).forEach(key => {
      const updateValue = update[key as keyof CompanySettingsUpdate]

      if (updateValue !== undefined) {
        if (typeof updateValue === 'object' && updateValue !== null && !Array.isArray(updateValue)) {
          // Deep merge for nested objects
          const currentValue = merged[key as keyof CompanySettings] as Record<string, unknown>
          const mergedValue = {
            ...currentValue,
            ...updateValue
          }
          ;(merged as Record<string, unknown>)[key] = mergedValue
        } else {
          // Direct assignment for primitives and arrays
          ;(merged as Record<string, unknown>)[key] = updateValue
        }
      }
    })

    return merged
  }

  /**
   * Check if user has permission to modify company settings
   */
  private async checkCompanyPermission(
    company: InstanceType<typeof Company>,
    userId: string,
    _requiredRoles: string[]
  ): Promise<void> {
    const userObjectId = this.toObjectId(userId)

    // Check if user is company admin
    const isAdmin = company.admins.some((adminId: unknown) => {
      if (adminId && typeof adminId === 'object' && 'equals' in adminId) {
        return (adminId as { equals: (id: unknown) => boolean }).equals(userObjectId)
      }
      return false
    })
    
    if (!isAdmin) {
      throw errorService.createError(
        ErrorCode.FORBIDDEN,
        'Insufficient permissions to modify company settings',
        'permissions'
      )
    }
  }

  /**
   * Invalidate related caches
   */
  private async invalidateRelatedCaches(companyId: string): Promise<void> {
    const cacheKeys = [
      `company:${companyId}`,
      `company_profile:${companyId}`,
      `company_public:${companyId}`
    ]

    await Promise.all(
      cacheKeys.map(key => cacheService.del(key))
    )
  }

  /**
   * Validate settings update with custom validation
   */
  private async validateSettingsUpdate(settingsUpdate: CompanySettingsUpdate): Promise<CompanySettingsUpdate> {
    try {
      // First use Zod schema validation
      const validatedUpdate = updateCompanySettingsSchema.parse(settingsUpdate)

      // Additional custom validations
      if (validatedUpdate.branding) {
        // Validate color formats
        if (validatedUpdate.branding.primaryColor &&
            !/^#[0-9A-F]{6}$/i.test(validatedUpdate.branding.primaryColor)) {
          throw errorService.createError(
            ErrorCode.VALIDATION_ERROR,
            'Primary color must be a valid hex color code (e.g., #FF5733)',
            'branding.primaryColor'
          )
        }

        if (validatedUpdate.branding.secondaryColor &&
            !/^#[0-9A-F]{6}$/i.test(validatedUpdate.branding.secondaryColor)) {
          throw errorService.createError(
            ErrorCode.VALIDATION_ERROR,
            'Secondary color must be a valid hex color code (e.g., #33FF57)',
            'branding.secondaryColor'
          )
        }

        // Validate image URLs
        if (validatedUpdate.branding.customLogo &&
            !this.isValidImageUrl(validatedUpdate.branding.customLogo)) {
          throw errorService.createError(
            ErrorCode.VALIDATION_ERROR,
            'Logo URL must be a valid image URL',
            'branding.customLogo'
          )
        }

        if (validatedUpdate.branding.customBanner &&
            !this.isValidImageUrl(validatedUpdate.branding.customBanner)) {
          throw errorService.createError(
            ErrorCode.VALIDATION_ERROR,
            'Banner URL must be a valid image URL',
            'branding.customBanner'
          )
        }
      }

      // Validate timezone if provided
      if (validatedUpdate.advanced?.timezone &&
          !this.isValidTimezone(validatedUpdate.advanced.timezone)) {
        throw errorService.createError(
          ErrorCode.VALIDATION_ERROR,
          'Invalid timezone format',
          'advanced.timezone'
        )
      }

      return validatedUpdate
    } catch (error) {
      if (error instanceof Error) {
        // Handle Zod validation errors
        if (error.name === 'ZodError') {
          const zodError = error as unknown as { errors: Array<{ path: string[], message: string }> }
          const firstError = zodError.errors[0]
          const path = firstError.path.join('.')

          throw errorService.createError(
            ErrorCode.VALIDATION_ERROR,
            firstError.message,
            path
          )
        }
      }

      // Re-throw if it's already an AppError
      throw error
    }
  }

  /**
   * Validate complete settings object
   */
  private async validateCompleteSettings(settings: CompanySettings): Promise<CompanySettings> {
    try {
      // Use Zod schema validation
      return companySettingsSchema.parse(settings)
    } catch (error) {
      if (error instanceof Error) {
        // Handle Zod validation errors
        if (error.name === 'ZodError') {
          const zodError = error as unknown as { errors: Array<{ path: string[], message: string }> }
          const firstError = zodError.errors[0]
          const path = firstError.path.join('.')

          throw errorService.createError(
            ErrorCode.VALIDATION_ERROR,
            firstError.message,
            path
          )
        }
      }

      // Re-throw if it's already an AppError
      throw error
    }
  }

  /**
   * Validate business rules for settings
   */
  private async validateBusinessRules(settings: CompanySettings, company: InstanceType<typeof Company>): Promise<void> {
    // Check if auto-reject days is valid for the company's subscription plan
    if (settings.autoRejectAfterDays) {
      const maxAllowedDays = this.getMaxAutoRejectDaysForPlan(company)
      if (settings.autoRejectAfterDays > maxAllowedDays) {
        throw errorService.createError(
          ErrorCode.VALIDATION_ERROR,
          `Auto-reject days cannot exceed ${maxAllowedDays} for your current plan`,
          'autoRejectAfterDays'
        )
      }
    }

    // Check if integration settings are valid for the company's subscription
    if (settings.integrations.atsIntegration?.enabled) {
      const canUseAtsIntegration = this.canUseAtsIntegration(company)
      if (!canUseAtsIntegration) {
        throw errorService.createError(
          ErrorCode.FORBIDDEN,
          'ATS integration is not available on your current plan',
          'integrations.atsIntegration'
        )
      }
    }
  }

  /**
   * Handle settings-specific errors
   */
  private handleSettingsError(error: unknown, operation: string): never {
    console.error(`[${this.constructor.name}] Error in ${operation}:`, error)

    // If it's already an AppError, just throw it
    if (typeof error === 'object' && error !== null && 'code' in error) {
      throw error
    }

    // Handle database errors
    this.handleDatabaseError(error, operation)
  }

  /**
   * Check if a URL is a valid image URL
   */
  private isValidImageUrl(url: string): boolean {
    // Basic URL validation
    try {
      new URL(url)
    } catch {
      return false
    }

    // Check if URL ends with common image extensions
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp']
    return imageExtensions.some(ext => url.toLowerCase().endsWith(ext)) ||
           url.includes('/image/') ||
           url.includes('/images/') ||
           url.includes('data:image/')
  }

  /**
   * Check if a timezone is valid
   */
  private isValidTimezone(timezone: string): boolean {
    // Basic timezone validation
    return /^[A-Za-z_]+\/[A-Za-z_]+$/.test(timezone) || timezone === 'UTC'
  }

  /**
   * Get maximum auto-reject days based on company plan
   */
  private getMaxAutoRejectDaysForPlan(_company: InstanceType<typeof Company>): number {
    // This would normally check the company's subscription plan
    // For now, return a default value
    return 365
  }

  /**
   * Check if company can use ATS integration
   */
  private canUseAtsIntegration(_company: InstanceType<typeof Company>): boolean {
    // This would normally check the company's subscription plan
    // For now, return true
    return true
  }
}

// Export singleton instance
export const companySettingsService = new CompanySettingsService()
