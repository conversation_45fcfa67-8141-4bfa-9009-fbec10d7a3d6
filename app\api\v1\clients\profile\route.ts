import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/db'
import { clientService } from '@/lib/services/client.service'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id

    // Get client profile
    const client = await clientService.getClientByUserId(userId)

    return NextResponse.json({
      success: true,
      data: client
    })

  } catch (error) {
    console.error('Get client profile error:', error)
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const updateData = await request.json()

    // Update client profile
    const updatedClient = await clientService.updateClientByUserId(userId, updateData)

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      data: updatedClient
    })

  } catch (error) {
    console.error('Update client profile error:', error)
    
    if (error instanceof Error && error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Client profile not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to update profile' },
      { status: 500 }
    )
  }
}
