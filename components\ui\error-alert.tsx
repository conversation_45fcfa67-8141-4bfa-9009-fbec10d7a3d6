'use client'

import React from 'react'
import { AlertTriangle, XCircle, AlertCircle, Info, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { motion, AnimatePresence } from 'framer-motion'

type ErrorType = 'error' | 'warning' | 'info' | 'success'

interface ErrorAlertProps {
  type?: ErrorType
  title?: string
  message: string
  dismissible?: boolean
  onDismiss?: () => void
  className?: string
  actions?: React.ReactNode
}

export function ErrorAlert({
  type = 'error',
  title,
  message,
  dismissible = false,
  onDismiss,
  className,
  actions
}: ErrorAlertProps) {
  const getTypeConfig = () => {
    switch (type) {
      case 'error':
        return {
          icon: <XCircle className="w-5 h-5" />,
          bgColor: 'bg-red-50 border-red-200',
          textColor: 'text-red-800',
          iconColor: 'text-red-600',
          defaultTitle: 'Error'
        }
      case 'warning':
        return {
          icon: <AlertTriangle className="w-5 h-5" />,
          bgColor: 'bg-yellow-50 border-yellow-200',
          textColor: 'text-yellow-800',
          iconColor: 'text-yellow-600',
          defaultTitle: 'Warning'
        }
      case 'info':
        return {
          icon: <Info className="w-5 h-5" />,
          bgColor: 'bg-blue-50 border-blue-200',
          textColor: 'text-blue-800',
          iconColor: 'text-blue-600',
          defaultTitle: 'Information'
        }
      case 'success':
        return {
          icon: <AlertCircle className="w-5 h-5" />,
          bgColor: 'bg-green-50 border-green-200',
          textColor: 'text-green-800',
          iconColor: 'text-green-600',
          defaultTitle: 'Success'
        }
    }
  }

  const config = getTypeConfig()

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={cn(
        'border rounded-lg p-4',
        config.bgColor,
        className
      )}
    >
      <div className="flex items-start space-x-3">
        <div className={cn('flex-shrink-0', config.iconColor)}>
          {config.icon}
        </div>
        
        <div className="flex-1 min-w-0">
          {title && (
            <h3 className={cn('font-medium mb-1', config.textColor)}>
              {title}
            </h3>
          )}
          <p className={cn('text-sm', config.textColor)}>
            {message}
          </p>
          
          {actions && (
            <div className="mt-3">
              {actions}
            </div>
          )}
        </div>

        {dismissible && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className={cn(
              'flex-shrink-0 h-6 w-6 p-0',
              config.textColor,
              'hover:bg-black/5'
            )}
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>
    </motion.div>
  )
}

// Inline error for form fields
export function InlineError({ 
  message, 
  className 
}: { 
  message?: string
  className?: string 
}) {
  if (!message) return null

  return (
    <motion.p
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className={cn(
        'text-sm text-red-600 mt-1 flex items-center space-x-1',
        className
      )}
    >
      <AlertCircle className="w-3 h-3 flex-shrink-0" />
      <span>{message}</span>
    </motion.p>
  )
}

// Error list for multiple errors
export function ErrorList({ 
  errors, 
  title = 'Please fix the following errors:',
  className 
}: { 
  errors: string[]
  title?: string
  className?: string 
}) {
  if (!errors.length) return null

  return (
    <ErrorAlert
      type="error"
      title={title}
      message=""
      className={className}
      actions={
        <ul className="list-disc list-inside space-y-1 text-sm text-red-700">
          {errors.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      }
    />
  )
}

// Toast-style error notification
export function ErrorToast({
  message,
  title,
  onDismiss,
  autoClose = true,
  duration = 5000
}: {
  message: string
  title?: string
  onDismiss?: () => void
  autoClose?: boolean
  duration?: number
}) {
  React.useEffect(() => {
    if (autoClose && onDismiss) {
      const timer = setTimeout(onDismiss, duration)
      return () => clearTimeout(timer)
    }
  }, [autoClose, duration, onDismiss])

  return (
    <motion.div
      initial={{ opacity: 0, x: 300 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 300 }}
      className="fixed top-4 right-4 z-50 w-96 max-w-sm"
    >
      <ErrorAlert
        type="error"
        title={title}
        message={message}
        dismissible
        onDismiss={onDismiss}
        className="shadow-lg"
      />
    </motion.div>
  )
}

// Network error component
export function NetworkError({ 
  onRetry,
  message = 'Unable to connect to the server. Please check your internet connection and try again.'
}: {
  onRetry?: () => void
  message?: string
}) {
  return (
    <ErrorAlert
      type="error"
      title="Connection Error"
      message={message}
      actions={
        onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="mt-2 border-red-300 text-red-700 hover:bg-red-100"
          >
            Try Again
          </Button>
        )
      }
    />
  )
}

// 404 Error component
export function NotFoundError({
  title = 'Page Not Found',
  message = 'The page you are looking for does not exist.',
  onGoHome
}: {
  title?: string
  message?: string
  onGoHome?: () => void
}) {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-6">
        <AlertTriangle className="w-8 h-8 text-gray-600" />
      </div>
      <h1 className="text-2xl font-bold text-gray-900 mb-2">{title}</h1>
      <p className="text-gray-600 mb-6">{message}</p>
      {onGoHome && (
        <Button onClick={onGoHome}>
          Go to Homepage
        </Button>
      )}
    </div>
  )
}
