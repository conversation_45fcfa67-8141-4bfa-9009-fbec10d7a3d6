'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import { Job } from '@/lib/job-data'
import { 
  Upload,
  FileText,
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  GraduationCap,
  Calendar,
  CheckCircle,
  X,
  Send,
  AlertCircle,
  Star,
  Clock,
  DollarSign
} from 'lucide-react'

interface JobApplicationModalProps {
  job: Job | null
  isOpen: boolean
  onClose: () => void
  onSubmit: (applicationData: any) => void
}

export function JobApplicationModal({ 
  job, 
  isOpen, 
  onClose, 
  onSubmit 
}: JobApplicationModalProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    location: '',
    
    // Professional Information
    currentTitle: '',
    experience: '',
    expectedSalary: '',
    availableDate: '',
    
    // Application Details
    coverLetter: '',
    resume: null as File | null,
    portfolio: '',
    linkedIn: '',
    
    // Additional Information
    workAuthorization: '',
    willingToRelocate: false,
    remoteWork: false,
    
    // Agreements
    termsAccepted: false,
    privacyAccepted: false
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Local storage key for this specific job application
  const storageKey = `job-application-${job?.id || 'draft'}`
  const [hasRestoredData, setHasRestoredData] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  // Load saved data from localStorage when modal opens
  useEffect(() => {
    if (isOpen && job) {
      const savedData = localStorage.getItem(storageKey)
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData)
          setFormData(prev => ({ ...prev, ...parsedData }))
          setHasRestoredData(true)
          // Auto-dismiss the notification after 5 seconds
          setTimeout(() => setHasRestoredData(false), 5000)
        } catch (error) {
          console.error('Error loading saved application data:', error)
        }
      }
    }
  }, [isOpen, job, storageKey])

  // Save data to localStorage whenever form data changes
  useEffect(() => {
    if (isOpen && job) {
      const dataToSave = { ...formData }
      // Don't save the resume file to localStorage (too large)
      delete dataToSave.resume
      localStorage.setItem(storageKey, JSON.stringify(dataToSave))
      setLastSaved(new Date())
    }
  }, [formData, isOpen, job, storageKey])

  // Clear saved data when application is successfully submitted
  const clearSavedData = () => {
    localStorage.removeItem(storageKey)
  }

  if (!job) return null

  const totalSteps = 4
  const progress = (currentStep / totalSteps) * 100

  const formatSalary = (salary: Job['salary']) => {
    const min = salary.min.toLocaleString()
    const max = salary.max.toLocaleString()
    const period = salary.period === 'year' ? '/year' : salary.period === 'month' ? '/month' : '/hour'
    return `$${min} - $${max}${period}`
  }

  const validateStep = (step: number) => {
    const newErrors: Record<string, string> = {}

    switch (step) {
      case 1:
        if (!formData.firstName) newErrors.firstName = 'First name is required'
        if (!formData.lastName) newErrors.lastName = 'Last name is required'
        if (!formData.email) newErrors.email = 'Email is required'
        if (!formData.phone) newErrors.phone = 'Phone number is required'
        if (!formData.location) newErrors.location = 'Location is required'
        break
      case 2:
        if (!formData.currentTitle) newErrors.currentTitle = 'Current title is required'
        if (!formData.experience) newErrors.experience = 'Experience level is required'
        if (!formData.expectedSalary) newErrors.expectedSalary = 'Expected salary is required'
        if (!formData.availableDate) newErrors.availableDate = 'Available date is required'
        break
      case 3:
        if (!formData.coverLetter) {
          newErrors.coverLetter = 'Cover letter is required'
        } else if (formData.coverLetter.length < 100) {
          newErrors.coverLetter = 'Cover letter must be at least 100 characters long'
        }
        if (!formData.resume) newErrors.resume = 'Resume is required'
        break
      case 4:
        if (!formData.workAuthorization) newErrors.workAuthorization = 'Work authorization is required'
        if (!formData.termsAccepted) newErrors.termsAccepted = 'You must accept the terms'
        if (!formData.privacyAccepted) newErrors.privacyAccepted = 'You must accept the privacy policy'
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps))
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return

    setIsSubmitting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    onSubmit({
      jobId: job.id,
      jobTitle: job.title,
      companyName: job.company.name,
      applicationData: formData,
      submittedAt: new Date().toISOString()
    })

    setIsSubmitting(false)

    // Clear saved data from localStorage
    clearSavedData()

    onClose()

    // Reset form
    setCurrentStep(1)
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      location: '',
      currentTitle: '',
      experience: '',
      expectedSalary: '',
      availableDate: '',
      coverLetter: '',
      resume: null,
      portfolio: '',
      linkedIn: '',
      workAuthorization: '',
      willingToRelocate: false,
      remoteWork: false,
      termsAccepted: false,
      privacyAccepted: false
    })
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setFormData(prev => ({ ...prev, resume: file }))
    }
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <User className="w-12 h-12 text-primary mx-auto mb-3" />
              <h3 className="text-xl font-semibold">Personal Information</h3>
              <p className="text-muted-foreground">Tell us about yourself</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  className={errors.firstName ? 'border-red-500' : ''}
                />
                {errors.firstName && <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>}
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  className={errors.lastName ? 'border-red-500' : ''}
                />
                {errors.lastName && <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="email">Email Address *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
              </div>
              <div>
                <Label htmlFor="location">Current Location *</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  className={errors.location ? 'border-red-500' : ''}
                  placeholder="City, State/Country"
                />
                {errors.location && <p className="text-red-500 text-sm mt-1">{errors.location}</p>}
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <Briefcase className="w-12 h-12 text-primary mx-auto mb-3" />
              <h3 className="text-xl font-semibold">Professional Information</h3>
              <p className="text-muted-foreground">Share your professional background</p>
            </div>

            <div>
              <Label htmlFor="currentTitle">Current Job Title *</Label>
              <Input
                id="currentTitle"
                value={formData.currentTitle}
                onChange={(e) => setFormData(prev => ({ ...prev, currentTitle: e.target.value }))}
                className={errors.currentTitle ? 'border-red-500' : ''}
                placeholder="e.g., Senior Software Engineer"
              />
              {errors.currentTitle && <p className="text-red-500 text-sm mt-1">{errors.currentTitle}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="experience">Experience Level *</Label>
                <Select value={formData.experience} onValueChange={(value) => setFormData(prev => ({ ...prev, experience: value }))}>
                  <SelectTrigger className={errors.experience ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select experience level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="entry">Entry Level (0-2 years)</SelectItem>
                    <SelectItem value="mid">Mid Level (3-5 years)</SelectItem>
                    <SelectItem value="senior">Senior Level (6-10 years)</SelectItem>
                    <SelectItem value="executive">Executive (10+ years)</SelectItem>
                  </SelectContent>
                </Select>
                {errors.experience && <p className="text-red-500 text-sm mt-1">{errors.experience}</p>}
              </div>
              <div>
                <Label htmlFor="expectedSalary">Expected Salary *</Label>
                <Input
                  id="expectedSalary"
                  value={formData.expectedSalary}
                  onChange={(e) => setFormData(prev => ({ ...prev, expectedSalary: e.target.value }))}
                  className={errors.expectedSalary ? 'border-red-500' : ''}
                  placeholder="e.g., $120,000"
                />
                {errors.expectedSalary && <p className="text-red-500 text-sm mt-1">{errors.expectedSalary}</p>}
              </div>
            </div>

            <div>
              <Label htmlFor="availableDate">Available Start Date *</Label>
              <Input
                id="availableDate"
                type="date"
                value={formData.availableDate}
                onChange={(e) => setFormData(prev => ({ ...prev, availableDate: e.target.value }))}
                className={errors.availableDate ? 'border-red-500' : ''}
              />
              {errors.availableDate && <p className="text-red-500 text-sm mt-1">{errors.availableDate}</p>}
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <FileText className="w-12 h-12 text-primary mx-auto mb-3" />
              <h3 className="text-xl font-semibold">Application Materials</h3>
              <p className="text-muted-foreground">Upload your documents and write your cover letter</p>
            </div>

            <div>
              <Label htmlFor="resume">Resume/CV *</Label>
              <div className="mt-2">
                <label htmlFor="resume-upload" className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                  formData.resume
                    ? 'border-green-300 bg-green-50 dark:bg-green-950/20'
                    : errors.resume
                      ? 'border-red-300 bg-red-50 dark:bg-red-950/20'
                      : 'border-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}>
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    {formData.resume ? (
                      <>
                        <CheckCircle className="w-8 h-8 mb-2 text-green-500" />
                        <p className="mb-2 text-sm text-green-700 dark:text-green-400 font-medium">
                          {formData.resume.name}
                        </p>
                        <p className="text-xs text-green-600 dark:text-green-500">
                          File uploaded successfully • Click to change
                        </p>
                      </>
                    ) : (
                      <>
                        <Upload className="w-8 h-8 mb-2 text-gray-500" />
                        <p className="mb-2 text-sm text-gray-500">
                          Click to upload resume or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">PDF, DOC, DOCX (MAX. 10MB)</p>
                      </>
                    )}
                  </div>
                  <input
                    id="resume-upload"
                    type="file"
                    className="hidden"
                    accept=".pdf,.doc,.docx"
                    onChange={handleFileUpload}
                  />
                </label>
              </div>
              {errors.resume && <p className="text-red-500 text-sm mt-1">{errors.resume}</p>}
              {!errors.resume && !formData.resume && (
                <p className="text-xs text-muted-foreground mt-2">
                  💡 Tip: Make sure your resume is up-to-date and highlights relevant experience for this role.
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="coverLetter">Cover Letter *</Label>
              <div className="mt-2">
                <Textarea
                  id="coverLetter"
                  value={formData.coverLetter}
                  onChange={(e) => {
                    const value = e.target.value
                    if (value.length <= 2000) {
                      setFormData(prev => ({ ...prev, coverLetter: value }))
                    }
                  }}
                  className={`min-h-[250px] max-h-[400px] resize-y ${errors.coverLetter ? 'border-red-500' : ''}`}
                  maxLength={2000}
                  placeholder={`Dear Hiring Manager,

I am writing to express my strong interest in the ${job.title} position at ${job.company.name}.

[Explain why you're interested in this role and company]

[Highlight your relevant experience and skills]

[Mention specific achievements or projects that demonstrate your qualifications]

[Explain what you can contribute to the team and company]

Thank you for considering my application. I look forward to discussing how my skills and experience can contribute to your team's success.

Best regards,
[Your Name]`}
                />
                <div className="flex justify-between items-center mt-2">
                  <p className="text-xs text-muted-foreground">
                    {formData.coverLetter.length}/2000 characters
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Minimum 100 characters required
                  </p>
                </div>
              </div>
              {errors.coverLetter && <p className="text-red-500 text-sm mt-1">{errors.coverLetter}</p>}
              {!errors.coverLetter && formData.coverLetter.length < 100 && (
                <p className="text-xs text-muted-foreground mt-2">
                  💡 Tip: A good cover letter should be at least 100 characters. Explain why you're interested in this role and what makes you a great fit.
                </p>
              )}
              {!errors.coverLetter && formData.coverLetter.length >= 100 && (
                <p className="text-xs text-green-600 dark:text-green-400 mt-2">
                  ✅ Great! Your cover letter looks good.
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="portfolio">Portfolio URL (Optional)</Label>
                <Input
                  id="portfolio"
                  value={formData.portfolio}
                  onChange={(e) => setFormData(prev => ({ ...prev, portfolio: e.target.value }))}
                  placeholder="https://yourportfolio.com"
                />
              </div>
              <div>
                <Label htmlFor="linkedIn">LinkedIn Profile (Optional)</Label>
                <Input
                  id="linkedIn"
                  value={formData.linkedIn}
                  onChange={(e) => setFormData(prev => ({ ...prev, linkedIn: e.target.value }))}
                  placeholder="https://linkedin.com/in/yourprofile"
                />
              </div>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <CheckCircle className="w-12 h-12 text-primary mx-auto mb-3" />
              <h3 className="text-xl font-semibold">Final Details</h3>
              <p className="text-muted-foreground">Complete your application</p>
            </div>

            <div>
              <Label htmlFor="workAuthorization">Work Authorization *</Label>
              <Select value={formData.workAuthorization} onValueChange={(value) => setFormData(prev => ({ ...prev, workAuthorization: value }))}>
                <SelectTrigger className={errors.workAuthorization ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select work authorization status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="citizen">US Citizen</SelectItem>
                  <SelectItem value="permanent">Permanent Resident</SelectItem>
                  <SelectItem value="visa">Work Visa Holder</SelectItem>
                  <SelectItem value="sponsorship">Require Sponsorship</SelectItem>
                </SelectContent>
              </Select>
              {errors.workAuthorization && <p className="text-red-500 text-sm mt-1">{errors.workAuthorization}</p>}
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="willingToRelocate"
                  checked={formData.willingToRelocate}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, willingToRelocate: checked as boolean }))}
                />
                <Label htmlFor="willingToRelocate">I am willing to relocate for this position</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remoteWork"
                  checked={formData.remoteWork}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, remoteWork: checked as boolean }))}
                />
                <Label htmlFor="remoteWork">I am interested in remote work opportunities</Label>
              </div>
            </div>

            <div className="space-y-4 pt-4 border-t">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="termsAccepted"
                  checked={formData.termsAccepted}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, termsAccepted: checked as boolean }))}
                  className={errors.termsAccepted ? 'border-red-500' : ''}
                />
                <Label htmlFor="termsAccepted" className="text-sm">
                  I agree to the <a href="#" className="text-primary hover:underline">Terms of Service</a> and acknowledge that my information will be shared with the employer *
                </Label>
              </div>
              {errors.termsAccepted && <p className="text-red-500 text-sm">{errors.termsAccepted}</p>}

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="privacyAccepted"
                  checked={formData.privacyAccepted}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, privacyAccepted: checked as boolean }))}
                  className={errors.privacyAccepted ? 'border-red-500' : ''}
                />
                <Label htmlFor="privacyAccepted" className="text-sm">
                  I agree to the <a href="#" className="text-primary hover:underline">Privacy Policy</a> *
                </Label>
              </div>
              {errors.privacyAccepted && <p className="text-red-500 text-sm">{errors.privacyAccepted}</p>}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        <div className="flex flex-col h-full max-h-[90vh]">
          {/* Header */}
          <DialogHeader className="p-6 pb-4 border-b bg-gradient-to-r from-primary/5 to-primary/10">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <Avatar className="w-12 h-12 border-2 border-primary/20">
                  <AvatarImage src={job.company.logo} alt={job.company.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold">
                    {job.company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                
                <div>
                  <DialogTitle className="text-xl font-bold mb-1">Apply for {job.title}</DialogTitle>
                  <p className="text-primary font-medium">{job.company.name}</p>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-2">
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-3 h-3" />
                      <span>{job.location}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-3 h-3" />
                      <span>{formatSalary(job.salary)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{job.type}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Progress Bar */}
            <div className="mt-6">
              <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
                <span>Step {currentStep} of {totalSteps}</span>
                <div className="flex items-center space-x-3">
                  {lastSaved && (
                    <span className="text-xs text-green-600 dark:text-green-400">
                      Auto-saved {lastSaved.toLocaleTimeString()}
                    </span>
                  )}
                  <span>{Math.round(progress)}% Complete</span>
                </div>
              </div>
              <Progress value={progress} className="h-2" />
            </div>

            {/* Data Restored Notification */}
            {hasRestoredData && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg"
              >
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-blue-500" />
                  <span className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                    Your previous application data has been restored
                  </span>
                </div>
                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  We automatically saved your progress to prevent data loss.
                </p>
              </motion.div>
            )}
          </DialogHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStep()}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Footer */}
          <div className="p-6 border-t bg-muted/30">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                {job.applicants > 0 && (
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4" />
                    <span>{job.applicants} other candidates have applied</span>
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    if (confirm('Are you sure you want to clear all saved data? This action cannot be undone.')) {
                      clearSavedData()
                      setFormData({
                        firstName: '',
                        lastName: '',
                        email: '',
                        phone: '',
                        location: '',
                        currentTitle: '',
                        experience: '',
                        expectedSalary: '',
                        availableDate: '',
                        coverLetter: '',
                        resume: null,
                        portfolio: '',
                        linkedIn: '',
                        workAuthorization: '',
                        willingToRelocate: false,
                        remoteWork: false,
                        termsAccepted: false,
                        privacyAccepted: false
                      })
                      setCurrentStep(1)
                    }
                  }}
                  className="text-xs text-muted-foreground hover:text-destructive"
                >
                  Clear Saved Data
                </Button>
              </div>
              
              <div className="flex items-center space-x-3">
                {currentStep > 1 && (
                  <Button variant="outline" onClick={handlePrevious}>
                    Previous
                  </Button>
                )}
                
                {currentStep < totalSteps ? (
                  <Button onClick={handleNext} className="button-premium">
                    Next Step
                  </Button>
                ) : (
                  <Button 
                    onClick={handleSubmit} 
                    disabled={isSubmitting}
                    className="button-premium"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Submit Application
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
