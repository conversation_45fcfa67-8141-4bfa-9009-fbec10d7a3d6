'use client'

import { useFormContext } from 'react-hook-form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Mail, Smartphone, Bell } from 'lucide-react'
import type { CompanySettings } from '@/types/company-settings.types'

export function NotificationSettings() {
  const { register, watch } = useFormContext<CompanySettings>()

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Email Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Email Notifications
            </CardTitle>
            <CardDescription>
              Configure which email notifications you want to receive
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>New Applications</Label>
                <p className="text-sm text-muted-foreground">
                  Get notified when someone applies to your jobs
                </p>
              </div>
              <Switch
                {...register('emailNotifications.newApplications')}
                checked={watch('emailNotifications.newApplications')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Application Updates</Label>
                <p className="text-sm text-muted-foreground">
                  Updates on application status changes
                </p>
              </div>
              <Switch
                {...register('emailNotifications.applicationUpdates')}
                checked={watch('emailNotifications.applicationUpdates')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Interview Reminders</Label>
                <p className="text-sm text-muted-foreground">
                  Reminders for scheduled interviews
                </p>
              </div>
              <Switch
                {...register('emailNotifications.interviewReminders')}
                checked={watch('emailNotifications.interviewReminders')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Job Expiring</Label>
                <p className="text-sm text-muted-foreground">
                  Alerts when your job postings are about to expire
                </p>
              </div>
              <Switch
                {...register('emailNotifications.jobExpiring')}
                checked={watch('emailNotifications.jobExpiring')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Weekly Reports</Label>
                <p className="text-sm text-muted-foreground">
                  Weekly summary of your recruitment activity
                </p>
              </div>
              <Switch
                {...register('emailNotifications.weeklyReports')}
                checked={watch('emailNotifications.weeklyReports')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Marketing Emails</Label>
                <p className="text-sm text-muted-foreground">
                  Product updates and promotional content
                </p>
              </div>
              <Switch
                {...register('emailNotifications.marketingEmails')}
                checked={watch('emailNotifications.marketingEmails')}
              />
            </div>
          </CardContent>
        </Card>

        {/* Push Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              Push Notifications
            </CardTitle>
            <CardDescription>
              Configure push notifications for mobile and desktop
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>New Applications</Label>
                <p className="text-sm text-muted-foreground">
                  Instant notifications for new applications
                </p>
              </div>
              <Switch
                {...register('pushNotifications.newApplications')}
                checked={watch('pushNotifications.newApplications')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Urgent Updates</Label>
                <p className="text-sm text-muted-foreground">
                  Critical updates that need immediate attention
                </p>
              </div>
              <Switch
                {...register('pushNotifications.urgentUpdates')}
                checked={watch('pushNotifications.urgentUpdates')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Daily Digest</Label>
                <p className="text-sm text-muted-foreground">
                  Daily summary of activity and updates
                </p>
              </div>
              <Switch
                {...register('pushNotifications.dailyDigest')}
                checked={watch('pushNotifications.dailyDigest')}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Team Mentions</Label>
                <p className="text-sm text-muted-foreground">
                  When team members mention you in comments
                </p>
              </div>
              <Switch
                {...register('pushNotifications.teamMentions')}
                checked={watch('pushNotifications.teamMentions')}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notification Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Additional notification settings and preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Notification Frequency</Label>
              <p className="text-sm text-muted-foreground">
                How often you want to receive bundled notifications
              </p>
              <div className="flex gap-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="immediate"
                    name="frequency"
                    value="immediate"
                    className="h-4 w-4"
                  />
                  <Label htmlFor="immediate" className="text-sm">Immediate</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="hourly"
                    name="frequency"
                    value="hourly"
                    className="h-4 w-4"
                  />
                  <Label htmlFor="hourly" className="text-sm">Hourly</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="daily"
                    name="frequency"
                    value="daily"
                    className="h-4 w-4"
                    defaultChecked
                  />
                  <Label htmlFor="daily" className="text-sm">Daily</Label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Quiet Hours</Label>
              <p className="text-sm text-muted-foreground">
                Time range when notifications should be muted
              </p>
              <div className="flex gap-2 items-center">
                <input
                  type="time"
                  defaultValue="22:00"
                  className="px-3 py-2 border rounded-md text-sm"
                />
                <span className="text-sm text-muted-foreground">to</span>
                <input
                  type="time"
                  defaultValue="08:00"
                  className="px-3 py-2 border rounded-md text-sm"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
