import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'
import { errorService } from '@/lib/errors/error-service'
import { ErrorCode } from '@/lib/errors/error-types'

export interface CompanyProfile {
  id: string
  name: string
  slug: string
  description?: string
  website?: string
  industry?: string
  size?: string
  location: {
    address?: string
    city?: string
    state?: string
    country?: string
    coordinates?: {
      lat: number
      lng: number
    }
  }
  logo?: string
  banner?: string
  socialLinks?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
  isVerified: boolean
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CreateCompanyRequest {
  name: string
  description?: string
  website?: string
  industry?: string
  size?: string
  location?: {
    address?: string
    city?: string
    state?: string
    country?: string
  }
  logo?: string
  socialLinks?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
}

export interface UpdateCompanyRequest {
  name?: string
  description?: string
  website?: string
  industry?: string
  size?: string
  location?: {
    address?: string
    city?: string
    state?: string
    country?: string
  }
  logo?: string
  banner?: string
  socialLinks?: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
  }
}

export class CompanyService {
  /**
   * Create a new company
   */
  async createCompany(companyData: CreateCompanyRequest, adminUserId: string): Promise<CompanyProfile> {
    try {
      // Check if company name already exists
      const existingCompany = await Company.findOne({ 
        name: { $regex: new RegExp(`^${companyData.name}$`, 'i') }
      })
      
      if (existingCompany) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'A company with this name already exists',
          'name'
        )
      }
      
      // Generate slug from company name
      const slug = this.generateSlug(companyData.name)
      
      // Create company
      const company = new Company({
        ...companyData,
        slug,
        isActive: true,
        isVerified: false
      })
      
      await company.save()
      
      // Update user to be company admin
      await User.findByIdAndUpdate(adminUserId, {
        companyId: company._id,
        role: 'company_admin'
      })
      
      return this.formatCompanyProfile(company)
      
    } catch (error: unknown) {
      if (typeof error === 'object' && error !== null && 'code' in error && (error as Record<string, unknown>).code === 11000) {
        throw errorService.createError(
          ErrorCode.DUPLICATE_ENTRY,
          'A company with this name already exists',
          'name'
        )
      }
      throw error
    }
  }

  /**
   * Get company by ID
   */
  async getCompanyById(companyId: string): Promise<CompanyProfile> {
    const company = await Company.findById(companyId)
    
    if (!company) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'Company not found',
        'companyId'
      )
    }
    
    return this.formatCompanyProfile(company)
  }

  /**
   * Get company by slug
   */
  async getCompanyBySlug(slug: string): Promise<CompanyProfile> {
    const company = await Company.findOne({ slug })
    
    if (!company) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'Company not found',
        'slug'
      )
    }
    
    return this.formatCompanyProfile(company)
  }

  /**
   * Update company profile
   */
  async updateCompany(companyId: string, updateData: UpdateCompanyRequest): Promise<CompanyProfile> {
    const company = await Company.findById(companyId)
    
    if (!company) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'Company not found',
        'companyId'
      )
    }
    
    // Update fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof UpdateCompanyRequest] !== undefined) {
        if (key === 'location' && updateData.location) {
          company.location = { ...company.location, ...updateData.location }
        } else if (key === 'socialLinks' && updateData.socialLinks) {
          company.socialLinks = { ...company.socialLinks, ...updateData.socialLinks }
        } else {
          ;(company as Record<string, unknown>)[key] = updateData[key as keyof UpdateCompanyRequest]
        }
      }
    })
    
    // Update slug if name changed
    if (updateData.name) {
      company.slug = this.generateSlug(updateData.name)
    }
    
    await company.save()
    
    return this.formatCompanyProfile(company)
  }

  /**
   * Get companies with pagination and filters
   */
  async getCompanies(
    page: number = 1, 
    limit: number = 10, 
    filters?: { 
      industry?: string
      size?: string
      location?: string
      isVerified?: boolean
      isActive?: boolean
    }
  ): Promise<{
    companies: CompanyProfile[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }> {
    const query: Record<string, unknown> = {}
    
    if (filters?.industry) query.industry = filters.industry
    if (filters?.size) query.size = filters.size
    if (filters?.location) {
      query.$or = [
        { 'location.city': { $regex: filters.location, $options: 'i' } },
        { 'location.state': { $regex: filters.location, $options: 'i' } },
        { 'location.country': { $regex: filters.location, $options: 'i' } }
      ]
    }
    if (filters?.isVerified !== undefined) query.isVerified = filters.isVerified
    if (filters?.isActive !== undefined) query.isActive = filters.isActive
    
    const skip = (page - 1) * limit
    
    const [companies, total] = await Promise.all([
      Company.find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      Company.countDocuments(query)
    ])
    
    return {
      companies: companies.map(company => this.formatCompanyProfile(company)),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * Search companies
   */
  async searchCompanies(searchTerm: string, limit: number = 10): Promise<CompanyProfile[]> {
    const companies = await Company.find({
      $or: [
        { name: { $regex: searchTerm, $options: 'i' } },
        { description: { $regex: searchTerm, $options: 'i' } },
        { industry: { $regex: searchTerm, $options: 'i' } }
      ],
      isActive: true
    })
    .limit(limit)
    .sort({ isVerified: -1, name: 1 })
    
    return companies.map(company => this.formatCompanyProfile(company))
  }

  /**
   * Verify company
   */
  async verifyCompany(companyId: string): Promise<void> {
    const company = await Company.findById(companyId)
    
    if (!company) {
      throw errorService.createError(
        ErrorCode.NOT_FOUND,
        'Company not found',
        'companyId'
      )
    }
    
    company.isVerified = true
    await company.save()
  }

  /**
   * Generate slug from company name
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }

  /**
   * Format company data for API response
   */
  private formatCompanyProfile(company: Record<string, unknown>): CompanyProfile {
    // Type assertions for nested properties
    const _id = company._id as { toString: () => string }

    return {
      id: _id.toString(),
      name: String(company.name),
      slug: String(company.slug),
      description: String(company.description),
      website: company.website as string | undefined,
      industry: Array.isArray(company.industry) ? company.industry.join(', ') : String(company.industry || ''),
      size: String(company.size),
      location: company.location as Record<string, unknown>,
      logo: company.logo as string | undefined,
      banner: company.banner as string | undefined,
      socialLinks: company.socialLinks as Record<string, string> | undefined,
      isVerified: Boolean(company.isVerified),
      isActive: Boolean(company.isActive),
      createdAt: company.createdAt as Date,
      updatedAt: company.updatedAt as Date
    }
  }
}

export const companyService = new CompanyService()
