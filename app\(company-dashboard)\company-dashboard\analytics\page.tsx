// app/(company-dashboard)/company-dashboard/analytics/page.tsx
"use client"

import React, { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useCompanyDashboardStore } from "@/stores/company-dashboard.store"
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Eye,
  Calendar,
  DollarSign,
  Target,
  Clock,
  Download,
  RefreshCw
} from "lucide-react"

export default function CompanyAnalyticsPage() {
  const {
    hiringMetrics,
    metricsLoading,
    fetchHiringMetrics,
    selectedTimeRange,
    setTimeRange
  } = useCompanyDashboardStore()

  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    fetchHiringMetrics(selectedTimeRange)
  }, [fetchHiringMetrics, selectedTimeRange])

  const mockMetrics = {
    overview: {
      totalJobs: 24,
      activeJobs: 18,
      totalApplications: 342,
      totalViews: 1250,
      averageApplicationsPerJob: 14.3,
      responseRate: 68,
      timeToHire: 21,
      costPerHire: 3500
    },
    trends: {
      applicationsGrowth: 15,
      viewsGrowth: 8,
      hiringGrowth: 25,
      responseRateGrowth: -3
    },
    jobPerformance: [
      { title: "Senior Frontend Developer", applications: 45, views: 180, conversionRate: 25 },
      { title: "Product Manager", applications: 38, views: 165, conversionRate: 23 },
      { title: "UX Designer", applications: 32, views: 142, conversionRate: 22.5 },
      { title: "Backend Engineer", applications: 28, views: 125, conversionRate: 22.4 },
      { title: "Data Scientist", applications: 25, views: 98, conversionRate: 25.5 }
    ],
    applicationFunnel: {
      submitted: 342,
      reviewed: 245,
      interviewed: 89,
      offered: 34,
      hired: 28
    }
  }

  const getTrendIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="w-4 h-4 text-green-600" />
    if (growth < 0) return <TrendingDown className="w-4 h-4 text-red-600" />
    return <TrendingUp className="w-4 h-4 text-gray-400" />
  }

  const getTrendColor = (growth: number) => {
    if (growth > 0) return "text-green-600"
    if (growth < 0) return "text-red-600"
    return "text-gray-600"
  }

  return (
    <div className="w-full h-full">
      {/* Full Width Container with Padding */}
      <div className="w-full p-6">
        {/* Header Section */}
        <div className="w-full flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics & Reports</h1>
            <p className="text-muted-foreground mt-2">
              Track your hiring performance and optimize your recruitment process
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Select value={selectedTimeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={() => fetchHiringMetrics(selectedTimeRange)}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="jobs">Job Performance</TabsTrigger>
            <TabsTrigger value="funnel">Application Funnel</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="w-full">
            {/* Key Metrics Cards */}
            <div className="w-full grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{mockMetrics.overview.totalApplications}</div>
                  <div className={`text-xs flex items-center space-x-1 ${getTrendColor(mockMetrics.trends.applicationsGrowth)}`}>
                    {getTrendIcon(mockMetrics.trends.applicationsGrowth)}
                    <span>{Math.abs(mockMetrics.trends.applicationsGrowth)}% from last period</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Job Views</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{mockMetrics.overview.totalViews}</div>
                  <div className={`text-xs flex items-center space-x-1 ${getTrendColor(mockMetrics.trends.viewsGrowth)}`}>
                    {getTrendIcon(mockMetrics.trends.viewsGrowth)}
                    <span>{Math.abs(mockMetrics.trends.viewsGrowth)}% from last period</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Response Rate</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{mockMetrics.overview.responseRate}%</div>
                  <div className={`text-xs flex items-center space-x-1 ${getTrendColor(mockMetrics.trends.responseRateGrowth)}`}>
                    {getTrendIcon(mockMetrics.trends.responseRateGrowth)}
                    <span>{Math.abs(mockMetrics.trends.responseRateGrowth)}% from last period</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Time to Hire</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{mockMetrics.overview.timeToHire} days</div>
                  <div className="text-xs text-muted-foreground">
                    Average across all positions
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Metrics */}
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Hiring Efficiency</CardTitle>
                  <CardDescription>Key performance indicators</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Applications per Job</span>
                    <span className="text-sm text-muted-foreground">{mockMetrics.overview.averageApplicationsPerJob}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Cost per Hire</span>
                    <span className="text-sm text-muted-foreground">${mockMetrics.overview.costPerHire}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Active Jobs</span>
                    <span className="text-sm text-muted-foreground">{mockMetrics.overview.activeJobs}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Total Jobs Posted</span>
                    <span className="text-sm text-muted-foreground">{mockMetrics.overview.totalJobs}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Performance</CardTitle>
                  <CardDescription>Last 30 days overview</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">New Applications</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">89</span>
                      <div className={`flex items-center space-x-1 ${getTrendColor(15)}`}>
                        {getTrendIcon(15)}
                        <span className="text-xs">15%</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Interviews Conducted</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">23</span>
                      <div className={`flex items-center space-x-1 ${getTrendColor(8)}`}>
                        {getTrendIcon(8)}
                        <span className="text-xs">8%</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Offers Extended</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">12</span>
                      <div className={`flex items-center space-x-1 ${getTrendColor(25)}`}>
                        {getTrendIcon(25)}
                        <span className="text-xs">25%</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Successful Hires</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">8</span>
                      <div className={`flex items-center space-x-1 ${getTrendColor(33)}`}>
                        {getTrendIcon(33)}
                        <span className="text-xs">33%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="jobs" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Job Performance Analysis</CardTitle>
                <CardDescription>
                  Compare performance across your job postings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockMetrics.jobPerformance.map((job, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{job.title}</h4>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                          <span>{job.applications} applications</span>
                          <span>•</span>
                          <span>{job.views} views</span>
                          <span>•</span>
                          <span>{job.conversionRate}% conversion</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-semibold">{job.conversionRate}%</div>
                        <div className="text-xs text-muted-foreground">Conversion Rate</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="funnel" className="w-full">
            <Card>
              <CardHeader>
                <CardTitle>Application Funnel</CardTitle>
                <CardDescription>
                  Track candidates through your hiring process
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(mockMetrics.applicationFunnel).map(([stage, count], index) => {
                    const percentage = (count / mockMetrics.applicationFunnel.submitted) * 100
                    return (
                      <div key={stage} className="flex items-center space-x-4">
                        <div className="w-32 text-sm font-medium capitalize">
                          {stage.replace('_', ' ')}
                        </div>
                        <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                          <div 
                            className="bg-primary h-6 rounded-full flex items-center justify-center text-white text-sm font-medium"
                            style={{ width: `${percentage}%` }}
                          >
                            {count}
                          </div>
                        </div>
                        <div className="w-16 text-sm text-muted-foreground text-right">
                          {percentage.toFixed(1)}%
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="w-full">
            <div className="w-full grid gap-6 grid-cols-1 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Application Trends</CardTitle>
                  <CardDescription>Application volume over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <BarChart3 className="w-12 h-12 mx-auto mb-4" />
                    <p>Chart visualization would be implemented here</p>
                    <p className="text-sm">Using a charting library like Chart.js or Recharts</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Hiring Success Rate</CardTitle>
                  <CardDescription>Success rate trends over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12 text-muted-foreground">
                    <TrendingUp className="w-12 h-12 mx-auto mb-4" />
                    <p>Chart visualization would be implemented here</p>
                    <p className="text-sm">Showing hiring success rate trends</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
