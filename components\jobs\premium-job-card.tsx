'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Job, getApplicationStatusInfo } from '@/lib/job-data'
import { 
  MapPin, 
  DollarSign, 
  Clock, 
  Eye, 
  Heart, 
  Briefcase,
  CheckCircle,
  TrendingUp,
  Users,
  Calendar,
  ExternalLink,
  Bookmark,
  Share2,
  Building,
  Globe,
  Zap,
  Star
} from 'lucide-react'

interface JobCardProps {
  job: Job
  viewMode: 'grid' | 'list'
  onViewJob: (job: Job) => void
  onSaveJob: (job: Job) => void
  onApplyJob: (job: Job) => void
}

export function PremiumJobCard({ 
  job, 
  viewMode, 
  onViewJob, 
  onSaveJob, 
  onApplyJob 
}: JobCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isSaved, setIsSaved] = useState(false)

  const applicationStatusInfo = getApplicationStatusInfo(job)

  const formatSalary = (salary: Job['salary']) => {
    const min = salary.min.toLocaleString()
    const max = salary.max.toLocaleString()
    const period = salary.period === 'year' ? '/year' : salary.period === 'month' ? '/month' : '/hour'
    return `$${min} - $${max}${period}`
  }

  const getTimeAgo = (posted: string) => {
    // Simple time ago calculation - in real app would use a proper library
    return posted
  }

  const handleSave = () => {
    setIsSaved(!isSaved)
    if (onSaveJob) onSaveJob(job)
  }

  // On mobile, we'll render both views but hide the list view with CSS
  // This ensures mobile always shows grid view while desktop can toggle
  if (viewMode === 'list') {
    return (
      <>
        {/* List View - Hidden on Mobile */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="hidden md:block"
        >
          <Card className={`card-premium transition-all duration-300 ${
            isHovered ? 'scale-[1.02] theme-glow' : ''
          }`}>
          <CardContent className="p-4 md:p-6">
            <div className="flex items-start space-x-4 md:space-x-6">
              {/* Company Logo and Verification */}
              <div className="relative flex-shrink-0">
                <Avatar className="w-12 h-12 md:w-16 md:h-16 border-2 border-primary/20">
                  <AvatarImage src={job.company.logo} alt={job.company.name} />
                  <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm md:text-base">
                    {job.company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                {job.company.verified && (
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 md:w-5 md:h-5 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                    <CheckCircle className="w-2 h-2 md:w-3 md:h-3 text-white" />
                  </div>
                )}
              </div>

              {/* Main Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-2 md:mb-3">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-base md:text-xl font-bold truncate">{job.title}</h3>
                      {/* Hide featured badge on mobile to prevent overflow */}
                      {job.featured && (
                        <Badge variant="secondary" className="hidden sm:inline-flex bg-yellow-500 text-white text-xs">
                          <Star className="w-3 h-3 mr-1" />
                          Featured
                        </Badge>
                      )}
                      {job.urgent && (
                        <Badge variant="secondary" className="bg-red-500 text-white text-xs">
                          <Zap className="w-3 h-3 mr-1" />
                          <span className="hidden sm:inline">Urgent</span>
                          <span className="sm:hidden">!</span>
                        </Badge>
                      )}
                      {/* Hide application status on mobile */}
                      <Badge
                        variant="secondary"
                        className={`hidden md:inline-flex text-xs ${
                          applicationStatusInfo.color === 'green' ? 'bg-green-500 text-white' :
                          applicationStatusInfo.color === 'orange' ? 'bg-orange-500 text-white' :
                          applicationStatusInfo.color === 'red' ? 'bg-red-500 text-white' :
                          'bg-gray-500 text-white'
                        }`}
                      >
                        {applicationStatusInfo.label}
                      </Badge>
                    </div>
                    <p className="text-primary font-medium text-sm md:text-lg mb-2 truncate">{job.company.name}</p>
                    <div className="flex flex-wrap items-center gap-2 md:gap-4 text-xs md:text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-3 h-3 md:w-4 md:h-4" />
                        <span className="truncate">{job.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <DollarSign className="w-3 h-3 md:w-4 md:h-4" />
                        <span className="truncate">{formatSalary(job.salary)}</span>
                      </div>
                      {/* Hide some details on mobile */}
                      <div className="hidden sm:flex items-center space-x-1">
                        <Briefcase className="w-3 h-3 md:w-4 md:h-4" />
                        <span>{job.type}</span>
                      </div>
                      <div className="hidden md:flex items-center space-x-1">
                        <Globe className="w-3 h-3 md:w-4 md:h-4" />
                        <span>{job.workModel}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 flex-shrink-0">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSave}
                      className={`p-1.5 md:p-2 ${isSaved ? 'text-yellow-500' : 'text-muted-foreground'}`}
                    >
                      <Bookmark className={`w-3 h-3 md:w-4 md:h-4 ${isSaved ? 'fill-current' : ''}`} />
                    </Button>
                    {/* Hide applicant count on mobile */}
                    <div className="hidden sm:block text-right">
                      <div className="text-sm md:text-lg font-bold text-primary">{job.applicants}</div>
                      <div className="text-xs text-muted-foreground">applicants</div>
                    </div>
                  </div>
                </div>

                <p className="text-muted-foreground mb-3 md:mb-4 line-clamp-2 text-sm md:text-lg">{job.description}</p>

                {/* Skills - Reduced on mobile */}
                <div className="flex flex-wrap gap-1.5 md:gap-2 mb-3 md:mb-4">
                  {job.skills.slice(0, 4).map((skill) => (
                    <Badge key={skill} variant="secondary" className="theme-glow text-xs">
                      {skill}
                    </Badge>
                  ))}
                  {job.skills.length > 4 && (
                    <Badge variant="outline" className="text-xs">+{job.skills.length - 4}</Badge>
                  )}
                </div>

                {/* Stats and Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4 text-blue-500" />
                      <span>Posted {getTimeAgo(job.posted)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Eye className="w-4 h-4 text-green-500" />
                      <span>{job.views.toLocaleString()} views</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className="w-4 h-4 text-purple-500" />
                      <span>{job.experience}</span>
                    </div>
                    {job.company.verified && (
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span>Verified Company</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onViewJob(job)}
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Quick View
                    </Button>
                    <Button 
                      size="sm" 
                      className="button-premium"
                      onClick={() => onApplyJob(job)}
                    >
                      <Briefcase className="w-4 h-4 mr-2" />
                      Apply Now
                    </Button>
                    <Button 
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        window.open(`/jobs/${job.id}`, '_blank')
                      }}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Full Details
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        </motion.div>

        {/* Mobile Grid View Fallback - Shown on Mobile when list view is selected */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          className="block md:hidden"
        >
          <Card className={`card-premium h-full transition-all duration-300 ${
            isHovered ? 'scale-105 theme-glow' : ''
          }`}>
            <CardContent className="p-4">
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="relative">
                  <Avatar className="w-12 h-12 border-2 border-primary/20">
                    <AvatarImage src={job.company.logo} alt={job.company.name} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm">
                      {job.company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </AvatarFallback>
                  </Avatar>
                  {job.company.verified && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                      <CheckCircle className="w-2 h-2 text-white" />
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSave}
                    className={`p-1.5 ${isSaved ? 'text-yellow-500' : 'text-muted-foreground'}`}
                  >
                    <Heart className={`w-3 h-3 ${isSaved ? 'fill-current' : ''}`} />
                  </Button>
                  <div className="text-right">
                    <div className="text-sm font-bold text-primary">{formatSalary(job.salary)}</div>
                  </div>
                </div>
              </div>

              {/* Job Info */}
              <div className="mb-3">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="text-base font-bold truncate">{job.title}</h3>
                  {job.urgent && (
                    <Badge variant="secondary" className="bg-red-500 text-white text-xs">
                      <Zap className="w-3 h-3 mr-1" />
                      !
                    </Badge>
                  )}
                </div>
                <p className="text-primary font-medium text-xs mb-2 truncate">{job.company.name}</p>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span className="truncate">{job.location}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{getTimeAgo(job.posted)}</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              <p className="text-muted-foreground text-xs mb-3 line-clamp-2">{job.description}</p>

              {/* Skills */}
              <div className="flex flex-wrap gap-1 mb-3">
                {job.skills.slice(0, 3).map((skill) => (
                  <Badge key={skill} variant="secondary" className="text-xs theme-glow">
                    {skill}
                  </Badge>
                ))}
                {job.skills.length > 3 && (
                  <Badge variant="outline" className="text-xs">+{job.skills.length - 3}</Badge>
                )}
              </div>

              {/* Actions */}
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  className="button-premium flex-1 text-xs"
                  onClick={() => onApplyJob(job)}
                >
                  <Briefcase className="w-3 h-3 mr-1" />
                  Apply
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onViewJob(job)}
                  className="flex-1 text-xs"
                >
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </>
    )
  }

  // Grid View
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={`card-premium h-full transition-all duration-300 ${
        isHovered ? 'scale-105 theme-glow' : ''
      }`}>
        <CardContent className="p-4 md:p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-3 md:mb-4">
            <div className="relative">
              <Avatar className="w-10 h-10 md:w-12 md:h-12 border-2 border-primary/20">
                <AvatarImage src={job.company.logo} alt={job.company.name} />
                <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/10 text-primary font-bold text-sm md:text-base">
                  {job.company.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              {job.company.verified && (
                <div className="absolute -bottom-1 -right-1 w-3 h-3 md:w-4 md:h-4 bg-green-500 rounded-full border-2 border-background flex items-center justify-center">
                  <CheckCircle className="w-1.5 h-1.5 md:w-2 md:h-2 text-white" />
                </div>
              )}
            </div>
            
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSave}
                className={`p-2 ${isSaved ? 'text-yellow-500' : 'text-muted-foreground'}`}
              >
                <Bookmark className={`w-4 h-4 ${isSaved ? 'fill-current' : ''}`} />
              </Button>
              {job.featured && (
                <Badge variant="secondary" className="bg-yellow-500 text-white text-xs">
                  Featured
                </Badge>
              )}
              <Badge
                variant="secondary"
                className={`text-xs ${
                  applicationStatusInfo.color === 'green' ? 'bg-green-500 text-white' :
                  applicationStatusInfo.color === 'orange' ? 'bg-orange-500 text-white' :
                  applicationStatusInfo.color === 'red' ? 'bg-red-500 text-white' :
                  'bg-gray-500 text-white'
                }`}
              >
                {applicationStatusInfo.label}
              </Badge>
            </div>
          </div>

          {/* Job Info */}
          <div className="mb-3">
            <h3 className="text-lg font-bold mb-1 truncate">{job.title}</h3>
            <p className="text-primary font-medium text-sm mb-2">{job.company.name}</p>
            <div className="flex items-center space-x-3 text-xs text-muted-foreground">
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>{job.location}</span>
              </div>
              <div className="flex items-center space-x-1">
                <DollarSign className="w-3 h-3" />
                <span>{formatSalary(job.salary)}</span>
              </div>
            </div>
          </div>

          {/* Description */}
          <p className="text-muted-foreground text-sm mb-4 line-clamp-3">{job.description}</p>

          {/* Skills */}
          <div className="flex flex-wrap gap-1 mb-4">
            {job.skills.slice(0, 4).map((skill) => (
              <Badge key={skill} variant="secondary" className="text-xs theme-glow">
                {skill}
              </Badge>
            ))}
            {job.skills.length > 4 && (
              <Badge variant="outline" className="text-xs">+{job.skills.length - 4}</Badge>
            )}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-2 mb-4 text-xs">
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Clock className="w-3 h-3 text-blue-500" />
              <span>{getTimeAgo(job.posted)}</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Users className="w-3 h-3 text-purple-500" />
              <span>{job.applicants} applicants</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Briefcase className="w-3 h-3" />
              <span>{job.type}</span>
            </div>
            <div className="flex items-center space-x-1 text-muted-foreground">
              <Globe className="w-3 h-3" />
              <span>{job.workModel}</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col space-y-2">
            <Button 
              size="sm" 
              className="w-full button-premium"
              onClick={() => onApplyJob(job)}
            >
              <Briefcase className="w-4 h-4 mr-2" />
              Apply Now
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => onViewJob(job)}
            >
              <Eye className="w-4 h-4 mr-2" />
              Quick View
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full"
              onClick={() => {
                window.open(`/jobs/${job.id}`, '_blank')
              }}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Full Details
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
