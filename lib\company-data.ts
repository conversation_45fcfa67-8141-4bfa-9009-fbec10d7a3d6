// Comprehensive company data service
export interface Company {
  id: number
  name: string
  industry: string
  location: string
  logo: string
  rating: number
  size: string
  founded: number
  description: string
  specialties: string[]
  openJobs: number
  followers: number
  website: string
  benefits: string[]
  culture: string
  verified: boolean
  employees: string
  revenue: string
  funding: string
  headquarters: string
  offices: string[]
  techStack: string[]
  awards: string[]
  socialImpact: string
  jobs?: Job[]
  team?: TeamMember[]
  detailedBenefits?: DetailedBenefit[]
  reviews?: CompanyReview[]
  stats?: CompanyStats
  investors?: string[]
}

export interface Job {
  id: number
  title: string
  department: string
  type: string
  location: string
  salary: string
  posted: string
  description: string
  requirements: string[]
}

export interface TeamMember {
  name: string
  role: string
  avatar: string
  bio: string
  linkedin?: string
}

export interface DetailedBenefit {
  icon: any
  title: string
  description: string
}

export interface CompanyReview {
  id: number
  employee: string
  rating: number
  title: string
  comment: string
  pros: string[]
  cons: string[]
  date: string
  helpful: number
}

export interface CompanyStats {
  employeeSatisfaction: number
  ceoApproval: number
  recommendToFriend: number
  careerOpportunities: number
  workLifeBalance: number
  compensationBenefits: number
}

// Mock company data with full details
export const companies: Company[] = [
  {
    id: 1,
    name: "TechCorp Inc.",
    industry: "Software Development",
    location: "San Francisco, CA",
    logo: "/api/placeholder/80/80",
    rating: 4.8,
    size: "large",
    founded: 2015,
    description: "Leading software development company specializing in enterprise solutions and cloud infrastructure. We build scalable applications that power businesses worldwide, serving over 10,000 companies across 50+ countries. Our mission is to democratize technology and make powerful software accessible to businesses of all sizes.",
    specialties: ["Cloud Computing", "Enterprise Software", "AI/ML", "DevOps", "Microservices", "Data Analytics", "Cybersecurity", "API Development"],
    openJobs: 24,
    followers: 15420,
    website: "https://techcorp.com",
    benefits: ["Health Insurance", "Remote Work", "Stock Options", "Unlimited PTO", "Learning Budget", "Gym Membership"],
    culture: "Innovation-driven culture with focus on work-life balance, continuous learning, and collaborative problem-solving. We believe in empowering our team members to take ownership and drive meaningful impact.",
    verified: true,
    employees: "500-1000",
    revenue: "$50M - $100M",
    funding: "Series C",
    headquarters: "San Francisco, CA",
    offices: ["San Francisco, CA", "New York, NY", "Austin, TX", "London, UK"],
    techStack: ["React", "Node.js", "Python", "AWS", "Kubernetes", "PostgreSQL", "Redis", "GraphQL"],
    awards: ["Best Places to Work 2024", "Top 50 Startups to Watch", "Innovation Award 2023"],
    socialImpact: "Committed to environmental sustainability and social responsibility through our carbon-neutral operations and community education programs.",
    investors: ["Sequoia Capital", "Andreessen Horowitz", "Google Ventures"],
    jobs: [
      {
        id: 1,
        title: "Senior Software Engineer",
        department: "Engineering",
        type: "Full-time",
        location: "San Francisco, CA",
        salary: "$120,000 - $180,000",
        posted: "2 days ago",
        description: "Join our core platform team to build scalable microservices that power our enterprise customers. You'll work on high-impact projects using cutting-edge technologies.",
        requirements: ["5+ years experience", "React/Node.js", "AWS", "Microservices", "System Design"]
      },
      {
        id: 2,
        title: "Product Manager",
        department: "Product",
        type: "Full-time",
        location: "Remote",
        salary: "$130,000 - $160,000",
        posted: "1 week ago",
        description: "Lead product strategy and roadmap for our AI-powered analytics platform. Drive product vision from conception to launch.",
        requirements: ["3+ years PM experience", "B2B SaaS", "Analytics", "AI/ML knowledge", "Data-driven mindset"]
      },
      {
        id: 3,
        title: "UX Designer",
        department: "Design",
        type: "Full-time",
        location: "New York, NY",
        salary: "$90,000 - $120,000",
        posted: "3 days ago",
        description: "Design intuitive user experiences for our enterprise dashboard and mobile applications. Collaborate with product and engineering teams.",
        requirements: ["4+ years UX design", "Figma", "Design systems", "Enterprise software", "User research"]
      }
    ],
    team: [
      {
        name: "Sarah Johnson",
        role: "CEO & Founder",
        avatar: "/api/placeholder/80/80",
        bio: "Former VP of Engineering at Google with 15+ years in tech leadership. Stanford CS graduate with expertise in distributed systems and scaling engineering teams.",
        linkedin: "https://linkedin.com/in/sarahjohnson"
      },
      {
        name: "Michael Chen",
        role: "CTO",
        avatar: "/api/placeholder/80/80",
        bio: "Ex-Amazon architect specializing in scalable cloud infrastructure. Led engineering teams of 100+ developers and built systems serving millions of users.",
        linkedin: "https://linkedin.com/in/michaelchen"
      },
      {
        name: "Emily Rodriguez",
        role: "VP of Product",
        avatar: "/api/placeholder/80/80",
        bio: "Product leader with experience at Airbnb and Stripe. Expert in B2B SaaS, product-market fit, and driving user adoption at scale.",
        linkedin: "https://linkedin.com/in/emilyrodriguez"
      }
    ],
    reviews: [
      {
        id: 1,
        employee: "Anonymous Software Engineer",
        rating: 5,
        title: "Amazing place to grow your career",
        comment: "TechCorp has been an incredible place to work. The leadership is transparent, the technology is cutting-edge, and the team is brilliant. Great work-life balance and learning opportunities.",
        pros: ["Great leadership", "Cutting-edge tech", "Work-life balance", "Learning opportunities"],
        cons: ["Fast-paced environment", "High expectations"],
        date: "2 weeks ago",
        helpful: 24
      },
      {
        id: 2,
        employee: "Anonymous Product Manager",
        rating: 4,
        title: "Excellent product culture",
        comment: "The product team is very collaborative and data-driven. Lots of autonomy to drive impact. The company is growing fast which creates great opportunities but also some growing pains.",
        pros: ["Product-focused culture", "Data-driven decisions", "Growth opportunities", "Smart colleagues"],
        cons: ["Growing pains", "Sometimes chaotic", "Long hours during launches"],
        date: "1 month ago",
        helpful: 18
      }
    ],
    stats: {
      employeeSatisfaction: 4.8,
      ceoApproval: 92,
      recommendToFriend: 89,
      careerOpportunities: 4.6,
      workLifeBalance: 4.4,
      compensationBenefits: 4.7
    }
  },
  {
    id: 2,
    name: "DesignStudio Pro",
    industry: "Design & Creative",
    location: "New York, NY",
    logo: "/api/placeholder/80/80",
    rating: 4.9,
    size: "medium",
    founded: 2018,
    description: "Award-winning design studio creating beautiful digital experiences for brands worldwide. We specialize in UX/UI design, brand identity, and digital marketing for Fortune 500 companies and innovative startups.",
    specialties: ["UX/UI Design", "Brand Identity", "Digital Marketing", "Web Design", "Mobile Design", "Design Systems", "User Research", "Creative Strategy"],
    openJobs: 8,
    followers: 8750,
    website: "https://designstudio.com",
    benefits: ["Health Insurance", "Flexible Hours", "Professional Development", "Creative Freedom", "Design Tools Budget", "Inspiration Trips"],
    culture: "Creative and collaborative environment with emphasis on artistic expression, design thinking, and pushing creative boundaries. We foster a culture of experimentation and continuous learning.",
    verified: true,
    employees: "51-200",
    revenue: "$10M - $25M",
    funding: "Series A",
    headquarters: "New York, NY",
    offices: ["New York, NY", "Los Angeles, CA", "London, UK"],
    techStack: ["Figma", "Adobe Creative Suite", "Sketch", "Principle", "Framer", "React", "TypeScript", "Next.js"],
    awards: ["Design Agency of the Year 2024", "Webby Awards Winner", "Awwwards Site of the Day"],
    socialImpact: "Supporting design education through scholarships and mentorship programs for underrepresented communities in design.",
    investors: ["Design Fund", "Kleiner Perkins", "First Round Capital"],
    jobs: [
      {
        id: 4,
        title: "Senior UX Designer",
        department: "Design",
        type: "Full-time",
        location: "New York, NY",
        salary: "$95,000 - $130,000",
        posted: "1 day ago",
        description: "Lead UX design for major client projects including Fortune 500 brands. Drive design strategy and mentor junior designers.",
        requirements: ["5+ years UX design", "Portfolio of complex projects", "Design systems", "Client management", "Figma expert"]
      },
      {
        id: 5,
        title: "Brand Designer",
        department: "Brand",
        type: "Full-time",
        location: "Los Angeles, CA",
        salary: "$70,000 - $95,000",
        posted: "4 days ago",
        description: "Create compelling brand identities and visual systems for diverse clients. Work across print and digital mediums.",
        requirements: ["3+ years brand design", "Adobe Creative Suite", "Typography", "Brand strategy", "Print design"]
      }
    ],
    team: [
      {
        name: "Alex Thompson",
        role: "Creative Director",
        avatar: "/api/placeholder/80/80",
        bio: "Award-winning creative director with 12+ years at top agencies including IDEO and Pentagram. Passionate about human-centered design.",
        linkedin: "https://linkedin.com/in/alexthompson"
      },
      {
        name: "Maria Santos",
        role: "Head of UX",
        avatar: "/api/placeholder/80/80",
        bio: "UX leader with experience at Apple and Airbnb. Expert in design systems and user research methodologies.",
        linkedin: "https://linkedin.com/in/mariasantos"
      }
    ],
    reviews: [
      {
        id: 3,
        employee: "Anonymous UX Designer",
        rating: 5,
        title: "Creative paradise with amazing clients",
        comment: "Working at DesignStudio Pro has been a dream. The projects are challenging and creative, the team is supportive, and the work-life balance is excellent.",
        pros: ["Amazing projects", "Creative freedom", "Supportive team", "Great clients"],
        cons: ["Can be demanding during deadlines", "High creative standards"],
        date: "1 week ago",
        helpful: 15
      }
    ],
    stats: {
      employeeSatisfaction: 4.9,
      ceoApproval: 95,
      recommendToFriend: 94,
      careerOpportunities: 4.7,
      workLifeBalance: 4.8,
      compensationBenefits: 4.5
    }
  },
  {
    id: 3,
    name: "HealthTech Solutions",
    industry: "Healthcare Technology",
    location: "Boston, MA",
    logo: "/api/placeholder/80/80",
    rating: 4.7,
    size: "enterprise",
    founded: 2012,
    description: "Revolutionary healthcare technology company improving patient outcomes through innovative digital health solutions and telemedicine platforms. We serve over 500 healthcare institutions and 2 million patients worldwide.",
    specialties: ["Telemedicine", "Health Analytics", "Medical Devices", "Clinical Research", "Patient Care", "Healthcare IT", "Medical AI", "Digital Therapeutics"],
    openJobs: 31,
    followers: 22100,
    website: "https://healthtech.com",
    benefits: ["Health Insurance", "Dental & Vision", "Retirement Plan", "Wellness Programs", "Medical Research Stipend", "Continuing Education"],
    culture: "Mission-driven culture focused on improving healthcare accessibility, patient outcomes, and medical innovation. We prioritize work-life balance and support our team's professional growth in healthcare technology.",
    verified: true,
    employees: "1000+",
    revenue: "$100M+",
    funding: "IPO",
    headquarters: "Boston, MA",
    offices: ["Boston, MA", "San Francisco, CA", "Chicago, IL", "Atlanta, GA", "Toronto, ON"],
    techStack: ["Python", "React", "Node.js", "TensorFlow", "AWS", "FHIR", "HL7", "PostgreSQL"],
    awards: ["Healthcare Innovation Award 2024", "Best Digital Health Company", "FDA Breakthrough Device Designation"],
    socialImpact: "Providing free telemedicine services to underserved communities and supporting global health initiatives in developing countries.",
    investors: ["GV (Google Ventures)", "Andreessen Horowitz", "Kleiner Perkins"],
    jobs: [
      {
        id: 6,
        title: "Senior Data Scientist",
        department: "Data Science",
        type: "Full-time",
        location: "Boston, MA",
        salary: "$130,000 - $170,000",
        posted: "3 days ago",
        description: "Apply machine learning to healthcare data to improve patient outcomes and clinical decision-making.",
        requirements: ["PhD in relevant field", "Healthcare data experience", "Python/R", "Machine Learning", "Clinical knowledge"]
      }
    ],
    team: [
      {
        name: "Dr. Jennifer Walsh",
        role: "CEO & Co-Founder",
        avatar: "/api/placeholder/80/80",
        bio: "Former Chief Medical Officer at Mayo Clinic with 20+ years in healthcare innovation and digital transformation.",
        linkedin: "https://linkedin.com/in/jenniferwalsh"
      }
    ],
    reviews: [
      {
        id: 4,
        employee: "Anonymous Software Engineer",
        rating: 5,
        title: "Making a real impact in healthcare",
        comment: "Working at HealthTech Solutions feels meaningful. Every day we're building technology that directly improves patient lives. The team is brilliant and the mission is inspiring.",
        pros: ["Meaningful work", "Smart colleagues", "Healthcare impact", "Good benefits"],
        cons: ["Regulatory complexity", "Long development cycles"],
        date: "3 weeks ago",
        helpful: 22
      }
    ],
    stats: {
      employeeSatisfaction: 4.7,
      ceoApproval: 88,
      recommendToFriend: 85,
      careerOpportunities: 4.5,
      workLifeBalance: 4.3,
      compensationBenefits: 4.6
    }
  },
  {
    id: 4,
    name: "FinanceFlow",
    industry: "Financial Technology",
    location: "Austin, TX",
    logo: "/api/placeholder/80/80",
    rating: 4.6,
    size: "medium",
    founded: 2019,
    description: "Next-generation fintech company revolutionizing personal and business banking with AI-powered financial insights and seamless user experiences. We process over $1B in transactions monthly for 100,000+ users.",
    specialties: ["Digital Banking", "AI Analytics", "Payment Processing", "Cryptocurrency", "Financial Planning", "Robo-Advisory", "Open Banking", "RegTech"],
    openJobs: 16,
    followers: 12300,
    website: "https://financeflow.com",
    benefits: ["Stock Options", "Remote Work", "Health Insurance", "Learning Budget", "Financial Planning Services", "Crypto Investment Matching"],
    culture: "Fast-paced startup environment with focus on innovation, growth, and financial inclusion. We embrace experimentation and data-driven decision making while maintaining work-life balance.",
    verified: false,
    employees: "51-200",
    revenue: "$25M - $50M",
    funding: "Series B",
    headquarters: "Austin, TX",
    offices: ["Austin, TX", "San Francisco, CA", "New York, NY"],
    techStack: ["React", "Node.js", "Python", "Kubernetes", "AWS", "Blockchain", "TensorFlow", "Redis"],
    awards: ["Fintech Startup of the Year 2023", "Best Mobile Banking App", "AI Innovation in Finance Award"],
    socialImpact: "Promoting financial literacy through free educational programs and providing banking services to underbanked communities.",
    investors: ["Sequoia Capital", "Stripe", "Coinbase Ventures"],
    jobs: [
      {
        id: 7,
        title: "Blockchain Engineer",
        department: "Engineering",
        type: "Full-time",
        location: "Austin, TX",
        salary: "$110,000 - $150,000",
        posted: "1 week ago",
        description: "Build secure and scalable blockchain infrastructure for our cryptocurrency and DeFi products.",
        requirements: ["Blockchain experience", "Solidity", "Web3", "Security focus", "Distributed systems"]
      }
    ],
    team: [
      {
        name: "Marcus Thompson",
        role: "CEO & Founder",
        avatar: "/api/placeholder/80/80",
        bio: "Former Goldman Sachs VP with expertise in algorithmic trading and financial technology innovation.",
        linkedin: "https://linkedin.com/in/marcusthompson"
      }
    ],
    reviews: [
      {
        id: 5,
        employee: "Anonymous Product Manager",
        rating: 4,
        title: "Great fintech startup with growth potential",
        comment: "FinanceFlow is at an exciting stage with lots of opportunities to shape the product. The team is smart and the technology is cutting-edge. Work-life balance could be better during crunch times.",
        pros: ["Growth opportunities", "Cutting-edge tech", "Smart team", "Equity upside"],
        cons: ["Long hours sometimes", "Startup uncertainty", "Fast pace"],
        date: "2 weeks ago",
        helpful: 16
      }
    ],
    stats: {
      employeeSatisfaction: 4.6,
      ceoApproval: 85,
      recommendToFriend: 82,
      careerOpportunities: 4.8,
      workLifeBalance: 4.0,
      compensationBenefits: 4.5
    }
  }
]

// Helper functions
export function getCompanyById(id: number): Company | undefined {
  return companies.find(company => company.id === id)
}

export function getCompaniesByIndustry(industry: string): Company[] {
  return companies.filter(company => company.industry === industry)
}

export function searchCompanies(query: string): Company[] {
  const lowercaseQuery = query.toLowerCase()
  return companies.filter(company => 
    company.name.toLowerCase().includes(lowercaseQuery) ||
    company.industry.toLowerCase().includes(lowercaseQuery) ||
    company.location.toLowerCase().includes(lowercaseQuery) ||
    company.specialties.some(specialty => specialty.toLowerCase().includes(lowercaseQuery))
  )
}
