"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useClientStore } from "@/stores/client.store"
import { 
  User, 
  Briefcase, 
  GraduationCap, 
  Award, 
  Star,
  Plus,
  X,
  Save,
  Loader2
} from "lucide-react"

interface ProfileEditModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ProfileEditModal({ open, onOpenChange }: ProfileEditModalProps) {
  const { client, updateClientProfile, updateLoading } = useClientStore()
  const [activeTab, setActiveTab] = useState("basic")
  
  // Form states
  const [basicInfo, setBasicInfo] = useState({
    headline: client?.headline || "",
    summary: client?.summary || "",
    experience: {
      level: client?.experience?.level || "",
      years: client?.experience?.years || 0,
      currentPosition: client?.experience?.currentPosition || "",
      currentCompany: client?.experience?.currentCompany || ""
    }
  })

  const [workHistory, setWorkHistory] = useState(client?.workHistory || [])
  const [education, setEducation] = useState(client?.education || [])
  const [skills, setSkills] = useState(client?.skills || [])
  const [newSkill, setNewSkill] = useState({ name: "", category: "", proficiency: "beginner" })

  const handleSave = async () => {
    try {
      const updateData = {
        headline: basicInfo.headline,
        summary: basicInfo.summary,
        experience: basicInfo.experience,
        workHistory,
        education,
        skills
      }

      await updateClientProfile(updateData)
      onOpenChange(false)
    } catch (error) {
      console.error('Failed to update profile:', error)
    }
  }

  const addSkill = () => {
    if (newSkill.name.trim()) {
      setSkills([...skills, { 
        ...newSkill, 
        endorsed: false, 
        endorsements: 0 
      }])
      setNewSkill({ name: "", category: "", proficiency: "beginner" })
    }
  }

  const removeSkill = (index: number) => {
    setSkills(skills.filter((_, i) => i !== index))
  }

  const addWorkExperience = () => {
    setWorkHistory([...workHistory, {
      position: "",
      company: "",
      startDate: new Date(),
      endDate: new Date(),
      isCurrent: false,
      description: "",
      technologies: [],
      achievements: []
    }])
  }

  const addEducation = () => {
    setEducation([...education, {
      degree: "",
      institution: "",
      fieldOfStudy: "",
      startDate: new Date(),
      endDate: new Date(),
      isCurrent: false,
      gpa: 0,
      honors: []
    }])
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            Edit Profile
          </DialogTitle>
          <DialogDescription>
            Update your professional profile to attract better opportunities
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="experience">Experience</TabsTrigger>
            <TabsTrigger value="education">Education</TabsTrigger>
            <TabsTrigger value="skills">Skills</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Professional Summary</CardTitle>
                <CardDescription>
                  Your headline and summary are the first things employers see
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="headline">Professional Headline</Label>
                  <Input
                    id="headline"
                    placeholder="e.g., Senior React Developer with 5+ years experience"
                    value={basicInfo.headline}
                    onChange={(e) => setBasicInfo({
                      ...basicInfo,
                      headline: e.target.value
                    })}
                  />
                </div>
                
                <div>
                  <Label htmlFor="summary">Professional Summary</Label>
                  <Textarea
                    id="summary"
                    placeholder="Write a compelling summary of your professional background, key skills, and career objectives..."
                    rows={6}
                    value={basicInfo.summary}
                    onChange={(e) => setBasicInfo({
                      ...basicInfo,
                      summary: e.target.value
                    })}
                  />
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="experience-level">Experience Level</Label>
                    <select
                      id="experience-level"
                      className="w-full p-2 border rounded-md"
                      value={basicInfo.experience.level}
                      onChange={(e) => setBasicInfo({
                        ...basicInfo,
                        experience: { ...basicInfo.experience, level: e.target.value }
                      })}
                    >
                      <option value="">Select level</option>
                      <option value="entry">Entry Level</option>
                      <option value="mid">Mid Level</option>
                      <option value="senior">Senior Level</option>
                      <option value="executive">Executive</option>
                    </select>
                  </div>
                  
                  <div>
                    <Label htmlFor="years">Years of Experience</Label>
                    <Input
                      id="years"
                      type="number"
                      min="0"
                      max="50"
                      value={basicInfo.experience.years}
                      onChange={(e) => setBasicInfo({
                        ...basicInfo,
                        experience: { ...basicInfo.experience, years: parseInt(e.target.value) || 0 }
                      })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="current-position">Current Position</Label>
                    <Input
                      id="current-position"
                      placeholder="e.g., Senior Software Engineer"
                      value={basicInfo.experience.currentPosition}
                      onChange={(e) => setBasicInfo({
                        ...basicInfo,
                        experience: { ...basicInfo.experience, currentPosition: e.target.value }
                      })}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="current-company">Current Company</Label>
                    <Input
                      id="current-company"
                      placeholder="e.g., TechCorp Inc."
                      value={basicInfo.experience.currentCompany}
                      onChange={(e) => setBasicInfo({
                        ...basicInfo,
                        experience: { ...basicInfo.experience, currentCompany: e.target.value }
                      })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="experience" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <Briefcase className="w-5 h-5 mr-2" />
                      Work Experience
                    </CardTitle>
                    <CardDescription>
                      Add your professional work history
                    </CardDescription>
                  </div>
                  <Button onClick={addWorkExperience} size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Experience
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {workHistory.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Briefcase className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No work experience added yet</p>
                    <p className="text-sm">Click "Add Experience" to get started</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {workHistory.map((work, index) => (
                      <div key={index} className="p-4 border rounded-lg space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Experience #{index + 1}</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setWorkHistory(workHistory.filter((_, i) => i !== index))}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                          <Input
                            placeholder="Position Title"
                            value={work.position}
                            onChange={(e) => {
                              const updated = [...workHistory]
                              updated[index].position = e.target.value
                              setWorkHistory(updated)
                            }}
                          />
                          <Input
                            placeholder="Company Name"
                            value={work.company}
                            onChange={(e) => {
                              const updated = [...workHistory]
                              updated[index].company = e.target.value
                              setWorkHistory(updated)
                            }}
                          />
                        </div>
                        
                        <Textarea
                          placeholder="Describe your role, responsibilities, and achievements..."
                          rows={3}
                          value={work.description}
                          onChange={(e) => {
                            const updated = [...workHistory]
                            updated[index].description = e.target.value
                            setWorkHistory(updated)
                          }}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="education" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center">
                      <GraduationCap className="w-5 h-5 mr-2" />
                      Education
                    </CardTitle>
                    <CardDescription>
                      Add your educational background
                    </CardDescription>
                  </div>
                  <Button onClick={addEducation} size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Education
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {education.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <GraduationCap className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No education added yet</p>
                    <p className="text-sm">Click "Add Education" to get started</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {education.map((edu, index) => (
                      <div key={index} className="p-4 border rounded-lg space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Education #{index + 1}</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEducation(education.filter((_, i) => i !== index))}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                          <Input
                            placeholder="Degree"
                            value={edu.degree}
                            onChange={(e) => {
                              const updated = [...education]
                              updated[index].degree = e.target.value
                              setEducation(updated)
                            }}
                          />
                          <Input
                            placeholder="Institution"
                            value={edu.institution}
                            onChange={(e) => {
                              const updated = [...education]
                              updated[index].institution = e.target.value
                              setEducation(updated)
                            }}
                          />
                        </div>
                        
                        <Input
                          placeholder="Field of Study"
                          value={edu.fieldOfStudy}
                          onChange={(e) => {
                            const updated = [...education]
                            updated[index].fieldOfStudy = e.target.value
                            setEducation(updated)
                          }}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="skills" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="w-5 h-5 mr-2" />
                  Skills & Expertise
                </CardTitle>
                <CardDescription>
                  Add skills that showcase your expertise
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex space-x-2">
                  <Input
                    placeholder="Skill name"
                    value={newSkill.name}
                    onChange={(e) => setNewSkill({ ...newSkill, name: e.target.value })}
                  />
                  <Input
                    placeholder="Category"
                    value={newSkill.category}
                    onChange={(e) => setNewSkill({ ...newSkill, category: e.target.value })}
                  />
                  <select
                    className="px-3 py-2 border rounded-md"
                    value={newSkill.proficiency}
                    onChange={(e) => setNewSkill({ ...newSkill, proficiency: e.target.value })}
                  >
                    <option value="beginner">Beginner</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                    <option value="expert">Expert</option>
                  </select>
                  <Button onClick={addSkill}>
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>

                <div className="flex flex-wrap gap-2">
                  {skills.map((skill, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="flex items-center space-x-1"
                    >
                      <span>{skill.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => removeSkill(index)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={updateLoading}>
            {updateLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
