import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type {
  CompanySettings,
  CompanySettingsUpdate,
  GetCompanySettingsResponse,
  UpdateCompanySettingsResponse
} from '@/types/company-settings.types'
import type {
  EnhancedCompany,
  CompanyLocation,
  CompanyCulture,
  SocialLinks,
  UpdateCompanyProfileRequest,
  UpdateCompanyCultureRequest,
  UpdateSocialLinksRequest,
  AddLocationRequest,
  UpdateLocationRequest
} from '@/types/company-management.types'

// Use the enhanced company type
type Company = EnhancedCompany & {
  settings?: CompanySettings
}

interface CompanyState {
  // Current company data
  company: Company | null
  
  // Loading states
  loading: boolean
  profileLoading: boolean
  updateLoading: boolean
  settingsLoading: boolean
  settingsUpdateLoading: boolean

  // Error state
  error: string | null
  settingsError: string | null

  // Settings data
  settings: CompanySettings | null
  
  // Team members and jobs
  teamMembers: Array<Record<string, unknown>>
  jobs: Array<Record<string, unknown>>
  applications: Array<Record<string, unknown>>
  
  // Analytics data
  analytics: {
    profileViews: number[]
    applicationTrends: number[]
    jobPerformance: Array<Record<string, unknown>>
  }
}

interface CompanyActions {
  // Enhanced company profile management
  fetchCompanyProfile: () => Promise<void>
  createCompanyProfile: (data: Record<string, unknown>) => Promise<void>
  updateCompanyProfile: (data: Record<string, unknown>) => Promise<void>
  updateCompanyCulture: (culture: Record<string, unknown>) => Promise<void>
  updateSocialLinks: (socialLinks: Record<string, unknown>) => Promise<void>

  // Location management
  addLocation: (location: Record<string, unknown>) => Promise<void>
  updateLocation: (locationId: string, location: Record<string, unknown>) => Promise<void>
  removeLocation: (locationId: string) => Promise<void>

  // Team management
  fetchTeamMembers: () => Promise<void>
  inviteTeamMember: (email: string, role: string) => Promise<void>
  removeTeamMember: (memberId: string) => Promise<void>

  // Job management
  fetchCompanyJobs: () => Promise<void>
  createJob: (jobData: Record<string, unknown>) => Promise<void>
  updateJob: (jobId: string, jobData: Record<string, unknown>) => Promise<void>
  deleteJob: (jobId: string) => Promise<void>

  // Application management
  fetchApplications: () => Promise<void>
  updateApplicationStatus: (applicationId: string, status: string) => Promise<void>

  // Settings management
  fetchCompanySettings: () => Promise<void>
  updateCompanySettings: (settings: CompanySettingsUpdate) => Promise<void>
  resetCompanySettings: () => Promise<void>
  updateSettingsSection: (section: string, data: Record<string, unknown>) => Promise<void>

  // Analytics
  fetchAnalytics: () => Promise<void>

  // Utility
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  clearSettingsError: () => void
  reset: () => void
}

type CompanyStore = CompanyState & CompanyActions

const initialState: CompanyState = {
  company: null,
  loading: false,
  profileLoading: false,
  updateLoading: false,
  settingsLoading: false,
  settingsUpdateLoading: false,
  error: null,
  settingsError: null,
  settings: null,
  teamMembers: [],
  jobs: [],
  applications: [],
  analytics: {
    profileViews: [],
    applicationTrends: [],
    jobPerformance: []
  }
}

export const useCompanyStore = create<CompanyStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Company profile management
      fetchCompanyProfile: async () => {
        set({ profileLoading: true, error: null })

        try {
          const response = await fetch('/api/v1/companies/me', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch company profile')
          }

          const data = await response.json()

          set({
            company: data.data,
            profileLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch company profile',
            profileLoading: false
          })
        }
      },

      createCompanyProfile: async (data: Record<string, unknown>) => {
        set({ updateLoading: true, error: null })

        try {
          const response = await fetch('/api/v1/companies/me', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            throw new Error('Failed to create company profile')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to create company profile',
            updateLoading: false
          })
          throw error
        }
      },

      updateCompanyProfile: async (data: Record<string, unknown>) => {
        set({ updateLoading: true, error: null })

        try {
          const companyId = get().company?._id
          if (!companyId) {
            throw new Error('No company ID available')
          }

          const response = await fetch(`/api/v1/companies/${companyId}/profile`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            throw new Error('Failed to update company profile')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update company profile',
            updateLoading: false
          })
          throw error
        }
      },

      updateCompanyCulture: async (culture: Record<string, unknown>) => {
        set({ updateLoading: true, error: null })

        try {
          const companyId = get().company?._id
          if (!companyId) {
            throw new Error('No company ID available')
          }

          const response = await fetch(`/api/v1/companies/${companyId}/culture`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ culture })
          })

          if (!response.ok) {
            throw new Error('Failed to update company culture')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update company culture',
            updateLoading: false
          })
          throw error
        }
      },

      updateSocialLinks: async (socialLinks: Record<string, unknown>) => {
        set({ updateLoading: true, error: null })

        try {
          const companyId = get().company?._id
          if (!companyId) {
            throw new Error('No company ID available')
          }

          const response = await fetch(`/api/v1/companies/${companyId}/social`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ socialLinks })
          })

          if (!response.ok) {
            throw new Error('Failed to update social links')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update social links',
            updateLoading: false
          })
          throw error
        }
      },

      addLocation: async (location: Record<string, unknown>) => {
        set({ updateLoading: true, error: null })

        try {
          const companyId = get().company?._id
          if (!companyId) {
            throw new Error('No company ID available')
          }

          const response = await fetch(`/api/v1/companies/${companyId}/locations`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ location })
          })

          if (!response.ok) {
            throw new Error('Failed to add location')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to add location',
            updateLoading: false
          })
          throw error
        }
      },

      updateLocation: async (locationId: string, location: Record<string, unknown>) => {
        set({ updateLoading: true, error: null })

        try {
          const companyId = get().company?._id
          if (!companyId) {
            throw new Error('No company ID available')
          }

          const response = await fetch(`/api/v1/companies/${companyId}/locations/${locationId}`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ location })
          })

          if (!response.ok) {
            throw new Error('Failed to update location')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update location',
            updateLoading: false
          })
          throw error
        }
      },

      removeLocation: async (locationId: string) => {
        set({ updateLoading: true, error: null })

        try {
          const companyId = get().company?._id
          if (!companyId) {
            throw new Error('No company ID available')
          }

          const response = await fetch(`/api/v1/companies/${companyId}/locations/${locationId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to remove location')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to remove location',
            updateLoading: false
          })
          throw error
        }
      },

      // Team management
      fetchTeamMembers: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch team members')
          }

          const data = await response.json()
          
          set({ teamMembers: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch team members'
          })
        }
      },

      inviteTeamMember: async (email: string, role: string) => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team/invite`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, role })
          })

          if (!response.ok) {
            throw new Error('Failed to invite team member')
          }

          // Refresh team members
          await get().fetchTeamMembers()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to invite team member'
          })
        }
      },

      removeTeamMember: async (memberId: string) => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team/${memberId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to remove team member')
          }

          // Refresh team members
          await get().fetchTeamMembers()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to remove team member'
          })
        }
      },

      // Job management
      fetchCompanyJobs: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/jobs`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch company jobs')
          }

          const data = await response.json()
          
          set({ jobs: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch company jobs'
          })
        }
      },

      createJob: async (jobData: Record<string, unknown>) => {
        try {
          const response = await fetch('/api/v1/jobs', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(jobData)
          })

          if (!response.ok) {
            throw new Error('Failed to create job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to create job'
          })
        }
      },

      updateJob: async (jobId: string, jobData: Record<string, unknown>) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(jobData)
          })

          if (!response.ok) {
            throw new Error('Failed to update job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update job'
          })
        }
      },

      deleteJob: async (jobId: string) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to delete job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to delete job'
          })
        }
      },

      // Application management
      fetchApplications: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/applications`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch applications')
          }

          const data = await response.json()
          
          set({ applications: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch applications'
          })
        }
      },

      updateApplicationStatus: async (applicationId: string, status: string) => {
        try {
          const response = await fetch(`/api/v1/applications/${applicationId}/status`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status })
          })

          if (!response.ok) {
            throw new Error('Failed to update application status')
          }

          // Refresh applications
          await get().fetchApplications()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update application status'
          })
        }
      },

      // Settings management
      fetchCompanySettings: async () => {
        const { company } = get()
        if (!company?._id) {
          set({ settingsError: 'No company selected' })
          return
        }

        set({ settingsLoading: true, settingsError: null })

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/settings`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch company settings')
          }

          const data = await response.json()

          set({
            settings: data.data || data,
            settingsLoading: false
          })
        } catch (error) {
          set({
            settingsError: error instanceof Error ? error.message : 'Failed to fetch company settings',
            settingsLoading: false
          })
        }
      },

      updateCompanySettings: async (settingsUpdate: CompanySettingsUpdate) => {
        const { company } = get()
        if (!company?._id) {
          set({ settingsError: 'No company selected' })
          return
        }

        set({ settingsUpdateLoading: true, settingsError: null })

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/settings`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ settings: settingsUpdate })
          })

          if (!response.ok) {
            throw new Error('Failed to update company settings')
          }

          const data = await response.json()

          set({
            settings: data.data || data,
            settingsUpdateLoading: false
          })
        } catch (error) {
          set({
            settingsError: error instanceof Error ? error.message : 'Failed to update company settings',
            settingsUpdateLoading: false
          })
        }
      },

      resetCompanySettings: async () => {
        const { company } = get()
        if (!company?._id) {
          set({ settingsError: 'No company selected' })
          return
        }

        set({ settingsUpdateLoading: true, settingsError: null })

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/settings?action=reset`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to reset company settings')
          }

          const data = await response.json()

          set({
            settings: data.data || data,
            settingsUpdateLoading: false
          })
        } catch (error) {
          set({
            settingsError: error instanceof Error ? error.message : 'Failed to reset company settings',
            settingsUpdateLoading: false
          })
        }
      },

      updateSettingsSection: async (section: string, sectionData: Record<string, unknown>) => {
        const { company } = get()
        if (!company?._id) {
          set({ settingsError: 'No company selected' })
          return
        }

        set({ settingsUpdateLoading: true, settingsError: null })

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/settings?section=${section}`, {
            method: 'PATCH',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ settings: { [section]: sectionData } })
          })

          if (!response.ok) {
            throw new Error(`Failed to update ${section} settings`)
          }

          const data = await response.json()

          set({
            settings: data.data || data,
            settingsUpdateLoading: false
          })
        } catch (error) {
          set({
            settingsError: error instanceof Error ? error.message : `Failed to update ${section} settings`,
            settingsUpdateLoading: false
          })
        }
      },

      // Analytics
      fetchAnalytics: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/analytics`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch analytics')
          }

          const data = await response.json()
          
          set({ analytics: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch analytics'
          })
        }
      },

      // Utility
      setLoading: (loading: boolean) => set({ loading }),
      
      setError: (error: string | null) => set({ error }),
      
      clearError: () => set({ error: null }),

      clearSettingsError: () => set({ settingsError: null }),

      reset: () => set(initialState)
    }),
    {
      name: 'company-store',
      partialize: (state) => ({
        company: state.company
      })
    }
  )
)
