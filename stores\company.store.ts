import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type {
  CompanySettings,
  CompanySettingsUpdate
} from '@/types/company-settings.types'

interface Company {
  _id: string
  name: string
  slug: string
  description: string
  tagline?: string
  logo?: string
  coverImage?: string
  website?: string
  industry: string[]
  size: string
  founded?: number
  locations: Array<{
    city: string
    state?: string
    country: string
    isHeadquarters: boolean
    address?: string
    postalCode?: string
  }>
  contact: {
    email: string
    phone?: string
    address?: string
    supportEmail?: string
    hrEmail?: string
  }
  culture: {
    values?: string[]
    benefits?: string[]
    workEnvironment?: string
    diversity?: string
    mission?: string
    vision?: string
    perks?: string[]
  }
  socialLinks: {
    linkedin?: string
    twitter?: string
    facebook?: string
    instagram?: string
    github?: string
    youtube?: string
    glassdoor?: string
  }
  subscription: {
    plan: string
    status: string
    jobPostingLimit: number
    jobPostingsUsed: number
    featuredJobsLimit: number
    featuredJobsUsed: number
  }
  admins: string[]
  recruiters: string[]
  teamMembers: Array<{
    user: string
    role: string
    department?: string
    joinedAt: Date
    isActive: boolean
  }>
  stats: {
    totalJobs: number
    activeJobs: number
    totalApplications: number
    totalHires: number
    profileViews: number
    followerCount: number
  }
  verification: {
    isVerified: boolean
    verifiedAt?: Date
    documents?: string[]
    status?: string
  }
  settings?: CompanySettings
  isActive: boolean
  isFeatured: boolean
  createdAt: Date
  updatedAt: Date
}

interface CompanyState {
  // Current company data
  company: Company | null
  
  // Loading states
  loading: boolean
  profileLoading: boolean
  updateLoading: boolean
  settingsLoading: boolean
  settingsUpdateLoading: boolean

  // Error state
  error: string | null
  settingsError: string | null

  // Settings data
  settings: CompanySettings | null
  
  // Team members and jobs
  teamMembers: Array<Record<string, unknown>>
  jobs: Array<Record<string, unknown>>
  applications: Array<Record<string, unknown>>
  
  // Analytics data
  analytics: {
    profileViews: number[]
    applicationTrends: number[]
    jobPerformance: Array<Record<string, unknown>>
  }
}

interface CompanyActions {
  // Company profile management
  fetchCompanyProfile: () => Promise<void>
  updateCompanyProfile: (data: Record<string, unknown>) => Promise<void>
  uploadCompanyLogo: (file: File) => Promise<void>

  // Team management
  fetchTeamMembers: () => Promise<void>
  inviteTeamMember: (email: string, role: string) => Promise<void>
  removeTeamMember: (memberId: string) => Promise<void>

  // Job management
  fetchCompanyJobs: () => Promise<void>
  createJob: (jobData: Record<string, unknown>) => Promise<void>
  updateJob: (jobId: string, jobData: Record<string, unknown>) => Promise<void>
  deleteJob: (jobId: string) => Promise<void>

  // Application management
  fetchApplications: () => Promise<void>
  updateApplicationStatus: (applicationId: string, status: string) => Promise<void>

  // Settings management
  fetchCompanySettings: () => Promise<void>
  updateCompanySettings: (settings: CompanySettingsUpdate) => Promise<void>
  resetCompanySettings: () => Promise<void>
  updateSettingsSection: (section: string, data: Record<string, unknown>) => Promise<void>

  // Analytics
  fetchAnalytics: () => Promise<void>

  // Utility
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  clearSettingsError: () => void
  reset: () => void
}

type CompanyStore = CompanyState & CompanyActions

const initialState: CompanyState = {
  company: null,
  loading: false,
  profileLoading: false,
  updateLoading: false,
  settingsLoading: false,
  settingsUpdateLoading: false,
  error: null,
  settingsError: null,
  settings: null,
  teamMembers: [],
  jobs: [],
  applications: [],
  analytics: {
    profileViews: [],
    applicationTrends: [],
    jobPerformance: []
  }
}

export const useCompanyStore = create<CompanyStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Company profile management
      fetchCompanyProfile: async () => {
        set({ profileLoading: true, error: null })

        try {
          const response = await fetch('/api/v1/companies/me', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch company profile')
          }

          const data = await response.json()

          set({
            company: data.data,
            profileLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch company profile',
            profileLoading: false
          })
        }
      },



      updateCompanyProfile: async (data: Record<string, unknown>) => {
        set({ updateLoading: true, error: null })

        try {
          const response = await fetch('/api/v1/companies/me', {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          })

          if (!response.ok) {
            throw new Error('Failed to update company profile')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update company profile',
            updateLoading: false
          })
          throw error
        }
      },

      uploadCompanyLogo: async (file: File) => {
        set({ updateLoading: true, error: null })

        try {
          const formData = new FormData()
          formData.append('logo', file)

          const response = await fetch('/api/v1/companies/me/logo', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: formData
          })

          if (!response.ok) {
            throw new Error('Failed to upload company logo')
          }

          const result = await response.json()

          set({
            company: result.data,
            updateLoading: false
          })
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to upload company logo',
            updateLoading: false
          })
          throw error
        }
      },

      // Team management
      fetchTeamMembers: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch team members')
          }

          const data = await response.json()
          
          set({ teamMembers: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch team members'
          })
        }
      },

      inviteTeamMember: async (email: string, role: string) => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team/invite`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, role })
          })

          if (!response.ok) {
            throw new Error('Failed to invite team member')
          }

          // Refresh team members
          await get().fetchTeamMembers()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to invite team member'
          })
        }
      },

      removeTeamMember: async (memberId: string) => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/team/${memberId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to remove team member')
          }

          // Refresh team members
          await get().fetchTeamMembers()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to remove team member'
          })
        }
      },

      // Job management
      fetchCompanyJobs: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/jobs`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch company jobs')
          }

          const data = await response.json()
          
          set({ jobs: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch company jobs'
          })
        }
      },

      createJob: async (jobData: Record<string, unknown>) => {
        try {
          const response = await fetch('/api/v1/jobs', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(jobData)
          })

          if (!response.ok) {
            throw new Error('Failed to create job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to create job'
          })
        }
      },

      updateJob: async (jobId: string, jobData: Record<string, unknown>) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(jobData)
          })

          if (!response.ok) {
            throw new Error('Failed to update job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update job'
          })
        }
      },

      deleteJob: async (jobId: string) => {
        try {
          const response = await fetch(`/api/v1/jobs/${jobId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to delete job')
          }

          // Refresh jobs
          await get().fetchCompanyJobs()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to delete job'
          })
        }
      },

      // Application management
      fetchApplications: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/applications`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch applications')
          }

          const data = await response.json()
          
          set({ applications: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch applications'
          })
        }
      },

      updateApplicationStatus: async (applicationId: string, status: string) => {
        try {
          const response = await fetch(`/api/v1/applications/${applicationId}/status`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status })
          })

          if (!response.ok) {
            throw new Error('Failed to update application status')
          }

          // Refresh applications
          await get().fetchApplications()
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to update application status'
          })
        }
      },

      // Settings management
      fetchCompanySettings: async () => {
        const { company } = get()
        if (!company?._id) {
          set({ settingsError: 'No company selected' })
          return
        }

        set({ settingsLoading: true, settingsError: null })

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/settings`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch company settings')
          }

          const data = await response.json()

          set({
            settings: data.data || data,
            settingsLoading: false
          })
        } catch (error) {
          set({
            settingsError: error instanceof Error ? error.message : 'Failed to fetch company settings',
            settingsLoading: false
          })
        }
      },

      updateCompanySettings: async (settingsUpdate: CompanySettingsUpdate) => {
        const { company } = get()
        if (!company?._id) {
          set({ settingsError: 'No company selected' })
          return
        }

        set({ settingsUpdateLoading: true, settingsError: null })

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/settings`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ settings: settingsUpdate })
          })

          if (!response.ok) {
            throw new Error('Failed to update company settings')
          }

          const data = await response.json()

          set({
            settings: data.data || data,
            settingsUpdateLoading: false
          })
        } catch (error) {
          set({
            settingsError: error instanceof Error ? error.message : 'Failed to update company settings',
            settingsUpdateLoading: false
          })
        }
      },

      resetCompanySettings: async () => {
        const { company } = get()
        if (!company?._id) {
          set({ settingsError: 'No company selected' })
          return
        }

        set({ settingsUpdateLoading: true, settingsError: null })

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/settings?action=reset`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to reset company settings')
          }

          const data = await response.json()

          set({
            settings: data.data || data,
            settingsUpdateLoading: false
          })
        } catch (error) {
          set({
            settingsError: error instanceof Error ? error.message : 'Failed to reset company settings',
            settingsUpdateLoading: false
          })
        }
      },

      updateSettingsSection: async (section: string, sectionData: Record<string, unknown>) => {
        const { company } = get()
        if (!company?._id) {
          set({ settingsError: 'No company selected' })
          return
        }

        set({ settingsUpdateLoading: true, settingsError: null })

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/settings?section=${section}`, {
            method: 'PATCH',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ settings: { [section]: sectionData } })
          })

          if (!response.ok) {
            throw new Error(`Failed to update ${section} settings`)
          }

          const data = await response.json()

          set({
            settings: data.data || data,
            settingsUpdateLoading: false
          })
        } catch (error) {
          set({
            settingsError: error instanceof Error ? error.message : `Failed to update ${section} settings`,
            settingsUpdateLoading: false
          })
        }
      },

      // Analytics
      fetchAnalytics: async () => {
        const { company } = get()
        if (!company) return

        try {
          const response = await fetch(`/api/v1/companies/${company._id}/analytics`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error('Failed to fetch analytics')
          }

          const data = await response.json()
          
          set({ analytics: data.data })
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to fetch analytics'
          })
        }
      },

      // Utility
      setLoading: (loading: boolean) => set({ loading }),
      
      setError: (error: string | null) => set({ error }),
      
      clearError: () => set({ error: null }),

      clearSettingsError: () => set({ settingsError: null }),

      reset: () => set(initialState)
    }),
    {
      name: 'company-store',
      partialize: (state) => ({
        company: state.company
      })
    }
  )
)
