'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useJobsStore } from '@/stores'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { ButtonLoading } from '@/components/ui/button-loading'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  MapPin, 
  Filter,
  X,
  TrendingUp,
  Clock
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface JobSearchBarProps {
  className?: string
  showFilters?: boolean
  onFiltersToggle?: () => void
  variant?: 'default' | 'compact' | 'hero'
}

export function JobSearchBar({ 
  className,
  showFilters = true,
  onFiltersToggle,
  variant = 'default'
}: JobSearchBarProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { 
    searchQuery, 
    updateSearchQuery, 
    searchJobs, 
    searchLoading,
    searchMeta 
  } = useJobsStore()

  const [localQuery, setLocalQuery] = useState({
    q: searchQuery.q || '',
    location: searchQuery.location || ''
  })

  // Popular search terms
  const popularSearches = [
    'Software Engineer',
    'Product Manager',
    'Data Scientist',
    'UX Designer',
    'Marketing Manager',
    'Sales Representative'
  ]

  // Recent searches (would come from localStorage in real app)
  const recentSearches = [
    'Frontend Developer',
    'React Developer',
    'Full Stack Engineer'
  ]

  // Initialize from URL params
  useEffect(() => {
    const q = searchParams.get('q') || ''
    const location = searchParams.get('location') || ''
    
    if (q || location) {
      setLocalQuery({ q, location })
      updateSearchQuery({ q, location })
    }
  }, [searchParams, updateSearchQuery])

  // Handle search submission
  const handleSearch = async (e?: React.FormEvent) => {
    if (e) e.preventDefault()

    // Update URL
    const params = new URLSearchParams()
    if (localQuery.q) params.set('q', localQuery.q)
    if (localQuery.location) params.set('location', localQuery.location)
    
    const queryString = params.toString()
    const newUrl = queryString ? `/jobs?${queryString}` : '/jobs'
    
    // Update search query in store
    updateSearchQuery({
      ...searchQuery,
      q: localQuery.q,
      location: localQuery.location,
      page: 1 // Reset to first page
    })

    // Perform search
    try {
      await searchJobs({
        ...searchQuery,
        q: localQuery.q,
        location: localQuery.location,
        page: 1
      })
      
      // Navigate if not already on jobs page
      if (!window.location.pathname.includes('/jobs')) {
        router.push(newUrl)
      }
    } catch (error) {
      console.error('Search failed:', error)
    }
  }

  // Handle popular search click
  const handlePopularSearchClick = (term: string) => {
    setLocalQuery(prev => ({ ...prev, q: term }))
    updateSearchQuery({ ...searchQuery, q: term })
    handleSearch()
  }

  // Clear search
  const clearSearch = () => {
    setLocalQuery({ q: '', location: '' })
    updateSearchQuery({ ...searchQuery, q: '', location: '' })
  }

  const variants = {
    default: {
      container: 'p-6',
      form: 'flex flex-col md:flex-row gap-4',
      input: 'h-12'
    },
    compact: {
      container: 'p-4',
      form: 'flex flex-row gap-2',
      input: 'h-10'
    },
    hero: {
      container: 'p-8',
      form: 'flex flex-col md:flex-row gap-4',
      input: 'h-14 text-lg'
    }
  }

  const currentVariant = variants[variant]

  return (
    <Card className={cn('w-full shadow-lg', className)}>
      <CardContent className={currentVariant.container}>
        <form onSubmit={handleSearch} className={currentVariant.form}>
          {/* Job Title/Keywords Input */}
          <div className="flex-1 relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
            <Input
              placeholder="Job title, keywords, or company"
              value={localQuery.q}
              onChange={(e) => setLocalQuery(prev => ({ ...prev, q: e.target.value }))}
              className={cn('pl-12 pr-10', currentVariant.input)}
              disabled={searchLoading}
            />
            {localQuery.q && (
              <button
                type="button"
                onClick={() => setLocalQuery(prev => ({ ...prev, q: '' }))}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Location Input */}
          <div className="flex-1 relative">
            <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
            <Input
              placeholder="City, state, or remote"
              value={localQuery.location}
              onChange={(e) => setLocalQuery(prev => ({ ...prev, location: e.target.value }))}
              className={cn('pl-12 pr-10', currentVariant.input)}
              disabled={searchLoading}
            />
            {localQuery.location && (
              <button
                type="button"
                onClick={() => setLocalQuery(prev => ({ ...prev, location: '' }))}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>

          {/* Search Button */}
          <div className="flex gap-2">
            <ButtonLoading
              type="submit"
              loading={searchLoading}
              loadingText="Searching..."
              className={cn('px-8', currentVariant.input)}
              disabled={searchLoading}
            >
              <Search className="w-5 h-5 mr-2" />
              Search Jobs
            </ButtonLoading>

            {/* Filters Toggle */}
            {showFilters && onFiltersToggle && (
              <Button
                type="button"
                variant="outline"
                onClick={onFiltersToggle}
                className={currentVariant.input}
              >
                <Filter className="w-5 h-5" />
              </Button>
            )}
          </div>
        </form>

        {/* Search Results Meta */}
        {searchMeta.totalResults > 0 && (
          <div className="mt-4 flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center space-x-4">
              <span>
                {searchMeta.totalResults.toLocaleString()} jobs found
              </span>
              <div className="flex items-center space-x-1">
                <Clock className="w-3 h-3" />
                <span>Search took {searchMeta.searchTime}ms</span>
              </div>
            </div>
            
            {(localQuery.q || localQuery.location) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className="text-muted-foreground hover:text-foreground"
              >
                Clear search
              </Button>
            )}
          </div>
        )}

        {/* Popular Searches */}
        {variant !== 'compact' && !localQuery.q && !searchLoading && (
          <div className="mt-6 space-y-3">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">
                Popular Searches
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {popularSearches.map((term) => (
                <Badge
                  key={term}
                  variant="outline"
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                  onClick={() => handlePopularSearchClick(term)}
                >
                  {term}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Recent Searches */}
        {variant !== 'compact' && !localQuery.q && !searchLoading && recentSearches.length > 0 && (
          <div className="mt-4 space-y-3">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">
                Recent Searches
              </span>
            </div>
            <div className="flex flex-wrap gap-2">
              {recentSearches.map((term) => (
                <Badge
                  key={term}
                  variant="secondary"
                  className="cursor-pointer hover:bg-secondary/80 transition-colors"
                  onClick={() => handlePopularSearchClick(term)}
                >
                  {term}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
