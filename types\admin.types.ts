import { BaseDocument, ObjectId } from './base.types'
import { User } from './user.types'
import { Company } from './company.types'
import { Job } from './job.types'
import { Application } from './application.types'

export type AdminRole = 'super_admin' | 'admin' | 'moderator' | 'support'

export type ContentStatus = 'pending' | 'approved' | 'rejected' | 'flagged'

export type ReportType = 'spam' | 'inappropriate' | 'fake' | 'discrimination' | 'other'

export type ReportStatus = 'open' | 'investigating' | 'resolved' | 'dismissed'

export interface AdminUser extends User {
  adminRole: AdminRole
  permissions: string[]
  lastAdminLogin?: Date
  adminNotes?: string
}

export interface AdminDashboard {
  stats: {
    totalUsers: number
    totalCompanies: number
    totalJobs: number
    totalApplications: number
    activeUsers: number
    newUsersToday: number
    newCompaniesToday: number
    newJobsToday: number
    pendingReports: number
    pendingVerifications: number
  }
  charts: {
    userGrowth: { date: Date; count: number }[]
    jobPostings: { date: Date; count: number }[]
    applications: { date: Date; count: number }[]
    revenue: { date: Date; amount: number }[]
  }
  recentActivity: {
    type: 'user_registered' | 'company_created' | 'job_posted' | 'report_submitted'
    description: string
    timestamp: Date
    entityId: ObjectId
  }[]
  alerts: {
    type: 'warning' | 'error' | 'info'
    message: string
    count: number
    timestamp: Date
  }[]
}

export interface ContentModeration extends BaseDocument {
  type: 'user' | 'company' | 'job' | 'application' | 'review'
  entityId: ObjectId
  status: ContentStatus
  moderator?: ObjectId
  reason?: string
  notes?: string
  autoModerated: boolean
  flaggedContent?: {
    field: string
    content: string
    reason: string
  }[]
  reviewedAt?: Date
  approvedAt?: Date
  rejectedAt?: Date
}

export interface Report extends BaseDocument {
  type: ReportType
  status: ReportStatus
  reporter: ObjectId
  reportedEntity: {
    type: 'user' | 'company' | 'job' | 'application' | 'review'
    id: ObjectId
  }
  reason: string
  description: string
  evidence?: {
    screenshots: string[]
    urls: string[]
    additionalInfo: string
  }
  assignedTo?: ObjectId
  resolution?: {
    action: 'no_action' | 'warning' | 'content_removed' | 'account_suspended' | 'account_banned'
    reason: string
    notes: string
    resolvedBy: ObjectId
    resolvedAt: Date
  }
  priority: 'low' | 'medium' | 'high' | 'urgent'
  tags: string[]
}

export interface SystemSettings {
  general: {
    siteName: string
    siteDescription: string
    contactEmail: string
    supportEmail: string
    maintenanceMode: boolean
    maintenanceMessage?: string
  }
  features: {
    userRegistration: boolean
    companyRegistration: boolean
    jobPosting: boolean
    applications: boolean
    messaging: boolean
    notifications: boolean
    analytics: boolean
  }
  limits: {
    maxJobsPerCompany: number
    maxApplicationsPerUser: number
    maxFileSize: number
    maxFilesPerUpload: number
    rateLimitRequests: number
    rateLimitWindow: number
  }
  email: {
    provider: 'sendgrid' | 'mailgun' | 'ses'
    fromEmail: string
    fromName: string
    templates: {
      welcome: string
      verification: string
      passwordReset: string
      jobAlert: string
      applicationReceived: string
    }
  }
  storage: {
    provider: 'local' | 's3' | 'gcs' | 'azure'
    bucket?: string
    region?: string
    cdnUrl?: string
  }
  payment: {
    provider: 'stripe' | 'paypal'
    currency: string
    taxRate: number
    trialDays: number
  }
  seo: {
    metaTitle: string
    metaDescription: string
    keywords: string[]
    ogImage: string
    twitterCard: string
  }
}

export interface AuditLog extends BaseDocument {
  admin: ObjectId
  action: string
  entityType: string
  entityId?: ObjectId
  changes?: {
    field: string
    oldValue: any
    newValue: any
  }[]
  ipAddress: string
  userAgent: string
  metadata?: Record<string, any>
}

export interface AdminAnalytics {
  period: 'day' | 'week' | 'month' | 'quarter' | 'year'
  startDate: Date
  endDate: Date
  users: {
    total: number
    new: number
    active: number
    inactive: number
    byRole: { role: string; count: number }[]
    byLocation: { location: string; count: number }[]
    growth: { date: Date; count: number }[]
  }
  companies: {
    total: number
    new: number
    verified: number
    active: number
    bySize: { size: string; count: number }[]
    byIndustry: { industry: string; count: number }[]
    growth: { date: Date; count: number }[]
  }
  jobs: {
    total: number
    new: number
    active: number
    filled: number
    byCategory: { category: string; count: number }[]
    byType: { type: string; count: number }[]
    growth: { date: Date; count: number }[]
  }
  applications: {
    total: number
    new: number
    successful: number
    conversionRate: number
    byStatus: { status: string; count: number }[]
    growth: { date: Date; count: number }[]
  }
  revenue: {
    total: number
    recurring: number
    oneTime: number
    byPlan: { plan: string; revenue: number }[]
    growth: { date: Date; amount: number }[]
  }
  engagement: {
    dailyActiveUsers: number
    monthlyActiveUsers: number
    averageSessionDuration: number
    bounceRate: number
    pageViews: number
    topPages: { page: string; views: number }[]
  }
}

export interface UserManagementFilters {
  role?: string
  isActive?: boolean
  isEmailVerified?: boolean
  search?: string
  dateFrom?: string
  dateTo?: string
}

export interface CompanyVerificationFilters {
  verificationStatus?: 'pending' | 'verified' | 'rejected'
  search?: string
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical'
  uptime: number
  lastChecked: Date
  services: {
    database: {
      status: 'up' | 'down' | 'slow'
      responseTime: number
      connections: number
    }
    storage: {
      status: 'up' | 'down'
      usage: number
      capacity: number
    }
    email: {
      status: 'up' | 'down'
      queueSize: number
      deliveryRate: number
    }
    payment: {
      status: 'up' | 'down'
      successRate: number
    }
    search: {
      status: 'up' | 'down'
      responseTime: number
      indexSize: number
    }
  }
  errors: {
    count: number
    recent: {
      message: string
      timestamp: Date
      severity: 'low' | 'medium' | 'high'
    }[]
  }
  performance: {
    averageResponseTime: number
    requestsPerSecond: number
    errorRate: number
    memoryUsage: number
    cpuUsage: number
  }
}

export interface BulkAction {
  type: 'approve' | 'reject' | 'delete' | 'suspend' | 'activate' | 'verify'
  entityType: 'user' | 'company' | 'job' | 'application'
  entityIds: ObjectId[]
  reason?: string
  notes?: string
  performedBy: ObjectId
  performedAt: Date
  results: {
    success: number
    failed: number
    errors: { entityId: ObjectId; error: string }[]
  }
}

export interface AdminNotification {
  id: ObjectId
  type: 'system' | 'moderation' | 'security' | 'revenue'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  title: string
  message: string
  data?: Record<string, any>
  recipients: ObjectId[]
  readBy: { admin: ObjectId; readAt: Date }[]
  createdAt: Date
  expiresAt?: Date
}

export interface FeatureFlag {
  id: string
  name: string
  description: string
  enabled: boolean
  rolloutPercentage: number
  conditions?: {
    userRole?: string[]
    companySize?: string[]
    location?: string[]
    subscription?: string[]
  }
  createdAt: Date
  updatedAt: Date
  createdBy: ObjectId
}
