'use client'

import { useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Palette, Upload, Eye, Monitor, Sun, Moon } from 'lucide-react'
import type { CompanySettings } from '@/types/company-settings.types'

export function BrandingSettings() {
  const { register, watch, setValue } = useFormContext<CompanySettings>()
  const [colorPreview, setColorPreview] = useState({
    primary: watch('branding.primaryColor') || '#3b82f6',
    secondary: watch('branding.secondaryColor') || '#64748b'
  })

  const handleColorChange = (type: 'primary' | 'secondary', color: string) => {
    setColorPreview(prev => ({ ...prev, [type]: color }))
    setValue(`branding.${type}Color` as const, color)
  }

  const presetColors = [
    { name: 'Blue', primary: '#3b82f6', secondary: '#64748b' },
    { name: 'Green', primary: '#10b981', secondary: '#6b7280' },
    { name: 'Purple', primary: '#8b5cf6', secondary: '#6b7280' },
    { name: 'Red', primary: '#ef4444', secondary: '#6b7280' },
    { name: 'Orange', primary: '#f97316', secondary: '#6b7280' },
    { name: 'Pink', primary: '#ec4899', secondary: '#6b7280' },
    { name: 'Indigo', primary: '#6366f1', secondary: '#6b7280' },
    { name: 'Teal', primary: '#14b8a6', secondary: '#6b7280' }
  ]

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Color Scheme */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Color Scheme
            </CardTitle>
            <CardDescription>
              Customize your company's brand colors
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Primary Color */}
            <div className="space-y-2">
              <Label>Primary Color</Label>
              <div className="flex gap-2 items-center">
                <div
                  className="w-10 h-10 rounded-md border-2 border-gray-200"
                  style={{ backgroundColor: colorPreview.primary }}
                />
                <Input
                  type="color"
                  value={colorPreview.primary}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  className="w-20 h-10 p-1 border-0"
                />
                <Input
                  type="text"
                  value={colorPreview.primary}
                  onChange={(e) => handleColorChange('primary', e.target.value)}
                  placeholder="#3b82f6"
                  className="flex-1"
                />
              </div>
            </div>

            {/* Secondary Color */}
            <div className="space-y-2">
              <Label>Secondary Color</Label>
              <div className="flex gap-2 items-center">
                <div
                  className="w-10 h-10 rounded-md border-2 border-gray-200"
                  style={{ backgroundColor: colorPreview.secondary }}
                />
                <Input
                  type="color"
                  value={colorPreview.secondary}
                  onChange={(e) => handleColorChange('secondary', e.target.value)}
                  className="w-20 h-10 p-1 border-0"
                />
                <Input
                  type="text"
                  value={colorPreview.secondary}
                  onChange={(e) => handleColorChange('secondary', e.target.value)}
                  placeholder="#64748b"
                  className="flex-1"
                />
              </div>
            </div>

            <Separator />

            {/* Color Presets */}
            <div className="space-y-2">
              <Label>Color Presets</Label>
              <div className="grid grid-cols-4 gap-2">
                {presetColors.map((preset) => (
                  <Button
                    key={preset.name}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      handleColorChange('primary', preset.primary)
                      handleColorChange('secondary', preset.secondary)
                    }}
                    className="flex items-center gap-2 h-auto p-2"
                  >
                    <div className="flex gap-1">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: preset.primary }}
                      />
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: preset.secondary }}
                      />
                    </div>
                    <span className="text-xs">{preset.name}</span>
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Theme Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Theme Settings
            </CardTitle>
            <CardDescription>
              Configure the visual theme for your company profile
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Default Theme</Label>
              <Select
                value={watch('branding.customTheme')}
                onValueChange={(value) => setValue('branding.customTheme', value as 'light' | 'dark' | 'auto')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">
                    <div className="flex items-center gap-2">
                      <Sun className="h-4 w-4" />
                      Light
                    </div>
                  </SelectItem>
                  <SelectItem value="dark">
                    <div className="flex items-center gap-2">
                      <Moon className="h-4 w-4" />
                      Dark
                    </div>
                  </SelectItem>
                  <SelectItem value="auto">
                    <div className="flex items-center gap-2">
                      <Monitor className="h-4 w-4" />
                      Auto (System)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Theme Preview */}
            <div className="space-y-2">
              <Label>Preview</Label>
              <div className="border rounded-lg p-4 space-y-2">
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: colorPreview.primary }}
                  />
                  <span className="text-sm font-medium">Primary Color</span>
                </div>
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: colorPreview.secondary }}
                  />
                  <span className="text-sm font-medium">Secondary Color</span>
                </div>
                <Badge
                  style={{
                    backgroundColor: colorPreview.primary,
                    color: 'white'
                  }}
                >
                  Sample Badge
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Logo and Banner */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Logo and Banner
          </CardTitle>
          <CardDescription>
            Upload custom logo and banner images for your company profile
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Custom Logo */}
            <div className="space-y-4">
              <div>
                <Label>Custom Logo</Label>
                <p className="text-sm text-muted-foreground">
                  Upload a custom logo (recommended: 200x200px, PNG/JPG)
                </p>
              </div>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                {watch('branding.customLogo') ? (
                  <div className="space-y-2">
                    <img
                      src={watch('branding.customLogo')}
                      alt="Custom logo"
                      className="mx-auto h-16 w-16 object-contain"
                    />
                    <p className="text-sm text-muted-foreground">Current logo</p>
                    <Button variant="outline" size="sm">
                      Change Logo
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="mx-auto h-8 w-8 text-gray-400" />
                    <p className="text-sm text-muted-foreground">
                      Click to upload or drag and drop
                    </p>
                    <Button variant="outline" size="sm">
                      Upload Logo
                    </Button>
                  </div>
                )}
              </div>
              
              <Input
                type="url"
                placeholder="Or enter logo URL"
                {...register('branding.customLogo')}
              />
            </div>

            {/* Custom Banner */}
            <div className="space-y-4">
              <div>
                <Label>Custom Banner</Label>
                <p className="text-sm text-muted-foreground">
                  Upload a banner image (recommended: 1200x400px, PNG/JPG)
                </p>
              </div>
              
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                {watch('branding.customBanner') ? (
                  <div className="space-y-2">
                    <img
                      src={watch('branding.customBanner')}
                      alt="Custom banner"
                      className="mx-auto h-16 w-32 object-cover rounded"
                    />
                    <p className="text-sm text-muted-foreground">Current banner</p>
                    <Button variant="outline" size="sm">
                      Change Banner
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="mx-auto h-8 w-8 text-gray-400" />
                    <p className="text-sm text-muted-foreground">
                      Click to upload or drag and drop
                    </p>
                    <Button variant="outline" size="sm">
                      Upload Banner
                    </Button>
                  </div>
                )}
              </div>
              
              <Input
                type="url"
                placeholder="Or enter banner URL"
                {...register('branding.customBanner')}
              />
            </div>
          </div>

          {/* Preview Section */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              <Label>Profile Preview</Label>
            </div>
            <div className="border rounded-lg p-4 bg-gray-50">
              <p className="text-sm text-muted-foreground text-center">
                Company profile preview will appear here with your custom branding
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
