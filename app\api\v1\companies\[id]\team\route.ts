import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/middleware/auth.middleware'
import { connectDB } from '@/lib/database/connection'
import { Company } from '@/lib/models/company.model'
import { User } from '@/lib/models/user.model'

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/v1/companies/[id]/team - Get team members
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const companyId = params.id

    // Verify user has access to this company
    const user = await User.findById(userId)
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if user is admin or belongs to this company
    if (user.role !== 'admin' && user.companyId?.toString() !== companyId) {
      return NextResponse.json(
        { error: 'Access denied to this company' },
        { status: 403 }
      )
    }

    // Get company with team members
    const company = await Company.findById(companyId)
      .populate({
        path: 'teamMembers.user',
        select: 'profile.firstName profile.lastName email role isActive lastLogin createdAt'
      })
      .populate({
        path: 'admins',
        select: 'profile.firstName profile.lastName email role isActive lastLogin createdAt'
      })
      .populate({
        path: 'recruiters',
        select: 'profile.firstName profile.lastName email role isActive lastLogin createdAt'
      })

    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Format team members data
    const teamMembers = []

    // Add admins
    for (const admin of company.admins || []) {
      teamMembers.push({
        id: admin._id,
        user: {
          id: admin._id,
          name: `${admin.profile.firstName} ${admin.profile.lastName}`,
          email: admin.email,
          role: admin.role,
          isActive: admin.isActive,
          lastLogin: admin.lastLogin,
          joinedAt: admin.createdAt
        },
        role: 'company_admin',
        department: 'Administration',
        permissions: ['manage_company', 'manage_jobs', 'manage_applications', 'manage_team', 'view_analytics'],
        isActive: admin.isActive,
        joinedAt: admin.createdAt
      })
    }

    // Add recruiters
    for (const recruiter of company.recruiters || []) {
      teamMembers.push({
        id: recruiter._id,
        user: {
          id: recruiter._id,
          name: `${recruiter.profile.firstName} ${recruiter.profile.lastName}`,
          email: recruiter.email,
          role: recruiter.role,
          isActive: recruiter.isActive,
          lastLogin: recruiter.lastLogin,
          joinedAt: recruiter.createdAt
        },
        role: 'recruiter',
        department: 'Human Resources',
        permissions: ['manage_jobs', 'manage_applications', 'view_analytics'],
        isActive: recruiter.isActive,
        joinedAt: recruiter.createdAt
      })
    }

    // Add other team members
    for (const member of company.teamMembers || []) {
      if (member.user && member.isActive) {
        teamMembers.push({
          id: member.user._id,
          user: {
            id: member.user._id,
            name: `${member.user.profile.firstName} ${member.user.profile.lastName}`,
            email: member.user.email,
            role: member.user.role,
            isActive: member.user.isActive,
            lastLogin: member.user.lastLogin,
            joinedAt: member.user.createdAt
          },
          role: member.role,
          department: member.department || 'General',
          permissions: [],
          isActive: member.isActive,
          joinedAt: member.joinedAt
        })
      }
    }

    // Remove duplicates (in case someone is both admin and team member)
    const uniqueTeamMembers = teamMembers.filter((member, index, self) => 
      index === self.findIndex(m => m.id.toString() === member.id.toString())
    )

    return NextResponse.json({
      success: true,
      data: {
        companyId,
        totalMembers: uniqueTeamMembers.length,
        activeMembers: uniqueTeamMembers.filter(m => m.isActive).length,
        teamMembers: uniqueTeamMembers
      }
    })

  } catch (error) {
    console.error('Get team members error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    )
  }
}

// POST /api/v1/companies/[id]/team - Add team member
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    await connectDB()
    
    // Authenticate user
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const userId = authResult.user.id
    const companyId = params.id
    const { email, role, department } = await request.json()

    // Validate input
    if (!email || !role) {
      return NextResponse.json(
        { error: 'Email and role are required' },
        { status: 400 }
      )
    }

    const validRoles = ['recruiter', 'hr_manager', 'team_member']
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be one of: ' + validRoles.join(', ') },
        { status: 400 }
      )
    }

    // Verify user has admin access to this company
    const currentUser = await User.findById(userId)
    if (!currentUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    if (currentUser.role !== 'admin' && 
        (currentUser.companyId?.toString() !== companyId || currentUser.role !== 'company_admin')) {
      return NextResponse.json(
        { error: 'Access denied. Only company admins can add team members' },
        { status: 403 }
      )
    }

    // Find the user to add
    const userToAdd = await User.findOne({ email })
    if (!userToAdd) {
      return NextResponse.json(
        { error: 'User with this email not found' },
        { status: 404 }
      )
    }

    // Check if user is already part of another company
    if (userToAdd.companyId && userToAdd.companyId.toString() !== companyId) {
      return NextResponse.json(
        { error: 'User is already associated with another company' },
        { status: 400 }
      )
    }

    // Get company
    const company = await Company.findById(companyId)
    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Check if user is already a team member
    const isAlreadyMember = company.teamMembers?.some(
      member => member.user.toString() === userToAdd._id.toString()
    ) || company.admins?.includes(userToAdd._id) || 
        company.recruiters?.includes(userToAdd._id)

    if (isAlreadyMember) {
      return NextResponse.json(
        { error: 'User is already a team member' },
        { status: 400 }
      )
    }

    // Update user's company and role
    await User.findByIdAndUpdate(userToAdd._id, {
      companyId,
      role: role === 'team_member' ? 'job_seeker' : role // Keep original role for team members
    })

    // Add to appropriate company array
    if (role === 'recruiter') {
      await Company.findByIdAndUpdate(companyId, {
        $addToSet: { recruiters: userToAdd._id }
      })
    } else {
      // Add to team members
      await Company.findByIdAndUpdate(companyId, {
        $addToSet: {
          teamMembers: {
            user: userToAdd._id,
            role,
            department: department || 'General',
            joinedAt: new Date(),
            isActive: true
          }
        }
      })
    }

    console.log(`User ${userToAdd.email} added to company ${companyId} as ${role} by ${userId}`)

    return NextResponse.json({
      success: true,
      message: `Successfully added ${userToAdd.email} as ${role}`,
      data: {
        id: userToAdd._id,
        user: {
          id: userToAdd._id,
          name: `${userToAdd.profile.firstName} ${userToAdd.profile.lastName}`,
          email: userToAdd.email,
          role: userToAdd.role
        },
        role,
        department: department || 'General',
        joinedAt: new Date(),
        isActive: true
      }
    })

  } catch (error) {
    console.error('Add team member error:', error)
    return NextResponse.json(
      { error: 'Failed to add team member' },
      { status: 500 }
    )
  }
}
